#!/bin/bash

# Script để setup Future Architecture (bỏ qua chat module)
# Combines Future Matching Engine Service + Module Separation (no chat)
# Usage: ./setup-future-architecture.sh [phase]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_phase() {
    echo -e "${PURPLE}🚀 PHASE $1: $2${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Check if we're in the right directory
check_directory() {
    if [ ! -f "pom.xml" ] || [ ! -d "future-core" ]; then
        print_error "Script phải chạy từ root directory của cex-be project"
        exit 1
    fi
    print_success "Đang chạy từ root directory: $(pwd)"
}

# Display architecture overview
show_architecture_overview() {
    print_header "Future Architecture Overview (No Chat)"

    echo "Target Architecture:"
    echo ""
    echo "📚 future-core/        → Library (business logic)"
    echo "🌐 future-api/         → API Service (REST, WebSocket, Notifications)"
    echo "⚡ future/             → Matching Engine Service"
    echo "📊 market/ (enhanced)  → Unified Market Data (spot + futures)"
    echo "⚙️  admin/ (enhanced)   → System Management + Schedulers"
    echo "💰 wallet/ (enhanced)  → Unified Wallet Operations"
    echo ""
    echo "Key Changes:"
    echo "  ❌ Bỏ qua chat module enhancement"
    echo "  ✅ Notifications integrated trong future-api"
    echo "  ✅ Simplified 6-service architecture"
    echo "  ✅ Reduced inter-service dependencies"
    echo ""
    echo "Benefits:"
    echo "  ✅ Consistency với exchange architecture"
    echo "  ✅ Optimal performance với dedicated matching engine"
    echo "  ✅ Clear separation of concerns theo module"
    echo "  ✅ Simplified notification management"
    echo ""
}

# Phase 1: Setup Future-API Structure với Notifications
phase1_setup_future_api() {
    print_phase "1" "Setup Future-API Structure với Notifications"

    # Create future-api structure
    mkdir -p future-api/src/main/java/com/icetea/lotus/{controller,websocket,messaging,notification,security,config}
    mkdir -p future-api/src/main/resources
    print_success "Created future-api directory structure"

    # Create main application class
    cat > future-api/src/main/java/com/icetea/lotus/FutureApiApplication.java << 'EOF'
package com.icetea.lotus;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Future Trading API Application
 * Main microservice cho futures trading API với integrated notifications
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableAsync
@OpenAPIDefinition(
    info = @Info(
        title = "Future Trading API",
        version = "1.0",
        description = "API cho giao dịch hợp đồng tương lai với notification support"
    )
)
public class FutureApiApplication {
    public static void main(String[] args) {
        SpringApplication.run(FutureApiApplication.class, args);
    }
}
EOF

    # Create notification services
    cat > future-api/src/main/java/com/icetea/lotus/notification/NotificationService.java << 'EOF'
package com.icetea.lotus.notification;

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * Notification Service
 * Handles all trading-related notifications trong future-api
 */
@Service
@Slf4j
public class NotificationService {

    // TODO: Implement notification logic:
    // - Trading notifications
    // - Position update notifications
    // - Liquidation alerts
    // - Margin call notifications

    public void sendTradingNotification(String userId, String message) {
        log.info("Sending trading notification to user {}: {}", userId, message);
        // TODO: Implement notification sending
    }

    public void sendLiquidationAlert(String userId, String symbol) {
        log.warn("Sending liquidation alert to user {} for symbol {}", userId, symbol);
        // TODO: Implement liquidation alert
    }
}
EOF

    cat > future-api/src/main/java/com/icetea/lotus/notification/EmailService.java << 'EOF'
package com.icetea.lotus.notification;

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * Email Service
 * Handles email notifications cho trading events
 */
@Service
@Slf4j
public class EmailService {

    public void sendTradingEmail(String email, String subject, String content) {
        log.info("Sending email to {}: {}", email, subject);
        // TODO: Implement email sending logic
    }

    public void sendLiquidationEmail(String email, String symbol, String details) {
        log.warn("Sending liquidation email to {} for symbol {}", email, symbol);
        // TODO: Implement liquidation email
    }
}
EOF

    cat > future-api/src/main/java/com/icetea/lotus/notification/SMSService.java << 'EOF'
package com.icetea.lotus.notification;

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * SMS Service
 * Handles SMS notifications cho critical trading events
 */
@Service
@Slf4j
public class SMSService {

    public void sendTradingSMS(String phoneNumber, String message) {
        log.info("Sending SMS to {}: {}", phoneNumber, message);
        // TODO: Implement SMS sending logic
    }

    public void sendLiquidationSMS(String phoneNumber, String symbol) {
        log.warn("Sending liquidation SMS to {} for symbol {}", phoneNumber, symbol);
        // TODO: Implement liquidation SMS
    }
}
EOF

    # Create WebSocket notification handler
    cat > future-api/src/main/java/com/icetea/lotus/websocket/NotificationWebSocketHandler.java << 'EOF'
package com.icetea.lotus.websocket;

import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

/**
 * Notification WebSocket Handler
 * Handles real-time notification streaming
 */
@Component
@Slf4j
public class NotificationWebSocketHandler {

    public void broadcastTradingAlert(String userId, String message) {
        log.info("Broadcasting trading alert to user {}: {}", userId, message);
        // TODO: Implement WebSocket broadcasting
    }

    public void broadcastLiquidationAlert(String userId, String symbol) {
        log.warn("Broadcasting liquidation alert to user {} for symbol {}", userId, symbol);
        // TODO: Implement liquidation alert broadcasting
    }
}
EOF

    # Create placeholder controllers
    cat > future-api/src/main/java/com/icetea/lotus/controller/TradingController.java << 'EOF'
package com.icetea.lotus.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;

/**
 * Trading Controller
 * REST API endpoints cho futures trading
 */
@RestController
@RequestMapping("/api/v1/trading")
@Slf4j
public class TradingController {

    // TODO: Implement trading endpoints từ future-core
    // - POST /orders
    // - GET /orders
    // - DELETE /orders/{id}

    public void placeholder() {
        log.info("TradingController placeholder - implement từ future-core");
    }
}
EOF

    cat > future-api/src/main/java/com/icetea/lotus/controller/PositionController.java << 'EOF'
package com.icetea.lotus.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;

/**
 * Position Controller
 * REST API endpoints cho position management
 */
@RestController
@RequestMapping("/api/v1/positions")
@Slf4j
public class PositionController {

    // TODO: Implement position endpoints từ future-core
    // - GET /positions
    // - GET /positions/{symbol}
    // - POST /positions/close

    public void placeholder() {
        log.info("PositionController placeholder - implement từ future-core");
    }
}
EOF

    # Create basic application configuration
    cat > future-api/src/main/resources/application.yml << 'EOF'
spring:
  application:
    name: future-api
  profiles:
    active: dev

server:
  port: 8080
  servlet:
    context-path: /future

# Service Discovery
spring:
  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30500}
      discovery:
        enabled: true
        service-name: future-api
        health-check-path: /future/actuator/health

# Notification Configuration
notification:
  email:
    enabled: true
  sms:
    enabled: true
  websocket:
    enabled: true

# Logging
logging:
  level:
    com.icetea.lotus: DEBUG
EOF

    print_success "Phase 1 completed: Future-API structure với notifications ready"
}

# Phase 2: Create Future Matching Engine Service
phase2_create_future_service() {
    print_phase "2" "Create Future Matching Engine Service"

    # Create future service structure
    mkdir -p future/src/main/java/com/icetea/lotus/{matching/distributed,service,messaging,config}
    mkdir -p future/src/main/resources
    print_success "Created future service directory structure"

    # Create main application class
    cat > future/src/main/java/com/icetea/lotus/FutureApplication.java << 'EOF'
package com.icetea.lotus;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Future Matching Engine Service
 * Dedicated service cho futures order matching và trade execution
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableAsync
@EnableKafka
public class FutureApplication {
    public static void main(String[] args) {
        SpringApplication.run(FutureApplication.class, args);
    }
}
EOF

    # Create placeholder matching engine
    cat > future/src/main/java/com/icetea/lotus/matching/distributed/DistributedLockingMatchingEngineService.java << 'EOF'
package com.icetea.lotus.matching.distributed;

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * Distributed Locking Matching Engine Service
 * Primary matching engine using Redis distributed locking
 */
@Service
@Slf4j
public class DistributedLockingMatchingEngineService {

    // TODO: Implement DistributedLockingMatchingEngine từ future-core:
    // - processOrder(Order order)
    // - matchOrders(Symbol symbol)
    // - updateOrderBook(Symbol symbol, Order order)
    // - Redis distributed locking logic
    // - Order book snapshot recovery

    public void placeholder() {
        log.info("DistributedLockingMatchingEngineService placeholder - primary engine");
    }
}
EOF

    # Create placeholder services
    cat > future/src/main/java/com/icetea/lotus/service/OrderProcessingService.java << 'EOF'
package com.icetea.lotus.service;

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * Order Processing Service
 * Handles order processing trong matching engine
 */
@Service
@Slf4j
public class OrderProcessingService {

    // TODO: Implement order processing logic từ future-core:
    // - processOrder(Order order)
    // - validateOrder(Order order)
    // - routeToDistributedLockingEngine(Order order)

    public void placeholder() {
        log.info("OrderProcessingService placeholder - implement từ future-core");
    }
}
EOF

    cat > future/src/main/java/com/icetea/lotus/service/TradeExecutionService.java << 'EOF'
package com.icetea.lotus.service;

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * Trade Execution Service
 * Handles trade execution và settlement
 */
@Service
@Slf4j
public class TradeExecutionService {

    // TODO: Implement trade execution logic từ future-core:
    // - executeTrade(Trade trade)
    // - settleTrade(Trade trade)
    // - updatePositions(Trade trade)

    public void placeholder() {
        log.info("TradeExecutionService placeholder - implement từ future-core");
    }
}
EOF

    # Create messaging components
    cat > future/src/main/java/com/icetea/lotus/messaging/OrderCommandConsumer.java << 'EOF'
package com.icetea.lotus.messaging;

import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

/**
 * Order Command Consumer
 * Consumes order commands từ future-api
 */
@Component
@Slf4j
public class OrderCommandConsumer {

    @KafkaListener(topics = "future-order-commands")
    public void handleOrderCommand(String orderCommand) {
        log.info("Received order command: {}", orderCommand);
        // TODO: Implement order command processing
    }
}
EOF

    # Create application configuration
    cat > future/src/main/resources/application.yml << 'EOF'
spring:
  application:
    name: future
  profiles:
    active: dev

server:
  port: 8081

# Service Discovery
spring:
  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30500}
      discovery:
        enabled: true
        service-name: future
        health-check-path: /actuator/health

# Kafka Configuration
spring:
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    consumer:
      group-id: future-matching-engine

# Matching Engine Configuration
matching-engine:
  primary-engine: DistributedLockingMatchingEngine
  backup-engine: OptimizedMatchingEngine
  use-lock-free: false  # Disabled
  redis-locking:
    enabled: true
    timeout: 5000ms
    retry-attempts: 3
  order-book-snapshot:
    enabled: true
    interval: 300000  # 5 minutes
    redis-key-prefix: "future:orderbook:"

# Logging
logging:
  level:
    com.icetea.lotus: DEBUG
    com.icetea.lotus.matching: DEBUG
EOF

    print_success "Phase 2 completed: Future matching engine service ready"
}

# Phase 3: Extract Market Data Services
phase3_extract_market_data() {
    print_phase "3" "Extract Market Data Services"

    # Enhance market service structure
    mkdir -p market/src/main/java/com/icetea/lotus/service/futures
    mkdir -p market/src/main/java/com/icetea/lotus/websocket/futures
    mkdir -p market/src/main/java/com/icetea/lotus/cache/futures
    mkdir -p market/src/main/java/com/icetea/lotus/controller/futures
    print_success "Enhanced market service structure"

    # Create futures market data service
    cat > market/src/main/java/com/icetea/lotus/service/futures/FuturesMarketDataService.java << 'EOF'
package com.icetea.lotus.service.futures;

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * Futures Market Data Service
 * Handles futures-specific market data operations
 */
@Service
@Slf4j
public class FuturesMarketDataService {

    // TODO: Implement market data logic từ future-core:
    // - getLastPrice(Symbol symbol)
    // - getMarkPrice(Symbol symbol)
    // - getIndexPrice(Symbol symbol)
    // - updateLastPrice(Symbol symbol, Money price)

    public void placeholder() {
        log.info("FuturesMarketDataService placeholder - implement từ future-core");
    }
}
EOF

    print_success "Phase 3 completed: Market data services structure ready"
}

# Phase 4: Extract Background Schedulers
phase4_extract_schedulers() {
    print_phase "4" "Extract Background Schedulers"

    # Enhance admin service structure
    mkdir -p admin/src/main/java/com/icetea/lotus/service/scheduler
    mkdir -p admin/src/main/java/com/icetea/lotus/monitoring
    print_success "Enhanced admin service structure"

    # Create futures scheduler service
    cat > admin/src/main/java/com/icetea/lotus/service/scheduler/FuturesSchedulerService.java << 'EOF'
package com.icetea.lotus.service.scheduler;

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * Futures Scheduler Service
 * Handles all futures-related background scheduling
 */
@Service
@Slf4j
public class FuturesSchedulerService {

    // TODO: Implement schedulers từ future-core:
    // - LiquidationScheduler
    // - FundingScheduler
    // - SettlementScheduler
    // - HealthCheckScheduler
    // - ADLScheduler

    public void placeholder() {
        log.info("FuturesSchedulerService placeholder - implement từ future-core");
    }
}
EOF

    print_success "Phase 4 completed: Admin service enhanced với scheduler support"
}

# Phase 5: Extract Wallet Operations
phase5_extract_wallet_operations() {
    print_phase "5" "Extract Wallet Operations"

    # Enhance wallet service structure
    mkdir -p wallet/src/main/java/com/icetea/lotus/service/futures
    mkdir -p wallet/src/main/java/com/icetea/lotus/risk
    print_success "Enhanced wallet service structure"

    # Create futures wallet service
    cat > wallet/src/main/java/com/icetea/lotus/service/futures/FuturesWalletService.java << 'EOF'
package com.icetea.lotus.service.futures;

import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

/**
 * Futures Wallet Service
 * Handles futures-specific wallet operations
 */
@Service
@Slf4j
public class FuturesWalletService {

    // TODO: Implement wallet operations từ future-core:
    // - Futures balance management
    // - Margin calculations
    // - Futures transaction recording
    // - Risk management cho margin trading

    public void placeholder() {
        log.info("FuturesWalletService placeholder - implement từ future-core");
    }
}
EOF

    print_success "Phase 5 completed: Wallet service enhanced với futures support"
}

# Update root pom.xml
update_root_pom() {
    print_header "Updating Root POM.xml"

    # Check and add future module if not exists
    if ! grep -q "<module>future</module>" pom.xml; then
        print_info "Adding future module to root pom.xml..."
        sed -i '/<module>future-api<\/module>/a\        <module>future</module>' pom.xml
        print_success "Added future module to root pom.xml"
    else
        print_info "Future module already exists in root pom.xml"
    fi
}

# Verify setup
verify_setup() {
    print_header "Verifying Setup"

    # Check all services
    services=("future-api" "future" "market" "admin" "wallet")

    for service in "${services[@]}"; do
        if [ -d "$service" ]; then
            print_success "$service service exists"
        else
            print_error "$service service missing"
        fi
    done

    # Check future-core
    if [ -d "future-core" ]; then
        print_success "future-core exists (will become library)"
    else
        print_error "future-core missing"
    fi

    # Check notification services trong future-api
    if [ -f "future-api/src/main/java/com/icetea/lotus/notification/NotificationService.java" ]; then
        print_success "Notification services created trong future-api"
    else
        print_error "Notification services missing trong future-api"
    fi
}

# Generate summary
generate_summary() {
    print_header "Setup Summary"

    echo "🎉 Future Architecture Setup Completed (No Chat)!"
    echo ""
    echo "Services Created/Enhanced:"
    echo "  📚 future-core        → Will become library"
    echo "  🌐 future-api         → API service với integrated notifications"
    echo "  ⚡ future             → Matching engine service"
    echo "  📊 market (enhanced)  → Futures market data support"
    echo "  ⚙️  admin (enhanced)   → Futures schedulers support"
    echo "  💰 wallet (enhanced)  → Futures wallet operations support"
    echo ""
    echo "Key Features:"
    echo "  ✅ Notifications integrated trong future-api"
    echo "  ✅ Simplified 6-service architecture"
    echo "  ✅ No chat module dependencies"
    echo "  ✅ Clear service boundaries"
    echo ""
    echo "Next Steps:"
    echo "  1. 📝 Review generated structure"
    echo "  2. 🔧 Implement business logic trong placeholder files"
    echo "  3. 🔗 Setup service-to-service communication"
    echo "  4. 📨 Configure Kafka topics"
    echo "  5. 🧪 Implement integration testing"
    echo ""
    print_success "Ready to implement future trading architecture!"
}

# Main execution function
main() {
    local phase=${1:-"all"}

    echo "🚀 Setting up Future Architecture (No Chat)..."
    echo "=============================================="

    check_directory
    echo ""

    show_architecture_overview
    echo ""

    case $phase in
        "1"|"phase1")
            phase1_setup_future_api
            ;;
        "2"|"phase2")
            phase2_create_future_service
            ;;
        "3"|"phase3")
            phase3_extract_market_data
            ;;
        "4"|"phase4")
            phase4_extract_schedulers
            ;;
        "5"|"phase5")
            phase5_extract_wallet_operations
            ;;
        "all"|*)
            phase1_setup_future_api
            echo ""
            phase2_create_future_service
            echo ""
            phase3_extract_market_data
            echo ""
            phase4_extract_schedulers
            echo ""
            phase5_extract_wallet_operations
            echo ""
            update_root_pom
            echo ""
            verify_setup
            echo ""
            generate_summary
            ;;
    esac

    echo ""
    echo "=============================================="
    echo "Setup completed for phase: $phase"

    if [ "$phase" = "all" ]; then
        print_info "To run individual phases: ./setup-future-architecture.sh [1-5]"
    fi
}

# Show usage if help requested
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    echo "Usage: $0 [phase]"
    echo ""
    echo "Phases:"
    echo "  1, phase1  - Setup Future-API structure với notifications"
    echo "  2, phase2  - Create Future Matching Engine service"
    echo "  3, phase3  - Extract Market Data services"
    echo "  4, phase4  - Extract Background Schedulers"
    echo "  5, phase5  - Extract Wallet Operations"
    echo "  all        - Run all phases (default)"
    echo ""
    echo "Examples:"
    echo "  $0           # Run all phases"
    echo "  $0 1         # Run only phase 1"
    echo "  $0 phase3    # Run only phase 3"
    exit 0
fi

# Run main function
main "$1"
