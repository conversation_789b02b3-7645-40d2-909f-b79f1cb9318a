-- <PERSON><PERSON><PERSON> để thêm bảng contract_adl_record vào cơ sở dữ liệu

-- Tạo bảng contract_adl_record nếu chưa tồn tại
CREATE TABLE IF NOT EXISTS contract_adl_record (
    id VARCHAR(255) PRIMARY KEY,
    position_id VARCHAR(255) NOT NULL,
    member_id VARCHAR(255) NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    direction VARCHAR(10) NOT NULL,
    volume DECIMAL(18,8) NOT NULL,
    price DECIMAL(18,8) NOT NULL,
    time TIMESTAMP NOT NULL
);

-- Tạo chỉ mục cho bảng contract_adl_record
CREATE INDEX IF NOT EXISTS idx_adl_record_position_id ON contract_adl_record (position_id);
CREATE INDEX IF NOT EXISTS idx_adl_record_member_id ON contract_adl_record (member_id);
CREATE INDEX IF NOT EXISTS idx_adl_record_symbol ON contract_adl_record (symbol);
CREATE INDEX IF NOT EXISTS idx_adl_record_time ON contract_adl_record (time);

-- <PERSON><PERSON><PERSON> bảo extension uuid-ossp đã được cài đặt
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Thêm dữ liệu mẫu cho bảng contract_adl_record
INSERT INTO contract_adl_record (id, position_id, member_id, symbol, direction, volume, price, time)
VALUES
('ADL-' || uuid_generate_v4(), '1', '1', 'BTCUSDT', 'LONG', 0.1, 50000.00000000, NOW() - INTERVAL '2 HOUR'),
('ADL-' || uuid_generate_v4(), '2', '2', 'ETHUSDT', 'SHORT', 1.0, 3000.00000000, NOW() - INTERVAL '3 HOUR'),
('ADL-' || uuid_generate_v4(), '3', '3', 'BTCUSDT', 'SHORT', 0.2, 49000.00000000, NOW() - INTERVAL '4 HOUR');

-- Thêm comment cho bảng và các cột
COMMENT ON TABLE contract_adl_record IS 'Bảng lưu trữ các bản ghi ADL (Auto-Deleveraging)';
COMMENT ON COLUMN contract_adl_record.id IS 'ID của bản ghi ADL';
COMMENT ON COLUMN contract_adl_record.position_id IS 'ID của vị thế';
COMMENT ON COLUMN contract_adl_record.member_id IS 'ID của thành viên';
COMMENT ON COLUMN contract_adl_record.symbol IS 'Symbol của hợp đồng';
COMMENT ON COLUMN contract_adl_record.direction IS 'Hướng của vị thế: LONG, SHORT';
COMMENT ON COLUMN contract_adl_record.volume IS 'Khối lượng của vị thế';
COMMENT ON COLUMN contract_adl_record.price IS 'Giá của vị thế';
COMMENT ON COLUMN contract_adl_record.time IS 'Thời gian ADL';
