package com.icetea.lotus.walletrpc.controller;

import java.math.BigDecimal;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import com.icetea.lotus.walletrpc.service.AccountService;
import com.icetea.lotus.walletrpc.util.ClientUtils;
import com.icetea.lotus.walletrpc.util.MessageResult;

import io.bytom.api.Account;
import io.bytom.api.Balance;
import io.bytom.api.Block;
import io.bytom.api.Receiver;
import io.bytom.exception.BytomException;
import io.bytom.http.Client;

@RestController
@RequestMapping("/rpc")
public class WalletController {

    @Value("${bytom.api.url}")
    private String coreUrl;

    @Value("${client.access.token}")
    private String accessToken;

    @Value("${bytom.alias}")
    private String bytomAlias;

    @Value("${bytom.password}")
    private String bytomPassword;

    @Autowired
    private AccountService accountService;

    private final Logger logger = LoggerFactory.getLogger(WalletController.class);

    @GetMapping("address/{account}")
    public MessageResult getNewAddress(@PathVariable String account) {
        logger.info("create new address: {}", account);
        try {
            Client client = ClientUtils.generateClient(coreUrl, accessToken);
            Account bytomAccount;
            Account.Items accounts = new Account.QueryBuilder().list(client);
            MessageResult result = new MessageResult(0, "success");
            if (!accounts.data.isEmpty()) {
                bytomAccount = accounts.data.get(0);
                // Create an address
                Receiver receiver = new Account.ReceiverBuilder()
                        .setAccountId(bytomAccount.id)
                        .create(client);
                accountService.saveOne(account, receiver.address);
                result.setData(receiver.address);
            }
            return result;
        } catch (BytomException e) {
            e.printStackTrace();
            return MessageResult.error(500, "error:" + e.getMessage());
        }
    }

    /**
     * Total wallet balance (non-node service, directly return 0, and then if there is free, you can add the overall balance later)
     *
     * @return MessageResult
     */
    @GetMapping("balance")
    public MessageResult balance() {
        Client client;
        try {
            client = ClientUtils.generateClient(coreUrl, accessToken);
            Balance.Items bItems = new Balance.QueryBuilder().list(client);
            BigDecimal total = BigDecimal.ZERO;
            for (int m = 0; m < bItems.data.size(); m++) {
                if (bItems.data.get(m).assetId.equalsIgnoreCase("ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff")) {
                    total = total.add(BigDecimal.valueOf(bItems.data.get(m).amount));
                }
            }
            MessageResult result = new MessageResult(0, "success");
            result.setData(total);
            return result;
        } catch (BytomException e) {
            e.printStackTrace();
            MessageResult result = new MessageResult(0, "success");
            result.setData(0);
            return result;
        }
    }

    /**
     * Get the address balance
     *
     * @param address address
     * @return MessageResult
     */
    @GetMapping("balance/{address}")
    public MessageResult balance(@PathVariable String address) {
        return MessageResult.error(500, "Bytom does not support a single address to query the balance");
    }

    @GetMapping("height")
    public MessageResult getHeight() {
        Client client;
        try {
            client = ClientUtils.generateClient(coreUrl, accessToken);
            int blockCount = Block.getBlockCount(client);
            MessageResult result = new MessageResult(0, "success");
            result.setData(blockCount);
            return result;
        } catch (BytomException e) {
            e.printStackTrace();
            return MessageResult.error(500, "error:" + e.getMessage());
        }
    }

    /**
     * TODO Add transfer function
     *
     * @param address address
     * @param amount  amount
     * @param fee     fee
     * @return MessageResult
     */
    @GetMapping({"transfer", "withdraw"})
    public MessageResult withdraw(String address, BigDecimal amount, BigDecimal fee) {
        return MessageResult.error(500, "This feature has not yet been implemented");
    }
}
