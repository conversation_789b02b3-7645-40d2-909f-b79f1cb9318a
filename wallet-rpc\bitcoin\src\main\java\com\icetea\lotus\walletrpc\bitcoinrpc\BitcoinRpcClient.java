package com.icetea.lotus.walletrpc.bitcoinrpc;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Bitcoin JSON-RPC client implementation
 */
@Slf4j
public class BitcoinRpcClient {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL);

    private final URI serverURI;
    private final String username;
    private final String password;
    private final String walletName;
    private final CloseableHttpClient httpClient;

    /**
     * Constructs a Bitcoin JSON-RPC client with the given server URI
     *
     * @param serverURI the URI to the Bitcoin RPC server (e.g. http://localhost:8332/)
     * @throws MalformedURLException if the URI is malformed
     */
    public BitcoinRpcClient(String serverURI) throws MalformedURLException {
        URL url = new URL(serverURI);
        this.username = url.getUserInfo() != null ? url.getUserInfo().split(":")[0] : null;
        this.password = url.getUserInfo() != null && url.getUserInfo().split(":").length > 1 ? url.getUserInfo().split(":")[1] : null;

        // Check if the path contains a wallet name
        String path = url.getPath();
        if (path != null && path.startsWith("/wallet/")) {
            this.walletName = path.substring("/wallet/".length());
        } else {
            this.walletName = null;
        }

        try {
            // Create URI without user info for HttpClient
            this.serverURI = new URI(
                    url.getProtocol(),
                    null, // No user info
                    url.getHost(),
                    url.getPort(),
                    walletName != null ? "/wallet/" + walletName : "/",
                    null,
                    null);
        } catch (URISyntaxException e) {
            throw new MalformedURLException("Invalid server URI: " + e.getMessage());
        }

        // Set up HTTP client with basic auth
        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        if (username != null && password != null) {
            credentialsProvider.setCredentials(
                    new AuthScope(url.getHost(), url.getPort()),
                    new UsernamePasswordCredentials(username, password));
        }

        this.httpClient = HttpClientBuilder.create()
                .setDefaultCredentialsProvider(credentialsProvider)
                .build();
    }

    /**
     * Makes a JSON-RPC request to the Bitcoin server
     *
     * @param method the RPC method name
     * @param params the parameters to the method
     * @return the response from the server
     * @throws BitcoinRpcException if there's an error making the request or processing the response
     */
    private <T> T makeRequest(String method, Object[] params, Class<T> responseType) throws BitcoinRpcException {
        try {
            HttpPost request = new HttpPost(serverURI);
            request.setHeader("Content-Type", "application/json");

            JsonRpcRequest rpcRequest = new JsonRpcRequest();
            rpcRequest.setJsonrpc("1.0");
            rpcRequest.setId(System.currentTimeMillis());
            rpcRequest.setMethod(method);
            rpcRequest.setParams(params);

            String requestBody = OBJECT_MAPPER.writeValueAsString(rpcRequest);
            request.setEntity(new StringEntity(requestBody));

            HttpResponse response = httpClient.execute(request);
            HttpEntity entity = response.getEntity();

            if (entity != null) {
                String responseBody = EntityUtils.toString(entity);
                JsonRpcResponse rpcResponse = OBJECT_MAPPER.readValue(responseBody, JsonRpcResponse.class);

                if (rpcResponse.getError() != null) {
                    throw new BitcoinRpcException("Bitcoin RPC error: " + rpcResponse.getError().getMessage());
                }

                return OBJECT_MAPPER.convertValue(rpcResponse.getResult(), responseType);
            } else {
                throw new BitcoinRpcException("Empty response from server");
            }
        } catch (IOException e) {
            throw new BitcoinRpcException("Error making RPC request: " + e.getMessage(), e);
        }
    }

    /**
     * Gets the current block count
     *
     * @return the current block count
     * @throws BitcoinRpcException if there's an error making the request
     */
    public int getBlockCount() throws BitcoinRpcException {
        return makeRequest("getblockcount", new Object[]{}, Integer.class);
    }

    /**
     * Gets a new address for the given account
     *
     * @param account the account name
     * @return the new address
     * @throws BitcoinRpcException if there's an error making the request
     */
    public String getNewAddress(String account) throws BitcoinRpcException {
        return makeRequest("getnewaddress", new Object[]{account}, String.class);
    }

    /**
     * Gets the balance for the current wallet
     *
     * @return the balance
     * @throws BitcoinRpcException if there's an error making the request
     */
    public BigDecimal getBalance() throws BitcoinRpcException {
        return makeRequest("getbalance", new Object[]{}, BigDecimal.class);
    }

    /**
     * Gets the block hash for the given block height
     *
     * @param height the block height
     * @return the block hash
     * @throws BitcoinRpcException if there's an error making the request
     */
    public String getBlockHash(int height) throws BitcoinRpcException {
        return makeRequest("getblockhash", new Object[]{height}, String.class);
    }

    /**
     * Gets the block for the given block hash
     *
     * @param hash the block hash
     * @return the block
     * @throws BitcoinRpcException if there's an error making the request
     */
    public Block getBlock(String hash) throws BitcoinRpcException {
        return makeRequest("getblock", new Object[]{hash}, Block.class);
    }

    /**
     * Gets the raw transaction for the given transaction ID
     *
     * @param txid the transaction ID
     * @return the raw transaction
     * @throws BitcoinRpcException if there's an error making the request
     */
    public RawTransaction getRawTransaction(String txid) throws BitcoinRpcException {
        return makeRequest("getrawtransaction", new Object[]{txid, true}, RawTransaction.class);
    }

    /**
     * Sends Bitcoin to the given address
     *
     * @param address the address to send to
     * @param amount the amount to send
     * @return the transaction ID
     * @throws BitcoinRpcException if there's an error making the request
     */
    public String sendToAddress(String address, BigDecimal amount) throws BitcoinRpcException {
        return makeRequest("sendtoaddress", new Object[]{address, amount}, String.class);
    }

    /**
     * Creates a raw transaction
     *
     * @param inputs the transaction inputs
     * @param outputs the transaction outputs
     * @return the raw transaction hex
     * @throws BitcoinRpcException if there's an error making the request
     */
    public String createRawTransaction(List<Map<String, Object>> inputs, Map<String, BigDecimal> outputs) throws BitcoinRpcException {
        return makeRequest("createrawtransaction", new Object[]{inputs, outputs}, String.class);
    }

    /**
     * Signs a raw transaction
     *
     * @param hexString the raw transaction hex
     * @return the signed transaction
     * @throws BitcoinRpcException if there's an error making the request
     */
    public SignedTransaction signRawTransaction(String hexString) throws BitcoinRpcException {
        return makeRequest("signrawtransactionwithwallet", new Object[]{hexString}, SignedTransaction.class);
    }

    /**
     * Sends a raw transaction
     *
     * @param hexString the raw transaction hex
     * @return the transaction ID
     * @throws BitcoinRpcException if there's an error making the request
     */
    public String sendRawTransaction(String hexString) throws BitcoinRpcException {
        return makeRequest("sendrawtransaction", new Object[]{hexString}, String.class);
    }

    /**
     * Creates a new wallet
     *
     * @param walletName the name of the wallet to create
     * @param disablePrivateKeys if true, the wallet will be created without private keys
     * @param blank if true, the wallet will be created without an HD seed
     * @param passphrase the passphrase to encrypt the wallet with (null for no encryption)
     * @param avoidReuse if true, the wallet will avoid reusing addresses
     * @return information about the created wallet
     * @throws BitcoinRpcException if there's an error making the request
     */
    public Map<String, Object> createWallet(String walletName, boolean disablePrivateKeys, boolean blank, 
                                           String passphrase, boolean avoidReuse) throws BitcoinRpcException {
        Object[] params = {walletName, disablePrivateKeys, blank, passphrase, avoidReuse};
        return makeRequest("createwallet", params, Map.class);
    }

    /**
     * Creates a new wallet with default settings
     *
     * @param walletName the name of the wallet to create
     * @return information about the created wallet
     * @throws BitcoinRpcException if there's an error making the request
     */
    public Map<String, Object> createWallet(String walletName) throws BitcoinRpcException {
        return createWallet(walletName, false, false, null, false);
    }

    /**
     * Lists unspent transaction outputs
     *
     * @param minConf minimum confirmations
     * @param maxConf maximum confirmations
     * @param addresses addresses to filter
     * @return list of unspent transaction outputs
     * @throws BitcoinRpcException if there's an error making the request
     */
    public List<Unspent> listUnspent(int minConf, int maxConf, List<String> addresses) throws BitcoinRpcException {
        return Arrays.asList(makeRequest("listunspent", new Object[]{minConf, maxConf, addresses}, Unspent[].class));
    }

    /**
     * Represents a Bitcoin block
     */
    @Data
    public static class Block {
        private String hash;
        private int confirmations;
        private int size;
        private int height;
        private int version;
        private String merkleroot;
        private List<String> tx;
        private long time;
        private long nonce;
        private String bits;
        private double difficulty;
        private String previousblockhash;
        private String nextblockhash;

        public List<String> tx() {
            return tx;
        }

        public int height() {
            return height;
        }
    }

    /**
     * Represents a Bitcoin raw transaction
     */
    @Data
    public static class RawTransaction {
        private String txid;
        private String hash;
        private int version;
        private int size;
        private int vsize;
        private int weight;
        private int locktime;
        private List<Vin> vin;
        private List<Vout> vout;
        private String hex;
        private String blockhash;
        private int confirmations;
        private long time;
        private long blocktime;

        public String txId() {
            return txid;
        }

        public String blockHash() {
            return blockhash;
        }

        public long time() {
            return time;
        }

        public List<Vout> vOut() {
            return vout;
        }

        /**
         * Represents a transaction input
         */
        @Data
        public static class Vin {
            private String txid;
            private int vout;
            private ScriptSig scriptSig;
            private long sequence;
            private List<String> txinwitness;

            /**
             * Represents a script signature
             */
            @Data
            public static class ScriptSig {
                private String asm;
                private String hex;
            }
        }

        /**
         * Represents a transaction output
         */
        @Data
        public static class Vout {
            private BigDecimal value;
            private int n;
            private ScriptPubKey scriptPubKey;

            public BigDecimal value() {
                return value;
            }

            public ScriptPubKey scriptPubKey() {
                return scriptPubKey;
            }

            /**
             * Represents a script public key
             */
            @Data
            public static class ScriptPubKey {
                private String asm;
                private String hex;
                private int reqSigs;
                private String type;
                private List<String> addresses;

                public List<String> addresses() {
                    return addresses != null ? addresses : new ArrayList<>();
                }
            }
        }
    }

    /**
     * Represents an unspent transaction output
     */
    @Data
    public static class Unspent {
        private String txid;
        private int vout;
        private String address;
        private String label;
        private String scriptPubKey;
        private BigDecimal amount;
        private int confirmations;
        private boolean spendable;
        private boolean solvable;
        private boolean safe;
    }

    /**
     * Represents a signed transaction
     */
    @Data
    public static class SignedTransaction {
        private String hex;
        private boolean complete;
    }

    /**
     * Represents a JSON-RPC request
     */
    @Data
    private static class JsonRpcRequest {
        private String jsonrpc;
        private Object id;
        private String method;
        private Object[] params;
    }

    /**
     * Represents a JSON-RPC response
     */
    @Data
    private static class JsonRpcResponse {
        private String jsonrpc;
        private Object id;
        private Object result;
        private JsonRpcError error;

        /**
         * Represents a JSON-RPC error
         */
        @Data
        public static class JsonRpcError {
            private int code;
            private String message;
        }
    }
}
