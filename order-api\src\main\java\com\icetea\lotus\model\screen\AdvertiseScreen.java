package com.icetea.lotus.model.screen;

import com.icetea.lotus.ability.ScreenAbility;
import com.icetea.lotus.constant.AdvertiseControlStatus;
import com.icetea.lotus.entity.QAdvertise;
import com.querydsl.core.types.dsl.BooleanExpression;

import lombok.Data;

import java.util.ArrayList;

@Data
public class AdvertiseScreen implements ScreenAbility {

    AdvertiseControlStatus status;

    /**
     * Handle internal assertions
     *
     * @return
     */
    @Override
    public ArrayList<BooleanExpression> getBooleanExpressions() {
        ArrayList<BooleanExpression> booleanExpressions = new ArrayList<>();
        if (status != null) {
            booleanExpressions.add(QAdvertise.advertise.status.eq(status));
        }
        return booleanExpressions;
    }


}
