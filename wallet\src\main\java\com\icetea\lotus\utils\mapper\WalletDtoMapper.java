package com.icetea.lotus.utils.mapper;

import com.icetea.lotus.dto.WalletDto;
import com.icetea.lotus.entity.MemberWallet;
import com.icetea.lotus.entity.WalletFuture;
import com.icetea.lotus.utils.Money;
import org.springframework.stereotype.Component;

/**
 * Mapper cho Wallet và WalletDto
 */
@Component
public class WalletDtoMapper {

    /**
     * Chuyển đổi từ Wallet sang WalletDto
     * @param wallet Wallet
     * @return WalletDto
     */
    public WalletDto toDto(WalletFuture wallet) {
        if (wallet == null) {
            return null;
        }

        return WalletDto.builder()
                .id(wallet.getId())
                .memberId(wallet.getMemberId())
                .coin(wallet.getCoin())
                .balance(wallet.getBalance())
                .frozenBalance(wallet.getFrozenBalance())
                .availableBalance(wallet.getAvailableBalance())
                .unrealizedPnl(wallet.getUnrealizedPnl())
                .realizedPnl(wallet.getRealizedPnl())
                .usedMargin(wallet.getUsedMargin())
                .totalFee(wallet.getTotalFee())
                .totalFundingFee(wallet.getTotalFundingFee())
                .isLock(wallet.isLocked())
                .createTime(wallet.getCreateTime())
                .updateTime(wallet.getUpdateTime())
                .build();
    }

    /**
     * Chuyển đổi từ WalletDto sang Wallet
     * @param walletDto WalletDto
     * @return Wallet
     */
    public WalletFuture toEntity(WalletDto walletDto) {
        if (walletDto == null) {
            return null;
        }

        return WalletFuture.builder()
                .id(null)
                .memberId(walletDto.getMemberId())
                .coin(walletDto.getCoin())
                .balance(Money.of(walletDto.getBalance()).getValue())
                .frozenBalance(Money.of(walletDto.getFrozenBalance()).getValue())
                .availableBalance(Money.of(walletDto.getAvailableBalance()).getValue())
                .unrealizedPnl(Money.of(walletDto.getUnrealizedPnl()).getValue())
                .realizedPnl(Money.of(walletDto.getRealizedPnl()).getValue())
                .usedMargin(Money.of(walletDto.getUsedMargin()).getValue())
                .totalFee(Money.of(walletDto.getTotalFee()).getValue())
                .totalFundingFee(Money.of(walletDto.getTotalFundingFee()).getValue())
                .isLocked(walletDto.getIsLock())
                .createTime(walletDto.getCreateTime())
                .updateTime(walletDto.getUpdateTime())
                .build();
    }
}
