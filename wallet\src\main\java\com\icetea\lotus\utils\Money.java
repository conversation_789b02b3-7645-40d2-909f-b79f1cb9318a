package com.icetea.lotus.utils;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Value object đại diện cho tiền tệ
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Money implements Serializable, Comparable<Money> {
    private static final long serialVersionUID = 1L;

    private BigDecimal value;



    public static final Money ZERO = new Money(BigDecimal.ZERO);

    public static Money of(BigDecimal value) {
        if (value == null) {
            return ZERO;
        }
        // Đảm bảo độ chính xác không vượt quá 8 chữ số thập phân
        return new Money(value.setScale(8, RoundingMode.HALF_UP));
    }

    public static Money of(String value) {
        if (value == null || value.isEmpty()) {
            return ZERO;
        }
        try {
            // Đ<PERSON><PERSON> bảo độ chính xác không vượt quá 8 chữ số thập phân
            return new Money(new BigDecimal(value).setScale(8, RoundingMode.HALF_UP));
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Không thể chuyển đổi chuỗi thành BigDecimal: " + value, e);
        }
    }

    public static Money of(double value) {
        try {
            // Đảm bảo độ chính xác không vượt quá 8 chữ số thập phân
            return new Money(BigDecimal.valueOf(value).setScale(8, RoundingMode.HALF_UP));
        } catch (Exception e) {
            throw new IllegalArgumentException("Không thể chuyển đổi double thành BigDecimal: " + value, e);
        }
    }

    public Money add(Money other) {
        return new Money(this.value.add(other.value).setScale(8, RoundingMode.HALF_UP));
    }

    public Money subtract(Money other) {
        return new Money(this.value.subtract(other.value).setScale(8, RoundingMode.HALF_UP));
    }

    public Money multiply(BigDecimal multiplier) {
        return new Money(this.value.multiply(multiplier).setScale(8, RoundingMode.HALF_UP));
    }

    public Money divide(BigDecimal divisor, int scale, RoundingMode roundingMode) {
        if (divisor == null || divisor.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("Không thể chia cho null hoặc 0");
        }
        // Đảm bảo scale không vượt quá 8
        int actualScale = Math.min(scale, 8);
        return new Money(this.value.divide(divisor, actualScale, roundingMode));
    }

    /**
     * Chia cho một giá trị Money khác
     * @param divisor Giá trị chia
     * @return Kết quả phép chia
     */
    public Money divide(Money divisor) {
        if (divisor == null || divisor.isZero()) {
            throw new IllegalArgumentException("Không thể chia cho null hoặc 0");
        }
        return new Money(this.value.divide(divisor.getValue(), 8, RoundingMode.HALF_UP));
    }

    public boolean isGreaterThan(Money other) {
        if (other == null) {
            return true; // Bất kỳ giá trị nào cũng lớn hơn null
        }
        return this.value.compareTo(other.value) > 0;
    }

    public boolean isLessThan(Money other) {
        if (other == null) {
            return false; // Bất kỳ giá trị nào cũng không nhỏ hơn null
        }
        return this.value.compareTo(other.value) < 0;
    }

    public boolean isZero() {
        return this.value.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * Kiểm tra xem giá trị có lớn hơn hoặc bằng giá trị khác không
     * @param other Giá trị khác
     * @return true nếu lớn hơn hoặc bằng
     */
    public boolean isGreaterThanOrEqual(Money other) {
        if (other == null) {
            return true; // Bất kỳ giá trị nào cũng lớn hơn hoặc bằng null
        }
        return this.value.compareTo(other.value) >= 0;
    }

    /**
     * Kiểm tra xem giá trị có nhỏ hơn hoặc bằng giá trị khác không
     * @param other Giá trị khác
     * @return true nếu nhỏ hơn hoặc bằng
     */
    public boolean isLessThanOrEqual(Money other) {
        if (other == null) {
            return false; // Bất kỳ giá trị nào cũng không nhỏ hơn hoặc bằng null
        }
        return this.value.compareTo(other.value) <= 0;
    }

    /**
     * Lấy giá trị âm
     * @return Giá trị âm
     */
    public Money negate() {
        return new Money(this.value.negate().setScale(8, RoundingMode.HALF_UP));
    }

    @Override
    public String toString() {
        return value.toPlainString();
    }

    /**
     * So sánh hai giá trị tiền tệ
     * @param other Giá trị tiền tệ khác
     * @return -1 nếu nhỏ hơn, 0 nếu bằng, 1 nếu lớn hơn
     */
    @Override
    public int compareTo(Money other) {
        if (other == null) {
            return 1; // Bất kỳ giá trị nào cũng lớn hơn null
        }
        return this.value.compareTo(other.value);
    }
}
