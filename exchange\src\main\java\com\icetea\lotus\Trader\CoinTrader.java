package com.icetea.lotus.Trader;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.entity.ExchangeCoinPublishType;
import com.icetea.lotus.entity.ExchangeOrder;
import com.icetea.lotus.entity.ExchangeOrderDirection;
import com.icetea.lotus.entity.ExchangeOrderType;
import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.MergeOrder;
import com.icetea.lotus.entity.TradePlate;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@Getter
@Setter
public class CoinTrader {
    private final String exchangeOrderCompleteTopic = "exchange-order-completed";
    private final String exchangeTradeTopic = "exchange-trade";
    private final String exchangeTradePlateTopic = "exchange-trade-plate";
    private String symbol;
    private KafkaTemplate<String, String> kafkaTemplate;
    // Accuracy of trading currency
    private int coinScale = 4;
    // The accuracy of the base coin
    private int baseCoinScale = 4;
    private Logger logger = LoggerFactory.getLogger(CoinTrader.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    // Buy limit orders linked a list, price arrangement from high to low
    private TreeMap<BigDecimal, MergeOrder> buyLimitPriceQueue;
    // Sell limit orders linked list, price ranking from low to high
    private TreeMap<BigDecimal, MergeOrder> sellLimitPriceQueue;
    // Buy the market order list, sorted from small to large by time
    private LinkedList<ExchangeOrder> buyMarketQueue;
    // Sell market orders linked list, sorted from small to large by time
    private LinkedList<ExchangeOrder> sellMarketQueue;
    // Selling order information
    private TradePlate sellTradePlate;
    // Buying placing information
    private TradePlate buyTradePlate;
    // Whether to suspend transactions
    private boolean tradingHalt = false;
    private boolean ready = false;
    // Transaction pair information
    private ExchangeCoinPublishType publishType;
    private String clearTime;
    private SimpleDateFormat dateTimeFormat;


    public CoinTrader(String symbol) {
        this.symbol = symbol;
        initialize();
    }

    /**
     * Initialize the transaction thread
     */
    public void initialize() {
        logger.info("init CoinTrader for symbol {}", symbol);
        // Pay order queue price ranking
        buyLimitPriceQueue = new TreeMap<>(Comparator.reverseOrder());
        // Sell order queue price ranking
        this.sellLimitPriceQueue = new TreeMap<>(Comparator.naturalOrder());
        this.buyMarketQueue = new LinkedList<>();
        this.sellMarketQueue = new LinkedList<>();
        this.sellTradePlate = new TradePlate(symbol, ExchangeOrderDirection.SELL);
        this.buyTradePlate = new TradePlate(symbol, ExchangeOrderDirection.BUY);
        this.dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    }

    /**
     * Increase limit orders to queues, buy orders are ranked from price high to low, sell orders are ranked from price low to high
     *
     * @param exchangeOrder
     */
    public void addLimitPriceOrder(ExchangeOrder exchangeOrder) {
        if (exchangeOrder.getType() != ExchangeOrderType.LIMIT_PRICE) {
            return;
        }
        // logger.info("addLimitPriceOrder,orderId = {}", exchangeOrder.getOrderId());
        TreeMap<BigDecimal, MergeOrder> list;
        if (exchangeOrder.getDirection() == ExchangeOrderDirection.BUY) {
            list = buyLimitPriceQueue;
            buyTradePlate.add(exchangeOrder);
            if (ready) {
                sendTradePlateMessage(buyTradePlate);
            }
        } else {
            list = sellLimitPriceQueue;
            sellTradePlate.add(exchangeOrder);
            if (ready) {
                sendTradePlateMessage(sellTradePlate);
            }
        }
        synchronized (list) {
            MergeOrder mergeOrder = list.get(exchangeOrder.getPrice());
            if (mergeOrder == null) {
                mergeOrder = new MergeOrder();
                mergeOrder.add(exchangeOrder);
                list.put(exchangeOrder.getPrice(), mergeOrder);
            } else {
                mergeOrder.add(exchangeOrder);
            }
        }
    }

    public void addMarketPriceOrder(ExchangeOrder exchangeOrder) {
        if (exchangeOrder.getType() != ExchangeOrderType.MARKET_PRICE) {
            return;
        }
        logger.info("addMarketPriceOrder,orderId = {}", exchangeOrder.getOrderId());
        LinkedList<ExchangeOrder> list = exchangeOrder.getDirection() == ExchangeOrderDirection.BUY ? buyMarketQueue : sellMarketQueue;
        synchronized (list) {
            list.addLast(exchangeOrder);
        }
    }

    public void trade(List<ExchangeOrder> orders) throws ParseException {
        if (tradingHalt) {
            return;
        }
        for (ExchangeOrder order : orders) {
            trade(order);
        }
    }


    /**
     * Orders entered actively, if the transaction is not completed, they will be entered into the queue.
     *
     * @param exchangeOrder
     * @throws ParseException
     */
    public void trade(ExchangeOrder exchangeOrder) throws ParseException {
        if (tradingHalt) {
            return;
        }
        logger.info("trade order={}", exchangeOrder);
        if (!symbol.equalsIgnoreCase(exchangeOrder.getSymbol())) {
            logger.info("unsupported symbol,coin={},base={}", exchangeOrder.getCoinSymbol(), exchangeOrder.getBaseSymbol());
            return;
        }
        if (exchangeOrder.getAmount().compareTo(BigDecimal.ZERO) <= 0 || exchangeOrder.getAmount().subtract(exchangeOrder.getTradedAmount()).compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        TreeMap<BigDecimal, MergeOrder> limitPriceOrderList;
        LinkedList<ExchangeOrder> marketPriceOrderList;
        if (exchangeOrder.getDirection() == ExchangeOrderDirection.BUY) {
            limitPriceOrderList = sellLimitPriceQueue;
            marketPriceOrderList = sellMarketQueue;
        } else {
            limitPriceOrderList = buyLimitPriceQueue;
            marketPriceOrderList = buyMarketQueue;
        }
        if (exchangeOrder.getType() == ExchangeOrderType.MARKET_PRICE) {
            // logger.info(">>>>>>Market order>>>Trading and limit order trading");
            // Trading with limit orders
            matchMarketPriceWithLPList(limitPriceOrderList, exchangeOrder);
        } else if (exchangeOrder.getType() == ExchangeOrderType.LIMIT_PRICE) {
            // The price limit order must be greater than 0
            if (exchangeOrder.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                return;
            }
            // No special treatment is required for rush purchases, and you can just make a transaction directly, but it cannot be traded at the market price. Filtration control is performed on Exchange-api
            // Only the allocation mode requires special treatment (under the allocation mode, customers first pay a lot, and then the administrator issues the sell orders and allocates according to the proportion)
            if (publishType == ExchangeCoinPublishType.FENTAN
                    && exchangeOrder.getDirection() == ExchangeOrderDirection.SELL) {
                logger.info(">>>>>Sales Sale Order>>>Start Processing");
                // Selling orders only in the end time and liquidation time require special treatment
                if (exchangeOrder.getTime() < dateTimeFormat.parse(clearTime).getTime()) {
                    logger.info(">>>>>>Sales Sale Order>>>In the end time and liquidation time");
                    // Share the sell orders and the activity sell orders are displayed on the front end without pressing them into the sell orders.
                    matchLimitPriceWithLPListByFENTAN(limitPriceOrderList, exchangeOrder, false);
                    return;
                }
            }
            // logger.info(">>>>> Limit order>>> Trading and limit order trading");
            // Trading with limit orders first
            matchLimitPriceWithLPList(limitPriceOrderList, exchangeOrder, false);
            if (exchangeOrder.getAmount().compareTo(exchangeOrder.getTradedAmount()) > 0) {
                // logger.info(">>>>>The limit order has not been traded>>>>Trade with market order>>>>");
                // After trading with the market order
                matchLimitPriceWithMPList(marketPriceOrderList, exchangeOrder);
            }
        }
    }

    /**
     * Transaction processing rules under the sharing and snap-up mode
     *
     * @param lpList
     * @param focusedOrder
     * @param canEnterList
     */

    public void matchLimitPriceWithLPListByFENTAN(TreeMap<BigDecimal, MergeOrder> lpList, ExchangeOrder focusedOrder, boolean canEnterList) {
        List<ExchangeTrade> exchangeTrades = new ArrayList<>();
        List<ExchangeOrder> completedOrders = new ArrayList<>();
        synchronized (lpList) {
            Iterator<Map.Entry<BigDecimal, MergeOrder>> mergeOrderIterator = lpList.entrySet().iterator();
            boolean exitLoop = false;
            while (!exitLoop && mergeOrderIterator.hasNext()) {
                Map.Entry<BigDecimal, MergeOrder> entry = mergeOrderIterator.next();
                MergeOrder mergeOrder = entry.getValue();
                Iterator<ExchangeOrder> orderIterator = mergeOrder.iterator();
                // The price to match for the purchase order shall not be greater than the entrustment price, otherwise the exit will be
                if (focusedOrder.getDirection() == ExchangeOrderDirection.BUY && mergeOrder.getPrice().compareTo(focusedOrder.getPrice()) > 0) {
                    break;
                }
                // The price to match for the sell order is not less than the entrustment price, otherwise the exit will be
                if (focusedOrder.getDirection() == ExchangeOrderDirection.SELL && mergeOrder.getPrice().compareTo(focusedOrder.getPrice()) < 0) {
                    break;
                }
                BigDecimal totalAmount = mergeOrder.getTotalAmount();
                while (orderIterator.hasNext()) {
                    ExchangeOrder matchOrder = orderIterator.next();
                    // Processing matches
                    ExchangeTrade trade = processMatchByFENTAN(focusedOrder, matchOrder, totalAmount);
                    exchangeTrades.add(trade);
                    // Determine whether the matching order is completed
                    if (matchOrder.isCompleted()) {
                        // The current matching order completes the transaction, delete the order
                        orderIterator.remove();
                        completedOrders.add(matchOrder);
                    }
                    // Determine whether the transaction order is completed
                    if (focusedOrder.isCompleted()) {
                        // Transaction completed
                        completedOrders.add(focusedOrder);
                        // Exit the loop
                        exitLoop = true;
                        break;
                    }
                }
                if (mergeOrder.size() == 0) {
                    mergeOrderIterator.remove();
                }
            }
        }
        // If the transaction has not been completed, the order will be pushed into the list
        if (focusedOrder.getTradedAmount().compareTo(focusedOrder.getAmount()) < 0 && canEnterList) {
            addLimitPriceOrder(focusedOrder);
        }
        // Matching batch push for each order
        handleExchangeTrade(exchangeTrades);
        if (!completedOrders.isEmpty()) {
            orderCompleted(completedOrders);
            TradePlate plate = focusedOrder.getDirection() == ExchangeOrderDirection.BUY ? sellTradePlate : buyTradePlate;
            sendTradePlateMessage(plate);
        }
    }

    /**
     * The limit order matches the limit queue
     *
     * @param lpList       Price limit opponents single queue
     * @param focusedOrder Transaction order
     */
    public void matchLimitPriceWithLPList(TreeMap<BigDecimal, MergeOrder> lpList, ExchangeOrder focusedOrder, boolean canEnterList) {
        List<ExchangeTrade> exchangeTrades = new ArrayList<>();
        List<ExchangeOrder> completedOrders = new ArrayList<>();
        synchronized (lpList) {
            Iterator<Map.Entry<BigDecimal, MergeOrder>> mergeOrderIterator = lpList.entrySet().iterator();
            boolean exitLoop = false;
            while (!exitLoop && mergeOrderIterator.hasNext()) {
                Map.Entry<BigDecimal, MergeOrder> entry = mergeOrderIterator.next();
                MergeOrder mergeOrder = entry.getValue();
                Iterator<ExchangeOrder> orderIterator = mergeOrder.iterator();

                // The price to match for the purchase order shall not be greater than the entrustment price, otherwise the exit will be
                if (focusedOrder.getDirection() == ExchangeOrderDirection.BUY && mergeOrder.getPrice().compareTo(focusedOrder.getPrice()) > 0) {
                    break;
                }
                // The price to match for the sell order is not less than the entrustment price, otherwise the exit will be
                if (focusedOrder.getDirection() == ExchangeOrderDirection.SELL && mergeOrder.getPrice().compareTo(focusedOrder.getPrice()) < 0) {
                    break;
                }
                while (orderIterator.hasNext()) {
                    ExchangeOrder matchOrder = orderIterator.next();

                    // Processing matches
                    ExchangeTrade trade = processMatch(focusedOrder, matchOrder);
                    exchangeTrades.add(trade);
                    // Determine whether the matching order is completed
                    if (matchOrder.isCompleted()) {
                        // The current matching order completes the transaction, delete the order
                        orderIterator.remove();
                        completedOrders.add(matchOrder);
                    }
                    // Determine whether the transaction order is completed
                    if (focusedOrder.isCompleted()) {
                        // Transaction completed
                        completedOrders.add(focusedOrder);
                        // Exit the loop
                        exitLoop = true;
                        break;
                    }
                }
                if (mergeOrder.size() == 0) {
                    mergeOrderIterator.remove();
                }
            }
        }
        // If the transaction has not been completed, the order will be pushed into the list
        if (focusedOrder.getTradedAmount().compareTo(focusedOrder.getAmount()) < 0 && canEnterList) {
            addLimitPriceOrder(focusedOrder);
        }
        // Matching batch push for each order
        handleExchangeTrade(exchangeTrades);
        if (!completedOrders.isEmpty()) {
            orderCompleted(completedOrders);
            TradePlate plate = focusedOrder.getDirection() == ExchangeOrderDirection.BUY ? sellTradePlate : buyTradePlate;
            sendTradePlateMessage(plate);
        }
    }

    /**
     * Price limit order matches market queue
     *
     * @param mpList       Market price rivals single queue
     * @param focusedOrder Transaction order
     */
    public void matchLimitPriceWithMPList(LinkedList<ExchangeOrder> mpList, ExchangeOrder focusedOrder) {
        List<ExchangeTrade> exchangeTrades = new ArrayList<>();
        List<ExchangeOrder> completedOrders = new ArrayList<>();
        synchronized (mpList) {
            Iterator<ExchangeOrder> iterator = mpList.iterator();
            while (iterator.hasNext()) {
                ExchangeOrder matchOrder = iterator.next();
                ExchangeTrade trade = processMatch(focusedOrder, matchOrder);
                logger.info(">>>>>" + trade);
                if (trade != null) {
                    exchangeTrades.add(trade);
                }
                // Determine whether the matching order is completed，市价单amount为成交量
                if (matchOrder.isCompleted()) {
                    iterator.remove();
                    completedOrders.add(matchOrder);
                }
                // Determine whether the order is completed and whether the transaction volume is completed
                if (focusedOrder.isCompleted()) {
                    // Transaction completed
                    completedOrders.add(focusedOrder);
                    // Exit the loop
                    break;
                }
            }
        }
        // If the transaction has not been completed, the order will be pushed into the list
        if (focusedOrder.getTradedAmount().compareTo(focusedOrder.getAmount()) < 0) {
            addLimitPriceOrder(focusedOrder);
        }
        // Matching batch push for each order
        handleExchangeTrade(exchangeTrades);
        orderCompleted(completedOrders);
    }


    /**
     * Market order and limit price competitor order list transaction
     *
     * @param lpList       List of limit competitors
     * @param focusedOrder Orders to be traded
     */
    public void matchMarketPriceWithLPList(TreeMap<BigDecimal, MergeOrder> lpList, ExchangeOrder focusedOrder) {
        List<ExchangeTrade> exchangeTrades = new ArrayList<>();
        List<ExchangeOrder> completedOrders = new ArrayList<>();
        synchronized (lpList) {
            Iterator<Map.Entry<BigDecimal, MergeOrder>> mergeOrderIterator = lpList.entrySet().iterator();
            boolean exitLoop = false;
            while (!exitLoop && mergeOrderIterator.hasNext()) {
                Map.Entry<BigDecimal, MergeOrder> entry = mergeOrderIterator.next();
                MergeOrder mergeOrder = entry.getValue();
                Iterator<ExchangeOrder> orderIterator = mergeOrder.iterator();
                while (orderIterator.hasNext()) {
                    ExchangeOrder matchOrder = orderIterator.next();
                    // Processing matches
                    ExchangeTrade trade = processMatch(focusedOrder, matchOrder);
                    if (trade != null) {
                        exchangeTrades.add(trade);
                    }
                    // Determine whether the matching order is completed
                    if (matchOrder.isCompleted()) {
                        // The current matching order completes the transaction, delete the order
                        orderIterator.remove();
                        completedOrders.add(matchOrder);
                    }
                    // Determine whether the focus order is completed
                    if (focusedOrder.isCompleted()) {
                        completedOrders.add(focusedOrder);
                        // Exit the loop
                        exitLoop = true;
                        break;
                    }
                }
                if (mergeOrder.size() == 0) {
                    mergeOrderIterator.remove();
                }
            }
        }
        // If the transaction has not been completed, the order will be pushed into the list,市价买单按成交量算
        if (focusedOrder.getDirection() == ExchangeOrderDirection.SELL && focusedOrder.getTradedAmount().compareTo(focusedOrder.getAmount()) < 0
                || focusedOrder.getDirection() == ExchangeOrderDirection.BUY && focusedOrder.getTurnover().compareTo(focusedOrder.getAmount()) < 0) {
            addMarketPriceOrder(focusedOrder);
        }
        // Matching batch push for each order
        handleExchangeTrade(exchangeTrades);
        if (!completedOrders.isEmpty()) {
            orderCompleted(completedOrders);
            TradePlate plate = focusedOrder.getDirection() == ExchangeOrderDirection.BUY ? sellTradePlate : buyTradePlate;
            sendTradePlateMessage(plate);
        }
    }

    /**
     * Calculate the remaining amount of transactions for the entrustment order
     *
     * @param order     Entrustment form
     * @param dealPrice Transaction price
     * @return
     */
    private BigDecimal calculateTradedAmount(ExchangeOrder order, BigDecimal dealPrice) {
        if (order.getDirection() == ExchangeOrderDirection.BUY && order.getType() == ExchangeOrderType.MARKET_PRICE) {
            // Remaining transaction volume
            BigDecimal leftTurnover = order.getAmount().subtract(order.getTurnover());
            return leftTurnover.divide(dealPrice, coinScale, RoundingMode.DOWN);
        } else {
            return order.getAmount().subtract(order.getTradedAmount());
        }
    }

    /**
     * Adjust the remaining transaction volume of the market order, and set up orders to complete when the remaining transaction volume is insufficient
     *
     * @param order
     * @param dealPrice
     * @return
     */
    private BigDecimal adjustMarketOrderTurnover(ExchangeOrder order, BigDecimal dealPrice) {
        if (order.getDirection() == ExchangeOrderDirection.BUY && order.getType() == ExchangeOrderType.MARKET_PRICE) {
            BigDecimal leftTurnover = order.getAmount().subtract(order.getTurnover());
            if (leftTurnover.divide(dealPrice, coinScale, RoundingMode.DOWN)
                    .compareTo(BigDecimal.ZERO) == 0) {
                order.setTurnover(order.getAmount());
                return leftTurnover;
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * Process two matching entrusted orders
     *
     * @param focusedOrder Focus list
     * @param matchOrder   Matching order
     * @return
     */
    private ExchangeTrade processMatch(ExchangeOrder focusedOrder, ExchangeOrder matchOrder) {
        // The quantity required for transactions, transaction volume, transaction price, available quantity
        BigDecimal needAmount, dealPrice, availAmount;
        // If the matching order is a limit order, the price is the transaction price
        if (matchOrder.getType() == ExchangeOrderType.LIMIT_PRICE) {
            dealPrice = matchOrder.getPrice();
        } else {
            dealPrice = focusedOrder.getPrice();
        }
        // The transaction price must be greater than 0
        if (dealPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        needAmount = calculateTradedAmount(focusedOrder, dealPrice);
        availAmount = calculateTradedAmount(matchOrder, dealPrice);
        // Calculate the transaction volume
        BigDecimal tradedAmount = (availAmount.compareTo(needAmount) >= 0 ? needAmount : availAmount);
        logger.info("dealPrice={},amount={}", dealPrice, tradedAmount);
        // If the transaction amount is 0, it means that the remaining amount cannot be traded.
        if (tradedAmount.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }

        // Calculate the transaction volume, and maintain sufficient accuracy
        BigDecimal turnover = tradedAmount.multiply(dealPrice);
        matchOrder.setTradedAmount(matchOrder.getTradedAmount().add(tradedAmount));
        matchOrder.setTurnover(matchOrder.getTurnover().add(turnover));
        focusedOrder.setTradedAmount(focusedOrder.getTradedAmount().add(tradedAmount));
        focusedOrder.setTurnover(focusedOrder.getTurnover().add(turnover));

        // Create a transaction record
        ExchangeTrade exchangeTrade = new ExchangeTrade();
        exchangeTrade.setSymbol(symbol);
        exchangeTrade.setAmount(tradedAmount);
        exchangeTrade.setDirection(focusedOrder.getDirection());
        exchangeTrade.setPrice(dealPrice);
        exchangeTrade.setBuyTurnover(turnover);
        exchangeTrade.setSellTurnover(turnover);
        // Correct the remaining transaction volume of the market order
        if (ExchangeOrderType.MARKET_PRICE == focusedOrder.getType() && focusedOrder.getDirection() == ExchangeOrderDirection.BUY) {
            BigDecimal adjustTurnover = adjustMarketOrderTurnover(focusedOrder, dealPrice);
            exchangeTrade.setBuyTurnover(turnover.add(adjustTurnover));
        } else if (ExchangeOrderType.MARKET_PRICE == matchOrder.getType() && matchOrder.getDirection() == ExchangeOrderDirection.BUY) {
            BigDecimal adjustTurnover = adjustMarketOrderTurnover(matchOrder, dealPrice);
            exchangeTrade.setBuyTurnover(turnover.add(adjustTurnover));
        }

        if (focusedOrder.getDirection() == ExchangeOrderDirection.BUY) {
            exchangeTrade.setBuyOrderId(focusedOrder.getOrderId());
            exchangeTrade.setSellOrderId(matchOrder.getOrderId());
        } else {
            exchangeTrade.setBuyOrderId(matchOrder.getOrderId());
            exchangeTrade.setSellOrderId(focusedOrder.getOrderId());
        }

        exchangeTrade.setTime(Calendar.getInstance().getTimeInMillis());
        if (matchOrder.getType() == ExchangeOrderType.LIMIT_PRICE) {
            if (matchOrder.getDirection() == ExchangeOrderDirection.BUY) {
                buyTradePlate.remove(matchOrder, tradedAmount);
            } else {
                sellTradePlate.remove(matchOrder, tradedAmount);
            }
        }
        return exchangeTrade;
    }

    /**
     * Process two matching entrusted orders
     *
     * @param focusedOrder Focus list
     * @param matchOrder   Matching order
     * @return
     */
    private ExchangeTrade processMatchByFENTAN(ExchangeOrder focusedOrder, ExchangeOrder matchOrder, BigDecimal totalAmount) {
        // The quantity required for transactions, transaction volume, transaction price, available quantity
        BigDecimal dealPrice;
        // If the matching order is a limit order, the price is the transaction price
        if (matchOrder.getType() == ExchangeOrderType.LIMIT_PRICE) {
            dealPrice = matchOrder.getPrice();
        } else {
            dealPrice = focusedOrder.getPrice();
        }
        // The transaction price must be greater than 0
        if (dealPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        // Number of transactions = Total number of issuance and sell orders * proportion of matching orders (e.g.: 1.2345%)
        // Calculate the transaction volume
        BigDecimal tradedAmount = focusedOrder.getAmount()
                .multiply(matchOrder.getAmount().divide(totalAmount, 8, RoundingMode.HALF_DOWN))
                .setScale(8, RoundingMode.HALF_DOWN);
        logger.info("dealPrice={},amount={}", dealPrice, tradedAmount);
        // If the transaction amount is 0, it means that the remaining amount cannot be traded.
        if (tradedAmount.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }

        // Calculate the transaction volume, and maintain sufficient accuracy
        BigDecimal turnover = tradedAmount.multiply(dealPrice).setScale(8, RoundingMode.HALF_DOWN);
        matchOrder.setTradedAmount(matchOrder.getTradedAmount().add(tradedAmount).setScale(8, RoundingMode.HALF_DOWN));
        matchOrder.setTurnover(matchOrder.getTurnover().add(turnover).setScale(8, RoundingMode.HALF_DOWN));
        focusedOrder.setTradedAmount(focusedOrder.getTradedAmount().add(tradedAmount).setScale(8, RoundingMode.HALF_DOWN));
        focusedOrder.setTurnover(focusedOrder.getTurnover().add(turnover).setScale(8, RoundingMode.HALF_DOWN));

        // Create a transaction record
        ExchangeTrade exchangeTrade = new ExchangeTrade();
        exchangeTrade.setSymbol(symbol);
        exchangeTrade.setAmount(tradedAmount);
        exchangeTrade.setDirection(focusedOrder.getDirection());
        exchangeTrade.setPrice(dealPrice);
        exchangeTrade.setBuyTurnover(turnover);
        exchangeTrade.setSellTurnover(turnover);

        if (focusedOrder.getDirection() == ExchangeOrderDirection.BUY) {
            exchangeTrade.setBuyOrderId(focusedOrder.getOrderId());
            exchangeTrade.setSellOrderId(matchOrder.getOrderId());
        } else {
            exchangeTrade.setBuyOrderId(matchOrder.getOrderId());
            exchangeTrade.setSellOrderId(focusedOrder.getOrderId());
        }

        exchangeTrade.setTime(Calendar.getInstance().getTimeInMillis());
        if (matchOrder.getType() == ExchangeOrderType.LIMIT_PRICE) {
            if (matchOrder.getDirection() == ExchangeOrderDirection.BUY) {
                buyTradePlate.remove(matchOrder, tradedAmount);
            } else {
                sellTradePlate.remove(matchOrder, tradedAmount);
            }
        }
        return exchangeTrade;
    }

    public void handleExchangeTrade(List<ExchangeTrade> trades) {
        // logger.info("handleExchangeTrade:{}", trades);
        if (!trades.isEmpty()) {
            int maxSize = 1000;
            // Send a message, the key is the transaction pair symbol
            if (trades.size() > maxSize) {
                int size = trades.size();
                for (int index = 0; index < size; index += maxSize) {
                    int length = (size - index) > maxSize ? maxSize : size - index;
                    List<ExchangeTrade> subTrades = trades.subList(index, index + length);
                    try {
                        kafkaTemplate.send(exchangeTradeTopic, objectMapper.writeValueAsString(subTrades));
                    } catch (JsonProcessingException e) {
                        logger.error("Error serializing subTrades to JSON", e);
                    }
                }
            } else {
                try {
                    kafkaTemplate.send(exchangeTradeTopic, objectMapper.writeValueAsString(trades));
                } catch (JsonProcessingException e) {
                    logger.error("Error serializing trades to JSON", e);
                }
            }
        }
    }

    /**
     * Order completion, message notification is executed, and the number of orders exceeds 1,000 to be sent separately
     *
     * @param orders
     */
    public void orderCompleted(List<ExchangeOrder> orders) {
        // logger.info("orderCompleted ,order={}",orders);
        if (!orders.isEmpty()) {
            int maxSize = 1000;
            if (orders.size() > maxSize) {
                int size = orders.size();
                for (int index = 0; index < size; index += maxSize) {
                    int length = (size - index) > maxSize ? maxSize : size - index;
                    List<ExchangeOrder> subOrders = orders.subList(index, index + length);
                    try {
                        kafkaTemplate.send(exchangeOrderCompleteTopic, objectMapper.writeValueAsString(subOrders));
                    } catch (JsonProcessingException e) {
                        logger.error("Error serializing subOrders to JSON", e);
                    }
                }
            } else {
                try {
                    kafkaTemplate.send(exchangeOrderCompleteTopic, objectMapper.writeValueAsString(orders));
                } catch (JsonProcessingException e) {
                    logger.error("Error serializing orders to JSON", e);
                }
            }
        }
    }

    /**
     * Send a transaction change message
     *
     * @param plate
     */
    public void sendTradePlateMessage(TradePlate plate) {
        // Prevent concurrency from causing arrays to cross boundaries and cause reverse hangs on the market
        synchronized (plate.getItems()) {
            try {
                kafkaTemplate.send(exchangeTradePlateTopic, objectMapper.writeValueAsString(plate));
            } catch (JsonProcessingException e) {
                logger.error("Error serializing trade plate to JSON", e);
            }
        }
    }

    /**
     * Cancel the entrustment order
     *
     * @param exchangeOrder
     * @return
     */
    public ExchangeOrder cancelOrder(ExchangeOrder exchangeOrder) {
        logger.info("cancelOrder,orderId={}", exchangeOrder.getOrderId());
        if (exchangeOrder.getType() == ExchangeOrderType.MARKET_PRICE) {
            // Process market orders
            Iterator<ExchangeOrder> orderIterator;
            List<ExchangeOrder> list = null;
            if (exchangeOrder.getDirection() == ExchangeOrderDirection.BUY) {
                list = this.buyMarketQueue;
            } else {
                list = this.sellMarketQueue;
            }
            synchronized (list) {
                orderIterator = list.iterator();
                while ((orderIterator.hasNext())) {
                    ExchangeOrder order = orderIterator.next();
                    if (order.getOrderId().equalsIgnoreCase(exchangeOrder.getOrderId())) {
                        orderIterator.remove();
                        onRemoveOrder(order);
                        return order;
                    }
                }
            }
        } else {
            // Process limit orders
            TreeMap<BigDecimal, MergeOrder> list = null;
            Iterator<MergeOrder> mergeOrderIterator;
            if (exchangeOrder.getDirection() == ExchangeOrderDirection.BUY) {
                list = this.buyLimitPriceQueue;
            } else {
                list = this.sellLimitPriceQueue;
            }
            synchronized (list) {
                MergeOrder mergeOrder = list.get(exchangeOrder.getPrice());
                if (mergeOrder != null) {
                    Iterator<ExchangeOrder> orderIterator = mergeOrder.iterator();
                    while (orderIterator.hasNext()) {
                        ExchangeOrder order = orderIterator.next();
                        if (order.getOrderId().equalsIgnoreCase(exchangeOrder.getOrderId())) {
                            orderIterator.remove();
                            if (mergeOrder.size() == 0) {
                                list.remove(exchangeOrder.getPrice());
                            }
                            onRemoveOrder(order);
                            return order;
                        }
                    }
                }
            }
        }
        return null;
    }

    public void onRemoveOrder(ExchangeOrder order) {
        if (order.getType() == ExchangeOrderType.LIMIT_PRICE) {
            if (order.getDirection() == ExchangeOrderDirection.BUY) {
                buyTradePlate.remove(order);
                sendTradePlateMessage(buyTradePlate);
            } else {
                sellTradePlate.remove(order);
                sendTradePlateMessage(sellTradePlate);
            }
        }
    }


    public TradePlate getTradePlate(ExchangeOrderDirection direction) {
        if (direction == ExchangeOrderDirection.BUY) {
            return buyTradePlate;
        } else {
            return sellTradePlate;
        }
    }


    /**
     * Query orders in the trader
     *
     * @param orderId
     * @param type
     * @param direction
     * @return
     */
    public ExchangeOrder findOrder(String orderId, ExchangeOrderType type, ExchangeOrderDirection direction) {
        if (type == ExchangeOrderType.MARKET_PRICE) {
            LinkedList<ExchangeOrder> list;
            if (direction == ExchangeOrderDirection.BUY) {
                list = this.buyMarketQueue;
            } else {
                list = this.sellMarketQueue;
            }
            synchronized (list) {
                for (ExchangeOrder order : list) {
                    if (order.getOrderId().equalsIgnoreCase(orderId)) {
                        return order;
                    }
                }
            }
        } else {
            TreeMap<BigDecimal, MergeOrder> list;
            if (direction == ExchangeOrderDirection.BUY) {
                list = this.buyLimitPriceQueue;
            } else {
                list = this.sellLimitPriceQueue;
            }
            synchronized (list) {
                for (Map.Entry<BigDecimal, MergeOrder> entry : list.entrySet()) {
                    MergeOrder mergeOrder = entry.getValue();
                    Iterator<ExchangeOrder> orderIterator = mergeOrder.iterator();
                    while ((orderIterator.hasNext())) {
                        ExchangeOrder order = orderIterator.next();
                        if (order.getOrderId().equalsIgnoreCase(orderId)) {
                            return order;
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * Suspend transactions and no new orders are accepted
     */
    public void haltTrading() {
        this.tradingHalt = true;
    }

    /**
     * Resuming transactions
     */
    public void resumeTrading() {
        this.tradingHalt = false;
    }

    public void stopTrading() {
        // TODO: Stop trading and cancel all current orders
    }

    public boolean getReady() {
        return this.ready;
    }

    public int getLimitPriceOrderCount(ExchangeOrderDirection direction) {
        int count = 0;
        TreeMap<BigDecimal, MergeOrder> queue = direction == ExchangeOrderDirection.BUY ? buyLimitPriceQueue : sellLimitPriceQueue;
        for (Map.Entry<BigDecimal, MergeOrder> entry : queue.entrySet()) {
            MergeOrder mergeOrder = entry.getValue();
            count += mergeOrder.size();
        }
        return count;
    }
}
