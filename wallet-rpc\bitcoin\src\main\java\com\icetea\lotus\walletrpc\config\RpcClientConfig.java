package com.icetea.lotus.walletrpc.config;

import com.icetea.lotus.walletrpc.bitcoinrpc.BitcoinRpcClient;
import com.icetea.lotus.walletrpc.bitcoinrpc.BitcoinRpcException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.MalformedURLException;

/**
 * Initialize the RPC client
 */
@Configuration
@Slf4j
public class RpcClientConfig {

    @Bean
    public BitcoinRpcClient setClient(@Value("${coin.rpc}") String uri) {
        try {
            log.info("uri={}", uri);
            BitcoinRpcClient client = new BitcoinRpcClient(uri);
            int blockCount = client.getBlockCount();
            log.info("blockHeight={}", blockCount);
            return client;
        } catch (MalformedURLException e) {
            log.error("Failed to initialize wallet: {}", e.getMessage(), e);
            return null;
        } catch (BitcoinRpcException e) {
            log.error("Bitcoin RPC error: {}", e.getMessage(), e);
            return null;
        }
    }
}
