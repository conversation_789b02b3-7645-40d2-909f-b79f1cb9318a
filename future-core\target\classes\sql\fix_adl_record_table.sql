-- Script để sửa lỗi bảng contract_adl_record
-- <PERSON><PERSON><PERSON> bảo cấu trúc bảng phù hợp với JPA entity

-- <PERSON>ểm tra và sửa cấu trúc bảng contract_adl_record
DO $$
BEGIN
    -- <PERSON><PERSON><PERSON> tra xem bảng có tồn tại không
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'contract_adl_record') THEN

        -- <PERSON><PERSON><PERSON> tra cấu trúc cột id hiện tại
        IF EXISTS (
            SELECT FROM information_schema.columns
            WHERE table_name = 'contract_adl_record'
            AND column_name = 'id'
            AND data_type = 'character varying'
        ) THEN
            -- Nếu id là VARCHAR, c<PERSON><PERSON> chuyể<PERSON> đổi sang BIGSERIAL
            RAISE NOTICE 'Chuyển đổi cột id từ VARCHAR sang BIGSERIAL...';

            -- Backup dữ liệu hiện có
            CREATE TEMP TABLE adl_record_backup AS
            SELECT position_id, member_id, symbol, direction, volume, price, time
            FROM contract_adl_record;

            -- <PERSON><PERSON><PERSON> bảng cũ
            DROP TABLE contract_adl_record;

            -- <PERSON><PERSON><PERSON> lạ<PERSON> bảng với cấu trúc đúng
            CREATE TABLE contract_adl_record (
                id BIGSERIAL PRIMARY KEY,
                position_id BIGINT NOT NULL,
                member_id BIGINT NOT NULL,
                symbol VARCHAR(50) NOT NULL,
                direction VARCHAR(10) NOT NULL,
                volume DECIMAL(18,8) NOT NULL,
                price DECIMAL(18,8) NOT NULL,
                time TIMESTAMP NOT NULL
            );

            -- Khôi phục dữ liệu
            INSERT INTO contract_adl_record (position_id, member_id, symbol, direction, volume, price, time)
            SELECT position_id::BIGINT, member_id::BIGINT, symbol, direction, volume, price, time
            FROM adl_record_backup;

            RAISE NOTICE 'Đã chuyển đổi thành công cột id sang BIGSERIAL';

        ELSIF EXISTS (
            SELECT FROM information_schema.columns
            WHERE table_name = 'contract_adl_record'
            AND column_name = 'id'
            AND data_type = 'bigint'
        ) THEN
            -- Kiểm tra xem có phải SERIAL không
            IF NOT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'contract_adl_record'
                AND column_name = 'id'
                AND column_default LIKE 'nextval%'
            ) THEN
                -- Nếu là BIGINT nhưng không phải SERIAL, cần thêm sequence
                RAISE NOTICE 'Thêm sequence cho cột id...';

                -- Tạo sequence
                CREATE SEQUENCE IF NOT EXISTS contract_adl_record_id_seq;

                -- Gán sequence cho cột
                ALTER TABLE contract_adl_record
                ALTER COLUMN id SET DEFAULT nextval('contract_adl_record_id_seq');

                -- Cập nhật sequence value
                SELECT setval('contract_adl_record_id_seq', COALESCE(MAX(id), 1))
                FROM contract_adl_record;

                RAISE NOTICE 'Đã thêm sequence cho cột id';
            ELSE
                RAISE NOTICE 'Cấu trúc bảng contract_adl_record đã đúng';
            END IF;
        END IF;

    ELSE
        -- Tạo bảng mới với cấu trúc đúng
        RAISE NOTICE 'Tạo bảng contract_adl_record mới...';

        CREATE TABLE contract_adl_record (
            id BIGSERIAL PRIMARY KEY,
            position_id BIGINT NOT NULL,
            member_id BIGINT NOT NULL,
            symbol VARCHAR(50) NOT NULL,
            direction VARCHAR(10) NOT NULL,
            volume DECIMAL(18,8) NOT NULL,
            price DECIMAL(18,8) NOT NULL,
            time TIMESTAMP NOT NULL
        );

        RAISE NOTICE 'Đã tạo bảng contract_adl_record';
    END IF;

    -- Tạo các index nếu chưa có
    CREATE INDEX IF NOT EXISTS idx_adl_record_position_id ON contract_adl_record (position_id);
    CREATE INDEX IF NOT EXISTS idx_adl_record_member_id ON contract_adl_record (member_id);
    CREATE INDEX IF NOT EXISTS idx_adl_record_symbol ON contract_adl_record (symbol);
    CREATE INDEX IF NOT EXISTS idx_adl_record_time ON contract_adl_record (time);

    -- Thêm comments
    COMMENT ON TABLE contract_adl_record IS 'Bảng lưu trữ các bản ghi ADL (Auto-Deleveraging)';
    COMMENT ON COLUMN contract_adl_record.id IS 'ID tự động tăng của bản ghi ADL';
    COMMENT ON COLUMN contract_adl_record.position_id IS 'ID của vị thế';
    COMMENT ON COLUMN contract_adl_record.member_id IS 'ID của thành viên';
    COMMENT ON COLUMN contract_adl_record.symbol IS 'Symbol của hợp đồng';
    COMMENT ON COLUMN contract_adl_record.direction IS 'Hướng của vị thế: LONG, SHORT';
    COMMENT ON COLUMN contract_adl_record.volume IS 'Khối lượng của vị thế';
    COMMENT ON COLUMN contract_adl_record.price IS 'Giá của vị thế';
    COMMENT ON COLUMN contract_adl_record.time IS 'Thời gian ADL';

    RAISE NOTICE 'Hoàn thành sửa lỗi bảng contract_adl_record';

END $$;
