# Implementation Roadmap - 5 Phases (7 Weeks)

## Phase 1: Setup Future-API Structure (Week 1)

### 1.1. **Objectives**
- Chuẩn bị future-api để nhận API components từ future-core
- Setup complete infrastructure cho API service
- **Setup notification services trong future-api** (thay vì chat)

### 1.2. **Deliverables**
- ✅ Future-API service structure hoàn chỉnh
- ✅ POM.xml với all required dependencies
- ✅ Application configuration files
- ✅ Placeholder controllers và WebSocket handlers
- ✅ **Notification services setup trong future-api**
- ✅ Security configuration setup

### 1.3. **Implementation Steps**
```bash
# Create future-api structure
mkdir -p future-api/src/main/java/com/icetea/lotus/{controller,websocket,messaging,notification,security,config}
mkdir -p future-api/src/main/resources

# Key components to create:
1. FutureApiApplication.java
2. TradingController (placeholder)
3. PositionController (placeholder)
4. WebSocketPositionHandler (placeholder)
5. NotificationService ✅
6. EmailService ✅
7. SMSService ✅
8. NotificationWebSocketHandler ✅
```

### 1.4. **Success Criteria**
- Future-API builds successfully
- Application starts without errors
- Health check endpoints accessible
- Notification services ready
- Ready để nhận components từ future-core

---

## Phase 2: Create Future Matching Engine Service (Week 2)

### 2.1. **Objectives**
- Tạo dedicated matching engine service
- Setup high-performance matching infrastructure
- Prepare để nhận matching engines từ future-core

### 2.2. **Deliverables**
- ✅ Future matching engine service hoàn chỉnh
- ✅ Kafka messaging infrastructure
- ✅ Redis distributed locking setup
- ✅ Placeholder matching engines
- ✅ Order processing pipeline

### 2.3. **Implementation Steps**
```bash
# Create future service structure
mkdir -p future/src/main/java/com/icetea/lotus/{matching,service,messaging,config}
mkdir -p future/src/main/resources

# Key components to create:
1. FutureApplication.java
2. OrderProcessingService (placeholder)
3. TradeExecutionService (placeholder)
4. OrderCommandConsumer (placeholder)
5. TradeEventProducer (placeholder)
6. Matching engine placeholders
```

### 2.4. **Success Criteria**
- Future service builds successfully
- Kafka consumers/producers working
- Redis connections established
- Ready để nhận matching engines từ future-core

---

## Phase 3: Extract Market Data Services (Weeks 3-4)

### 3.1. **Objectives**
- Di chuyển futures market data từ future-core sang market service
- Tạo unified market data hub cho spot và futures
- Setup real-time market data streaming

### 3.2. **Components to Migrate**
```
future-core → market service:
├── LastPriceService
├── IndexPriceService
├── MarkPriceService
├── KLineManagementService
├── WebSocketMarketHandler
├── PriceCache
├── KLineCache
└── Market data messaging
```

### 3.3. **Implementation Steps**

#### Week 3: Market Service Enhancement
```bash
# Enhance market service structure
mkdir -p market/src/main/java/com/icetea/lotus/service/futures
mkdir -p market/src/main/java/com/icetea/lotus/websocket/futures
mkdir -p market/src/main/java/com/icetea/lotus/cache/futures
mkdir -p market/src/main/java/com/icetea/lotus/controller/futures

# Manual implementation:
1. Create futures market data services
2. Setup futures WebSocket handlers
3. Configure futures cache components
4. Create REST API endpoints
5. Setup Kafka messaging
```

#### Week 4: Integration & Testing
```bash
# Integration tasks:
1. Update future-core để use market service
2. Create MarketDataClient trong future-core
3. Setup circuit breakers và retry logic
4. Configure service discovery
5. Integration testing
```

### 3.4. **Success Criteria**
- Market service serves futures market data
- Future-core successfully calls market service
- WebSocket streaming hoạt động
- Performance maintained hoặc improved

---

## Phase 4: Extract Background Schedulers (Week 5)

### 4.1. **Objectives**
- Di chuyển background schedulers từ future-core sang admin service
- Centralize system management operations
- Setup monitoring và alerting

### 4.2. **Components to Migrate**
```
future-core → admin service:
├── LiquidationScheduler
├── FundingScheduler
├── SettlementScheduler
├── HealthCheckScheduler
├── ADLScheduler
└── System monitoring components
```

### 4.3. **Implementation Steps**
```bash
# Enhance admin service
mkdir -p admin/src/main/java/com/icetea/lotus/service/scheduler
mkdir -p admin/src/main/java/com/icetea/lotus/monitoring
mkdir -p admin/src/main/java/com/icetea/lotus/management

# Manual implementation:
1. Create scheduler services
2. Setup monitoring dashboards
3. Configure alerting rules
4. Update future-core integration
5. Test scheduler operations
```

### 4.4. **Success Criteria**
- All schedulers running từ admin service
- Monitoring dashboards operational
- Alerting system working
- Future-core no longer has scheduler dependencies

---

## Phase 5: Extract Wallet Operations & Complete Migration (Weeks 6-7)

### 5.1. **Objectives**
- Di chuyển futures wallet logic sang wallet service
- Complete migration của remaining components
- Convert future-core thành pure library

### 5.2. **Final Migrations**

#### Week 6: Wallet Enhancement & Component Migration
```
future-core → wallet service:
├── Futures balance management
├── Margin calculations
├── Futures transaction recording
├── Risk management cho margin

future-core → future-api:
├── TradingController
├── PositionController
├── TradingManagementController
├── WebSocketPositionHandler
├── OrderCommandProducer
├── OrderEventConsumer
├── Notification components (already setup)

future-core → future service:
├── DistributedLockingMatchingEngine ✅ (primary - proven)
├── OptimizedMatchingEngine ✅ (backup engine)
├── SymbolMatchingEngine ✅ (symbol management)
├── DistributedOrderBookSnapshot ✅ (Redis snapshots)
├── DistributedMatchingEngineFactory ✅ (engine factory)
├── OrderProcessingService
├── TradeExecutionService
└── RedisLockingConfig ✅ (locking configuration)

Note: ❌ LockFreeMatchingEngine removed (not used in production)
```

#### Week 7: Library Conversion & Testing
```
future-core library conversion:
1. Remove FuturesCoreApplication.java
2. Update pom.xml (library packaging)
3. Clean up infrastructure components
4. Keep only domain và application logic
5. Update all service dependencies

Integration testing:
1. End-to-end trading flow testing
2. Performance testing
3. Load testing
4. Failover testing
5. Security testing
```

### 5.3. **Success Criteria**
- Wallet service handles futures operations
- Future-core is pure library
- Future-API handles all client requests với notifications
- Future service handles all matching
- All enhanced services operational
- Performance targets met

---

## Implementation Timeline Summary

```
Week 1: Phase 1 - Setup Future-API Structure (với notifications)
Week 2: Phase 2 - Create Future Matching Engine Service
Week 3: Phase 3.1 - Extract Market Data Services (Setup)
Week 4: Phase 3.2 - Extract Market Data Services (Integration)
Week 5: Phase 4 - Extract Background Schedulers
Week 6: Phase 5.1 - Extract Wallet Operations & Component Migration
Week 7: Phase 5.2 - Library Conversion & Testing
```

## Service Communication Patterns

### 1. **Future-API ↔ Market Service**
```
HTTP REST API:
- GET /api/market/futures/price/{symbol}
- GET /api/market/futures/kline/{symbol}

Kafka Events:
- future-price-updates
- future-kline-updates
```

### 2. **Future-API ↔ Wallet Service**
```
HTTP REST API:
- POST /api/wallet/futures/balance
- POST /api/wallet/futures/margin

Kafka Events:
- future-balance-updates
- future-margin-calls
```

### 3. **Future-API ↔ Admin Service**
```
HTTP REST API:
- GET /api/admin/scheduler/status

Kafka Events:
- future-liquidation-events
- future-funding-events
- future-system-alerts
```

### 4. **Future-API ↔ Future Service**
```
Kafka Events:
- future-order-commands (API → Engine)
- future-order-events (Engine → API)
- future-trade-events (Engine → API)
- future-position-updates (Engine → API)
```

### 5. **Notification Flow trong Future-API**
```
Trading Events → NotificationService → Email/SMS
                     ↓
              WebSocket Alerts → Clients
```

## Risk Management

### Phase-by-Phase Risks
- **Phase 1**: 🟢 Low risk - setup only
- **Phase 2**: 🟢 Low risk - setup only
- **Phase 3**: 🟡 Medium risk - market data migration
- **Phase 4**: 🟢 Low risk - scheduler migration
- **Phase 5**: 🔴 High risk - final integration

### Mitigation Strategies
- **Feature flags** cho gradual rollout
- **Blue-green deployment** cho each phase
- **Comprehensive testing** at each phase
- **Rollback procedures** documented
- **Monitoring** at each step

## Success Metrics

### Performance Targets
- **Matching Engine**: < 50ms latency
- **API Response**: < 100ms
- **Market Data**: < 10ms update propagation
- **WebSocket**: < 5ms message delivery
- **Notifications**: < 2s delivery time

### Reliability Targets
- **Uptime**: 99.9% cho critical services
- **Data Consistency**: 100% accuracy
- **Zero Data Loss**: During all migrations
- **Backward Compatibility**: Maintained throughout

## Key Benefits của Simplified Architecture

### 1. **Reduced Complexity**
- 6 services thay vì 7 services
- Notifications integrated trong future-api
- Fewer inter-service dependencies

### 2. **Easier Management**
- Simplified deployment procedures
- Reduced monitoring complexity
- Easier troubleshooting

### 3. **Better Performance**
- Direct notification handling trong API service
- Reduced network hops
- Faster notification delivery

### 4. **Development Efficiency**
- Clearer service boundaries
- Easier testing
- Simplified integration

Roadmap này sẽ tạo ra một **efficient futures trading architecture** với **optimal performance** và **simplified management**!
