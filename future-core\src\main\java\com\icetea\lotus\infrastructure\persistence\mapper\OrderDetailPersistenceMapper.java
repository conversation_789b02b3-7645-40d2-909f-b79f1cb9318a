package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.OrderDetail;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.OrderDetailId;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.infrastructure.persistence.entity.OrderDetailJpaEntity;
import com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper cho OrderDetail và OrderDetailJpaEntity
 */
@Component
public class OrderDetailPersistenceMapper {

    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domain Domain entity
     * @return JPA entity
     */
    public OrderDetailJpaEntity mapToJpaEntity(OrderDetail domain) {
        if (domain == null) {
            return null;
        }

        return OrderDetailJpaEntity.builder()
                .id(domain.getId().getValue())
                .orderId(domain.getOrderId().getValue())
                .price(BigDecimalValidator.validateAndScale(domain.getPrice().getValue(), "price"))
                .amount(BigDecimalValidator.validateAndScale(domain.getAmount(), "amount"))
                .turnover(BigDecimalValidator.validateAndScale(domain.getTurnover().getValue(), "turnover"))
                .fee(BigDecimalValidator.validateAndScale(domain.getFee().getValue(), "fee"))
                .time(domain.getTime())
                .contractId(domain.getContractId())
                .leverage(BigDecimalValidator.validateAndScale(domain.getLeverage(), "leverage"))
                .marginMode(domain.getMarginMode())
                .fundingFee(BigDecimalValidator.validateAndScale(domain.getFundingFee().getValue(), "fundingFee"))
                .build();
    }

    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param jpaEntity JPA entity
     * @return Domain entity
     */
    public OrderDetail mapToDomainEntity(OrderDetailJpaEntity jpaEntity) {
        if (jpaEntity == null) {
            return null;
        }

        return OrderDetail.builder()
                .id(OrderDetailId.of(jpaEntity.getId()))
                .orderId(OrderId.of(jpaEntity.getOrderId()))
                .price(Money.of(jpaEntity.getPrice()))
                .amount(jpaEntity.getAmount())
                .turnover(Money.of(jpaEntity.getTurnover()))
                .fee(Money.of(jpaEntity.getFee()))
                .time(jpaEntity.getTime())
                .contractId(jpaEntity.getContractId())
                .leverage(jpaEntity.getLeverage())
                .marginMode(jpaEntity.getMarginMode())
                .fundingFee(Money.of(jpaEntity.getFundingFee()))
                .build();
    }

    /**
     * Chuyển đổi từ danh sách JPA entity sang danh sách domain entity
     * @param jpaEntities Danh sách JPA entity
     * @return Danh sách domain entity
     */
    public List<OrderDetail> mapToDomainEntities(List<OrderDetailJpaEntity> jpaEntities) {
        if (jpaEntities == null) {
            return List.of();
        }

        return jpaEntities.stream()
                .map(this::mapToDomainEntity)
                .collect(Collectors.toList());
    }

    /**
     * Chuyển đổi từ danh sách domain entity sang danh sách JPA entity
     * @param domainEntities Danh sách domain entity
     * @return Danh sách JPA entity
     */
    public List<OrderDetailJpaEntity> mapToJpaEntities(List<OrderDetail> domainEntities) {
        if (domainEntities == null) {
            return List.of();
        }

        return domainEntities.stream()
                .map(this::mapToJpaEntity)
                .collect(Collectors.toList());
    }
}
