package com.icetea.lotus.job;

import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.entity.Coin;
import com.icetea.lotus.entity.Coinprotocol;
import com.icetea.lotus.entity.Withdraw;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.CoinprotocolService;
import com.icetea.lotus.service.WithdrawService;
import com.icetea.lotus.util.MessageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * Check user withdrawal application
 */
@Component
@Slf4j
public class CheckWithdrawJob {

    @Autowired
    private WithdrawService withdrawService;
    @Autowired
    private CoinprotocolService coinprotocolService;
    @Autowired
    private CoinService coinService;
    @Autowired
    private RestTemplate restTemplate;


    /**
     * Check once an hourly
     */
    @Scheduled(cron = "0 */2 * * * *")
    public void checkNewWithdrawApplication() {
        log.info("Start processing orders to be transferred");
        // Get coin orders
        List<Withdraw> list = withdrawService.findWithrawByStatus(1);
        if (list != null && list.size() > 0) {
            for (Withdraw withdraw : list) {
                log.info("deal with:::" + withdraw.getId());
                withdrawService.updateWithdrawStatus(withdraw.getId(), 2);
                handleWithdraw(withdraw);
            }
        }

    }

    private void handleWithdraw(Withdraw record) {
        if (record == null || record.getStatus() != 1) {
            return;
        }
        Coinprotocol protocol = coinprotocolService.findByProtocol(record.getProtocol());
        Long withdrawId = record.getId();
        try {
            String serviceName = "SERVICE-RPC-" + protocol.getSymbol().toUpperCase();
            String url = "http://" + serviceName + "/rpc/withdraw?address={1}&amount={2}&coinName={3}";

            Coin coin = coinService.findOne(record.getCoinname());

            log.info("coin = {}", coin.toString());
            if (coin != null && coin.getCanWithdraw() == BooleanEnum.IS_TRUE) {
                MessageResult result = restTemplate.getForObject(url,
                        MessageResult.class, record.getAddress(), record.getReal_money(), record.getCoinname());
                log.info("=========================rpc end================================");
                log.info("result = {}", result);
                if (result.getCode() == 0 && result.getData() != null) {
                    log.info("====================== Processing is successful, data updates business for txid ==================================");
                    //Processing successfully, data is txid, update business order
                    String txid = (String) result.getData();
                    withdrawService.withdrawSuccess(record, coin, txid);
                } else {
                    log.info("====================== Automatic transfer failed and converted to manual processing ==================================");
                    //Automatic transfer failed and converted to manual processing
                    withdrawService.updateWithdrawStatus(withdrawId, 3);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("auto withdraw failed,error={}", e.getMessage());
            log.info("======================Automatic transfer failed and converted to manual processing ==================================");
            //Automatic transfer failed and converted to manual processing
            withdrawService.updateWithdrawStatus(withdrawId, 3);
        }
    }


}