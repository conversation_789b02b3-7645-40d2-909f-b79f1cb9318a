<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.icetea.lotus.walletrpc</groupId>
    <artifactId>wallet-rpc</artifactId>
    <packaging>pom</packaging>
    <version>1.2</version>

    <modules>
        <module>rpc-common</module>
        <module>bitcoin</module>
        <module>usdt</module>
        <module>eth</module>
        <module>erc-token</module>
        <module>eth-support</module>
        <module>act</module>
        <module>ect</module>
<!--        <module>xrp</module>-->
        <module>bch</module>
        <module>ltc</module>
        <module>erc-eusdt</module>
        <module>bsv</module>
        <module>eos</module>
        <module>xmr</module>
		<module>btm</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>1.5.10.RELEASE</version>
        <relativePath/>
    </parent>

    <properties>
        <project-version>1.2</project-version>
        <commons-lang3.version>3.4</commons-lang3.version>
        <spring-cloud.version>Edgware.RELEASE</spring-cloud.version>
        <elasticsearch.version>7.10.0</elasticsearch.version>
        <logstash.version>6.6</logstash.version>
        <logback.version>1.2.6</logback.version>
        <springfox.version>2.9.2</springfox.version>
        <guava.version>31.0.1-jre</guava.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${springfox.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${springfox.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash.version}</version> <!-- Use the latest compatible version -->
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version> <!-- Ensure compatibility -->
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.31</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-devtools</artifactId>
                <version>1.5.9.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <optional>true</optional>
                <version>1.18.28</version>
            </dependency>
            <dependency>
                <groupId>com.icetea.lotus.walletrpc</groupId>
                <artifactId>rpc-common</artifactId>
                <version>${project-version}</version>
            </dependency>
            <dependency>
                <groupId>com.icetea.lotus.walletrpc</groupId>
                <artifactId>eth-support</artifactId>
                <version>${project-version}</version>
            </dependency>
            <dependency>
                <groupId>org.web3j</groupId>
                <artifactId>core</artifactId>
                <version>4.8.4</version>
            </dependency>
            <dependency>
                <groupId>com.mashape.unirest</groupId>
                <artifactId>unirest-java</artifactId>
                <version>1.4.9</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
