package com.icetea.lotus.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class SwaggerConfig {
    @Bean
    public OpenAPI customOpenAPI(
            @Value("${openapi.service.title}") String title,
            @Value("${openapi.service.version}") String version) {
        return new OpenAPI()
                .info(new Info()
                        .title(title)
                        .version(version)
                        .description("API Exchange API documentation"));
    }

    @Bean
    public GroupedOpenApi apiAdmin() {
        return GroupedOpenApi.builder()
                .group("admin")
                .packagesToScan("com.icetea.lotus.controller")
                .build();
    }
}
