#!/bin/bash

# Script để phân tích dependencies giữa các module trong cex-be
# Sử dụng: ./analyze-module-dependencies.sh

echo "=== Phân tích Dependencies giữa các Module CEX-BE ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Check if we're in the right directory
check_directory() {
    if [ ! -f "pom.xml" ] || [ ! -d "future-core" ]; then
        print_error "Script phải chạy từ root directory của cex-be project"
        exit 1
    fi
    print_success "Đang chạy từ root directory: $(pwd)"
}

# Analyze module structure
analyze_module_structure() {
    print_header "Cấu trúc Module"

    echo "Modules được tìm thấy:"
    for dir in */; do
        if [ -f "${dir}pom.xml" ]; then
            MODULE_NAME=$(basename "$dir")
            echo "  - $MODULE_NAME"

            # Check if it's a library or service
            if [ -f "${dir}src/main/java" ]; then
                MAIN_CLASS=$(find "${dir}src/main/java" -name "*Application.java" 2>/dev/null | head -1)
                if [ -n "$MAIN_CLASS" ]; then
                    echo "    Type: Service (có main class)"
                else
                    echo "    Type: Library (không có main class)"
                fi
            fi
        fi
    done
}

# Analyze future-core dependencies
analyze_future_core_dependencies() {
    print_header "Phân tích Dependencies trong Future-Core"

    FUTURE_CORE_DIR="future-core/src/main/java/com/icetea/lotus"

    if [ ! -d "$FUTURE_CORE_DIR" ]; then
        print_error "Không tìm thấy future-core source directory"
        return
    fi

    print_info "Phân tích các service trong future-core..."

    # Find all service classes
    echo "Core Trading Services:"
    find "$FUTURE_CORE_DIR/core/domain/service" -name "*.java" 2>/dev/null | while read file; do
        SERVICE_NAME=$(basename "$file" .java)
        echo "  - $SERVICE_NAME"
    done

    echo ""
    echo "Application Services:"
    find "$FUTURE_CORE_DIR/application/service" -name "*.java" 2>/dev/null | while read file; do
        SERVICE_NAME=$(basename "$file" .java)
        echo "  - $SERVICE_NAME"
    done

    echo ""
    echo "Infrastructure Services:"
    find "$FUTURE_CORE_DIR/infrastructure" -name "*Service*.java" 2>/dev/null | while read file; do
        SERVICE_NAME=$(basename "$file" .java)
        PACKAGE_PATH=$(dirname "$file" | sed "s|$FUTURE_CORE_DIR/||")
        echo "  - $SERVICE_NAME ($PACKAGE_PATH)"
    done
}

# Analyze market data related components
analyze_market_data_components() {
    print_header "Phân tích Market Data Components"

    FUTURE_CORE_DIR="future-core/src/main/java/com/icetea/lotus"

    print_info "Tìm kiếm các component liên quan đến Market Data..."

    # Search for market data related files
    echo "Market Data Services:"
    find "$FUTURE_CORE_DIR" -name "*Market*" -o -name "*Price*" -o -name "*KLine*" 2>/dev/null | while read file; do
        if [[ "$file" == *.java ]]; then
            COMPONENT_NAME=$(basename "$file" .java)
            PACKAGE_PATH=$(dirname "$file" | sed "s|$FUTURE_CORE_DIR/||")
            echo "  - $COMPONENT_NAME ($PACKAGE_PATH)"
        fi
    done

    echo ""
    echo "WebSocket Handlers:"
    find "$FUTURE_CORE_DIR" -name "*WebSocket*" -o -name "*Handler*" 2>/dev/null | while read file; do
        if [[ "$file" == *.java ]]; then
            COMPONENT_NAME=$(basename "$file" .java)
            PACKAGE_PATH=$(dirname "$file" | sed "s|$FUTURE_CORE_DIR/||")
            echo "  - $COMPONENT_NAME ($PACKAGE_PATH)"
        fi
    done
}

# Analyze wallet related components
analyze_wallet_components() {
    print_header "Phân tích Wallet Components"

    FUTURE_CORE_DIR="future-core/src/main/java/com/icetea/lotus"

    print_info "Tìm kiếm các component liên quan đến Wallet..."

    echo "Wallet Services:"
    find "$FUTURE_CORE_DIR" -name "*Wallet*" -o -name "*Transaction*" -o -name "*Balance*" 2>/dev/null | while read file; do
        if [[ "$file" == *.java ]]; then
            COMPONENT_NAME=$(basename "$file" .java)
            PACKAGE_PATH=$(dirname "$file" | sed "s|$FUTURE_CORE_DIR/||")
            echo "  - $COMPONENT_NAME ($PACKAGE_PATH)"
        fi
    done
}

# Analyze security components
analyze_security_components() {
    print_header "Phân tích Security Components"

    FUTURE_CORE_DIR="future-core/src/main/java/com/icetea/lotus"

    print_info "Tìm kiếm các component liên quan đến Security..."

    echo "Security Services:"
    find "$FUTURE_CORE_DIR" -name "*Security*" -o -name "*Auth*" -o -name "*Token*" -o -name "*Jwt*" 2>/dev/null | while read file; do
        if [[ "$file" == *.java ]]; then
            COMPONENT_NAME=$(basename "$file" .java)
            PACKAGE_PATH=$(dirname "$file" | sed "s|$FUTURE_CORE_DIR/||")
            echo "  - $COMPONENT_NAME ($PACKAGE_PATH)"
        fi
    done
}

# Analyze schedulers and background jobs
analyze_schedulers() {
    print_header "Phân tích Schedulers và Background Jobs"

    FUTURE_CORE_DIR="future-core/src/main/java/com/icetea/lotus"

    print_info "Tìm kiếm các scheduler và background job..."

    echo "Schedulers:"
    find "$FUTURE_CORE_DIR" -name "*Scheduler*" -o -name "*Job*" 2>/dev/null | while read file; do
        if [[ "$file" == *.java ]]; then
            COMPONENT_NAME=$(basename "$file" .java)
            PACKAGE_PATH=$(dirname "$file" | sed "s|$FUTURE_CORE_DIR/||")
            echo "  - $COMPONENT_NAME ($PACKAGE_PATH)"
        fi
    done
}

# Check cross-module dependencies
check_cross_module_dependencies() {
    print_header "Kiểm tra Cross-Module Dependencies"

    print_info "Phân tích pom.xml dependencies..."

    for module in */; do
        if [ -f "${module}pom.xml" ]; then
            MODULE_NAME=$(basename "$module")
            echo ""
            echo "Module: $MODULE_NAME"

            # Extract dependencies from pom.xml
            grep -A 2 -B 1 "com.icetea.lotus" "${module}pom.xml" 2>/dev/null | grep -E "(artifactId|groupId)" | while read line; do
                if [[ "$line" == *"artifactId"* ]]; then
                    ARTIFACT=$(echo "$line" | sed 's/.*<artifactId>\(.*\)<\/artifactId>.*/\1/')
                    if [[ "$ARTIFACT" != "$MODULE_NAME" ]]; then
                        echo "  → depends on: $ARTIFACT"
                    fi
                fi
            done
        fi
    done
}

# Generate migration recommendations
generate_migration_recommendations() {
    print_header "Đề xuất Migration (Updated)"

    echo "Dựa trên phân tích microservices hiện tại, đây là đề xuất tối ưu hóa:"
    echo ""

    print_success "Phase 1: Futures Market Data Services"
    echo "  - Di chuyển LastPriceService (futures) → market service"
    echo "  - Di chuyển IndexPriceService → market service"
    echo "  - Di chuyển MarkPriceService → market service"
    echo "  - Di chuyển KLineManagementService (futures) → market service"
    echo "  - Tạo unified market data API cho cả spot và futures"
    echo ""

    print_success "Phase 2: Background Schedulers"
    echo "  - Di chuyển LiquidationScheduler → admin service"
    echo "  - Di chuyển FundingScheduler → admin service"
    echo "  - Di chuyển SettlementScheduler → admin service"
    echo "  - Di chuyển HealthCheckScheduler → admin service"
    echo ""

    print_success "Phase 3: Futures Wallet Operations"
    echo "  - Di chuyển futures balance management → wallet service"
    echo "  - Di chuyển margin calculations → wallet service"
    echo "  - Di chuyển futures transaction recording → wallet service"
    echo ""

    print_success "Phase 4: Trading Notifications"
    echo "  - Di chuyển trading notifications → chat service"
    echo "  - Enhance real-time trading alerts"
    echo "  - Centralize email/SMS notifications"
    echo ""

    print_warning "Deprecated Modules (không migration):"
    echo "  - jobs module: ❌ Đã quyết định bỏ không dùng"
    echo "  - netty module: ❌ Đã quyết định bỏ không dùng"
    echo "  - hot-wallet: ⏸️ Tạm thời không phát triển"
}

# Check for potential issues
check_potential_issues() {
    print_header "Kiểm tra Potential Issues"

    print_info "Tìm kiếm circular dependencies..."

    # Check for circular dependencies in future-core
    FUTURE_CORE_DIR="future-core/src/main/java/com/icetea/lotus"

    echo "Checking for circular imports..."
    find "$FUTURE_CORE_DIR" -name "*.java" -exec grep -l "import.*com\.icetea\.lotus" {} \; 2>/dev/null | head -10 | while read file; do
        echo "  - $(basename "$file")"
    done

    echo ""
    print_info "Tìm kiếm tightly coupled components..."

    # Look for classes with many dependencies
    find "$FUTURE_CORE_DIR" -name "*.java" -exec grep -c "import.*com\.icetea\.lotus" {} \; 2>/dev/null | sort -nr | head -5 | while read count file; do
        if [ "$count" -gt 10 ]; then
            print_warning "$(basename "$file") có $count internal imports (có thể tightly coupled)"
        fi
    done
}

# Generate summary report
generate_summary_report() {
    print_header "Tóm tắt Phân tích"

    echo "Kết quả phân tích module dependencies:"
    echo ""

    # Count components by category
    FUTURE_CORE_DIR="future-core/src/main/java/com/icetea/lotus"

    MARKET_COMPONENTS=$(find "$FUTURE_CORE_DIR" -name "*Market*" -o -name "*Price*" -o -name "*KLine*" 2>/dev/null | wc -l)
    WALLET_COMPONENTS=$(find "$FUTURE_CORE_DIR" -name "*Wallet*" -o -name "*Transaction*" -o -name "*Balance*" 2>/dev/null | wc -l)
    WEBSOCKET_COMPONENTS=$(find "$FUTURE_CORE_DIR" -name "*WebSocket*" -o -name "*Handler*" 2>/dev/null | wc -l)
    SECURITY_COMPONENTS=$(find "$FUTURE_CORE_DIR" -name "*Security*" -o -name "*Auth*" -o -name "*Token*" 2>/dev/null | wc -l)
    SCHEDULER_COMPONENTS=$(find "$FUTURE_CORE_DIR" -name "*Scheduler*" -o -name "*Job*" 2>/dev/null | wc -l)

    echo "Components có thể di chuyển:"
    echo "  - Market Data: $MARKET_COMPONENTS components"
    echo "  - Wallet: $WALLET_COMPONENTS components"
    echo "  - WebSocket: $WEBSOCKET_COMPONENTS components"
    echo "  - Security: $SECURITY_COMPONENTS components"
    echo "  - Schedulers: $SCHEDULER_COMPONENTS components"
    echo ""

    TOTAL_MOVABLE=$((MARKET_COMPONENTS + WALLET_COMPONENTS + WEBSOCKET_COMPONENTS + SECURITY_COMPONENTS + SCHEDULER_COMPONENTS))
    echo "Tổng cộng: $TOTAL_MOVABLE components có thể di chuyển ra khỏi future-core"
    echo ""

    print_success "Việc tách module sẽ giúp:"
    echo "  - Giảm complexity của future-core"
    echo "  - Tăng maintainability"
    echo "  - Cải thiện scalability"
    echo "  - Tạo clear separation of concerns"
}

# Main execution
main() {
    echo "Bắt đầu phân tích module dependencies..."
    echo "================================================"

    check_directory
    echo ""

    analyze_module_structure
    echo ""

    analyze_future_core_dependencies
    echo ""

    analyze_market_data_components
    echo ""

    analyze_wallet_components
    echo ""

    analyze_security_components
    echo ""

    analyze_schedulers
    echo ""

    check_cross_module_dependencies
    echo ""

    check_potential_issues
    echo ""

    generate_migration_recommendations
    echo ""

    generate_summary_report
    echo ""

    echo "================================================"
    echo "Phân tích hoàn thành!"

    print_info "Để xem chi tiết migration plan:"
    echo "  - docs/module-analysis-and-refactoring.md"
    echo "  - docs/phase1-extract-market-data-services.md"
}

# Run main function
main
