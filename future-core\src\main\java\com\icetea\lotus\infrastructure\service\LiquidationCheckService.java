package com.icetea.lotus.infrastructure.service;

import com.icetea.lotus.core.domain.entity.Contract;
import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.PositionStatus;
import com.icetea.lotus.core.domain.repository.ContractRepository;
import com.icetea.lotus.core.domain.repository.PositionRepository;
import com.icetea.lotus.core.domain.service.LiquidationService;
import com.icetea.lotus.core.domain.service.PriceManagementService;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.service.PositionDiagnosticService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service kiểm tra thanh lý vị thế
 * Chạy định kỳ để kiểm tra các vị thế cần thanh lý
 */
@Slf4j
@Service
public class LiquidationCheckService {

    private final PositionRepository positionRepository;
    private final ContractRepository contractRepository;
    private final LiquidationService liquidationService;
    private final PriceManagementService priceManagementService;
    private final PositionDiagnosticService positionDiagnosticService;

    @Autowired
    public LiquidationCheckService(
            @Qualifier("positionRepositoryAdapter") PositionRepository positionRepository,
            ContractRepository contractRepository,
            LiquidationService liquidationService,
            PriceManagementService priceManagementService,
            PositionDiagnosticService positionDiagnosticService) {
        this.positionRepository = positionRepository;
        this.contractRepository = contractRepository;
        this.liquidationService = liquidationService;
        this.priceManagementService = priceManagementService;
        this.positionDiagnosticService = positionDiagnosticService;
    }

    @Value("${contract.liquidation.check-interval:5000}")
    private long checkInterval;

    @Value("${contract.liquidation.max-positions-per-batch:100}")
    private int maxPositionsPerBatch;

    @Value("${contract.liquidation.enabled:true}")
    private boolean liquidationEnabled;

    /**
     * Kiểm tra thanh lý cho một symbol cụ thể
     * @param symbol Symbol cần kiểm tra
     */
    @Transactional
    public void checkLiquidationForSymbol(String symbol) {
        if (!liquidationEnabled) {
            log.debug("Thanh lý vị thế đã bị vô hiệu hóa trong cấu hình");
            return;
        }

        log.info("Kiểm tra thanh lý cho symbol: {}", symbol);

        try {
            // Lấy giá đánh dấu hiện tại
            Money markPrice = priceManagementService.getCurrentMarkPrice(Symbol.of(symbol));
            if (markPrice == null || markPrice.getValue().compareTo(java.math.BigDecimal.ZERO) <= 0) {
                log.warn("Không có giá đánh dấu hợp lệ cho symbol: {}", symbol);
                return;
            }

            // Lấy hợp đồng
            Optional<Contract> contractOpt = contractRepository.findBySymbolOptional(Symbol.of(symbol));
            if (contractOpt.isEmpty()) {
                log.warn("Không tìm thấy hợp đồng cho symbol: {}", symbol);
                return;
            }
            Contract contract = contractOpt.get();

            // Lấy các vị thế đang mở cho symbol này
            List<Position> riskPositions = positionRepository.findAllBySymbolAndStatus(Symbol.of(symbol), PositionStatus.OPEN);

            if (riskPositions.isEmpty()) {
                log.debug("Không có vị thế đang mở nào cho symbol: {}", symbol);
                return;
            }

            // Giới hạn số lượng vị thế kiểm tra trong một lần
            if (riskPositions.size() > maxPositionsPerBatch) {
                riskPositions = riskPositions.subList(0, maxPositionsPerBatch);
            }

            if (riskPositions.isEmpty()) {
                log.debug("Không có vị thế nào có nguy cơ cao cho symbol: {}", symbol);
                return;
            }

            log.debug("Kiểm tra {} vị thế có nguy cơ cao cho symbol: {}", riskPositions.size(), symbol);

            // Kiểm tra từng vị thế
            for (Position position : riskPositions) {
                checkAndLiquidatePositionIfNeeded(position, markPrice, contract, symbol);
            }

            log.info("Đã hoàn thành kiểm tra thanh lý cho symbol: {}", symbol);
        } catch (Exception e) {
            log.error("Lỗi khi kiểm tra thanh lý cho symbol: {}", symbol, e);
        }
    }

    /**
     * Kiểm tra và thanh lý vị thế nếu cần
     * @param position Vị thế cần kiểm tra
     * @param markPrice Giá đánh dấu hiện tại
     * @param contract Hợp đồng
     * @param symbol Symbol
     */
    private void checkAndLiquidatePositionIfNeeded(Position position, Money markPrice, Contract contract, String symbol) {
        try {
            // Kiểm tra xem vị thế có cần thanh lý không
            boolean needsLiquidation = liquidationService.needsLiquidation(position, markPrice, contract);

            if (needsLiquidation) {
                log.warn("Vị thế cần thanh lý, positionId = {}, memberId = {}, symbol = {}, markPrice = {}, margin = {}, maintenanceMarginRatio = {}",
                        position.getId(), position.getMemberId(), symbol, markPrice,
                        position.getMargin(), contract.getMaintenanceMarginRatio());

                liquidatePosition(position, markPrice, contract, symbol);
            }
        } catch (Exception e) {
            log.error("Lỗi khi kiểm tra thanh lý cho vị thế, positionId = {}, memberId = {}, symbol = {}",
                    position.getId(), position.getMemberId(), symbol, e);
        }
    }

    /**
     * Thanh lý vị thế
     * @param position Vị thế cần thanh lý
     * @param markPrice Giá đánh dấu hiện tại
     * @param contract Hợp đồng
     * @param symbol Symbol
     */
    private void liquidatePosition(Position position, Money markPrice, Contract contract, String symbol) {
        try {
            // Chẩn đoán vị thế trước khi thanh lý
            positionDiagnosticService.logPositionDetails(position, markPrice);

            if (!positionDiagnosticService.canLiquidate(position, markPrice)) {
                log.warn("Vị thế không thể thanh lý theo kết quả chẩn đoán, positionId = {}, memberId = {}, symbol = {}",
                        position.getId(), position.getMemberId(), symbol);
                return;
            }

            // Log thông tin trước khi thanh lý
            log.info("Bắt đầu thanh lý vị thế, positionId = {}, memberId = {}, symbol = {}, status = {}, volume = {}, margin = {}, markPrice = {}",
                    position.getId(), position.getMemberId(), symbol, position.getStatus(),
                    position.getVolume(), position.getMargin() != null ? position.getMargin().getValue() : null, markPrice.getValue());

            // Kiểm tra trạng thái vị thế trước khi thanh lý
            if (position.getStatus() != null && position.getStatus() != PositionStatus.OPEN) {
                log.warn("Vị thế không ở trạng thái OPEN, bỏ qua thanh lý, positionId = {}, memberId = {}, symbol = {}, status = {}",
                        position.getId(), position.getMemberId(), symbol, position.getStatus());
                return;
            }

            // Thanh lý vị thế
            Position liquidatedPosition = liquidationService.liquidatePosition(position, markPrice, contract);

            // Kiểm tra xem liquidatedPosition có null không
            if (liquidatedPosition == null) {
                log.error("Thanh lý vị thế thất bại - liquidatedPosition trả về null, " +
                        "positionId = {}, memberId = {}, symbol = {}, originalStatus = {}, volume = {}, margin = {}",
                        position.getId(), position.getMemberId(), symbol, position.getStatus(),
                        position.getVolume(), position.getMargin() != null ? position.getMargin().getValue() : null);

                // Có thể cần cập nhật trạng thái vị thế để tránh thanh lý lại
                markPositionAsLiquidationFailed(position);
                return;
            }

            // Kiểm tra số tiền ký quỹ còn lại
            if (liquidatedPosition.getRemainingMargin() != null) {
                if (liquidatedPosition.getRemainingMargin().isLessThan(Money.ZERO)) {
                    log.warn("Vị thế thanh lý cần sử dụng quỹ bảo hiểm, positionId = {}, memberId = {}, symbol = {}, amount = {}",
                            liquidatedPosition.getId(), liquidatedPosition.getMemberId(), symbol,
                            liquidatedPosition.getRemainingMargin().getValue().abs());
                } else if (liquidatedPosition.getRemainingMargin().isGreaterThan(Money.ZERO)) {
                    log.info("Vị thế thanh lý có số tiền ký quỹ còn lại, positionId = {}, memberId = {}, symbol = {}, amount = {}",
                            liquidatedPosition.getId(), liquidatedPosition.getMemberId(), symbol,
                            liquidatedPosition.getRemainingMargin().getValue());
                }
            }

            log.info("Đã thanh lý vị thế thành công, positionId = {}, memberId = {}, symbol = {}, newStatus = {}",
                    liquidatedPosition.getId(), liquidatedPosition.getMemberId(), symbol, liquidatedPosition.getStatus());
        } catch (Exception e) {
            log.error("Lỗi khi thanh lý vị thế, positionId = {}, memberId = {}, symbol = {}, errorType = {}, errorMessage = {}",
                    position.getId(), position.getMemberId(), symbol, e.getClass().getSimpleName(), e.getMessage(), e);
        }
    }

    /**
     * Đánh dấu vị thế là thanh lý thất bại để tránh thanh lý lại
     * @param position Vị thế
     */
    private void markPositionAsLiquidationFailed(Position position) {
        try {
            // Có thể cập nhật status hoặc thêm remark để tránh thanh lý lại
            log.warn("Đánh dấu vị thế thanh lý thất bại, positionId = {}, memberId = {}, symbol = {}",
                    position.getId(), position.getMemberId(), position.getSymbol().getValue());

            // TODO: Implement logic để đánh dấu position hoặc ghi vào blacklist
            // Ví dụ: có thể set status thành LIQUIDATION_FAILED hoặc thêm vào cache
        } catch (Exception e) {
            log.error("Lỗi khi đánh dấu vị thế thanh lý thất bại, positionId = {}", position.getId(), e);
        }
    }

    /**
     * Kiểm tra thanh lý cho tất cả các symbol
     * Chạy định kỳ theo cấu hình contract.liquidation.check-interval
     */
    @Scheduled(fixedRateString = "${contract.liquidation.check-interval:5000}")
    public void checkLiquidationForAllSymbols() {
        if (!liquidationEnabled) {
            log.debug("Thanh lý vị thế đã bị vô hiệu hóa trong cấu hình");
            return;
        }

        log.debug("Bắt đầu kiểm tra thanh lý cho tất cả các symbol");

        try {
            // Lấy tất cả các hợp đồng và giá hiện tại trong một lần truy vấn
            List<Contract> contracts = contractRepository.findAll();

            // Lấy giá hiện tại cho tất cả các symbol
            java.util.Map<Symbol, Money> currentPrices = new java.util.HashMap<>();
            for (Contract contract : contracts) {
                Symbol symbol = contract.getSymbol();
                Money markPrice = priceManagementService.getCurrentMarkPrice(symbol);
                if (markPrice != null && markPrice.getValue().compareTo(java.math.BigDecimal.ZERO) > 0) {
                    currentPrices.put(symbol, markPrice);
                }
            }

            // Lấy tất cả các vị thế đang mở
            List<Position> allRiskPositions = positionRepository.findAllByStatus(PositionStatus.OPEN);

            // Nhóm các vị thế theo symbol
            java.util.Map<Symbol, java.util.List<Position>> positionsBySymbol = allRiskPositions.stream()
                .collect(java.util.stream.Collectors.groupingBy(Position::getSymbol));

            // Kiểm tra từng symbol
            processContracts(contracts, currentPrices, positionsBySymbol);

            log.debug("Đã hoàn thành kiểm tra thanh lý cho tất cả các symbol");
        } catch (Exception e) {
            log.error("Lỗi khi kiểm tra thanh lý cho tất cả các symbol", e);
        }
    }

    /**
     * Xử lý danh sách các hợp đồng để kiểm tra thanh lý
     * @param contracts Danh sách các hợp đồng
     * @param currentPrices Map chứa giá hiện tại cho mỗi symbol
     * @param positionsBySymbol Map chứa danh sách vị thế cho mỗi symbol
     */
    private void processContracts(List<Contract> contracts,
                                 java.util.Map<Symbol, Money> currentPrices,
                                 java.util.Map<Symbol, java.util.List<Position>> positionsBySymbol) {
        // Lọc các hợp đồng có giá hợp lệ và có vị thế
        contracts.forEach(contract -> {
            Symbol symbol = contract.getSymbol();
            Money markPrice = currentPrices.get(symbol);

            // Kiểm tra điều kiện
            if (markPrice == null) {
                log.warn("Không có giá đánh dấu hợp lệ cho symbol: {}", symbol.getValue());
                return; // Tương đương với continue trong vòng lặp for
            }

            // Lấy danh sách vị thế cho symbol này
            java.util.List<Position> symbolPositions = positionsBySymbol.getOrDefault(symbol, java.util.Collections.emptyList());
            if (symbolPositions.isEmpty()) {
                log.debug("Không có vị thế nào cho symbol: {}", symbol.getValue());
                return; // Tương đương với continue trong vòng lặp for
            }

            // Xử lý các vị thế cho symbol này
            processPositionsForSymbol(symbolPositions, markPrice, contract, symbol);
        });
    }

    /**
     * Xử lý danh sách các vị thế cho một symbol
     * @param positions Danh sách các vị thế
     * @param markPrice Giá đánh dấu hiện tại
     * @param contract Hợp đồng
     * @param symbol Symbol
     */
    private void processPositionsForSymbol(java.util.List<Position> positions, Money markPrice, Contract contract, Symbol symbol) {
        // Giới hạn số lượng vị thế kiểm tra trong một lần
        java.util.List<Position> positionsToCheck = positions;
        if (positions.size() > maxPositionsPerBatch) {
            positionsToCheck = positions.subList(0, maxPositionsPerBatch);
        }

        log.debug("Kiểm tra {} vị thế cho symbol: {}", positionsToCheck.size(), symbol.getValue());

        // Kiểm tra từng vị thế
        for (Position position : positionsToCheck) {
            checkAndLiquidatePositionIfNeeded(position, markPrice, contract, symbol.getValue());
        }
    }
}
