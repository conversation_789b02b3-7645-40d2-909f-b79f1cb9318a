package com.icetea.lotus;

import com.icetea.lotus.config.EnableCexResourceServer;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.commons.util.InetUtils;
import org.springframework.cloud.commons.util.InetUtilsProperties;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableScheduling
@SpringBootApplication
@EnableDiscoveryClient
@EnableCexResourceServer
public class ExchangeApiApplication implements CommandLineRunner {
    public static void main(String[] args) {
        SpringApplication.run(ExchangeApiApplication.class, args);
    }

    @Override
    public void run(String... strings) throws Exception {
        InetUtils inetUtils = new InetUtils(new InetUtilsProperties());
        System.out.println("Detected IP: " + inetUtils.findFirstNonLoopbackHostInfo().getIpAddress());
    }
}
