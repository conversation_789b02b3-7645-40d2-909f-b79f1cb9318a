package com.icetea.lotus.infrastructure.persistence.repository;

import com.icetea.lotus.core.domain.entity.OrderDirection;
import com.icetea.lotus.core.domain.entity.OrderStatus;
import com.icetea.lotus.infrastructure.persistence.entity.OrderJpaEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * JPA Repository cho OrderJpaEntity
 */
@Repository
public interface OrderJpaRepository extends JpaRepository<OrderJpaEntity, String>, JpaSpecificationExecutor<OrderJpaEntity> {

    /**
     * T<PERSON><PERSON> tất cả các lệnh theo memberId
     * @param memberId ID của thành viên
     * @return Danh sách các l<PERSON>nh
     */
    List<OrderJpaEntity> findAllByMemberId(Long memberId);

    /**
     * Tìm tất cả các lệnh theo memberId với phân trang
     * @param memberId ID của thành viên
     * @param pageable Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    Page<OrderJpaEntity> findAllByMemberId(Long memberId, Pageable pageable);

    /**
     * Tìm tất cả các lệnh theo symbol
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các lệnh
     */
    List<OrderJpaEntity> findAllBySymbol(String symbol);

    /**
     * Tìm tất cả các lệnh theo symbol với phân trang
     * @param symbol Symbol của hợp đồng
     * @param pageable Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    Page<OrderJpaEntity> findAllBySymbol(String symbol, Pageable pageable);

    /**
     * Tìm tất cả các lệnh theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các lệnh
     */
    List<OrderJpaEntity> findAllByMemberIdAndSymbol(Long memberId, String symbol);

    /**
     * Tìm tất cả các lệnh theo memberId và symbol với phân trang
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param pageable Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    Page<OrderJpaEntity> findAllByMemberIdAndSymbol(Long memberId, String symbol, Pageable pageable);

    /**
     * Tìm tất cả các lệnh theo status
     * @param status Trạng thái của lệnh
     * @return Danh sách các lệnh
     */
    List<OrderJpaEntity> findAllByStatus(String status);

    /**
     * Tìm tất cả các lệnh theo status với phân trang
     * @param status Trạng thái của lệnh
     * @param pageable Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    Page<OrderJpaEntity> findAllByStatus(String status, Pageable pageable);

    /**
     * Tìm tất cả các lệnh theo symbol và status
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của lệnh
     * @return Danh sách các lệnh
     */
    List<OrderJpaEntity> findBySymbolAndStatus(String symbol, OrderStatus status);

    /**
     * Tìm tất cả các lệnh theo symbol và status với phân trang
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của lệnh
     * @param pageable Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    Page<OrderJpaEntity> findBySymbolAndStatus(String symbol, OrderStatus status, Pageable pageable);

    /**
     * Tìm tất cả các lệnh theo memberId và status
     * @param memberId ID của thành viên
     * @param status Trạng thái của lệnh
     * @return Danh sách các lệnh
     */
    List<OrderJpaEntity> findAllByMemberIdAndStatus(Long memberId, String status);

    /**
     * Tìm tất cả các lệnh theo memberId và status với phân trang
     * @param memberId ID của thành viên
     * @param status Trạng thái của lệnh
     * @param pageable Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    Page<OrderJpaEntity> findAllByMemberIdAndStatus(Long memberId, String status, Pageable pageable);

    /**
     * Tìm tất cả các lệnh theo memberId, symbol và status
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của lệnh
     * @return Danh sách các lệnh
     */
    List<OrderJpaEntity> findByMemberIdAndSymbolAndStatus(Long memberId, String symbol, String status);

    /**
     * Tìm tất cả các lệnh theo memberId, symbol và status với phân trang
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của lệnh
     * @param pageable Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    Page<OrderJpaEntity> findByMemberIdAndSymbolAndStatus(Long memberId, String symbol, String status, Pageable pageable);

    /**
     * Tìm tất cả các lệnh theo khoảng thời gian
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách các lệnh
     */
    List<OrderJpaEntity> findAllByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Tìm tất cả các lệnh theo khoảng thời gian với phân trang
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @param pageable Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    Page<OrderJpaEntity> findAllByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * Tìm tất cả các lệnh theo symbol và khoảng thời gian
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách các lệnh
     */
    List<OrderJpaEntity> findAllBySymbolAndCreateTimeBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Tìm tất cả các lệnh theo symbol và khoảng thời gian với phân trang
     * @param symbol Symbol của hợp đồng
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @param pageable Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    Page<OrderJpaEntity> findAllBySymbolAndCreateTimeBetween(String symbol, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * Tìm tất cả các lệnh theo memberId và khoảng thời gian
     * @param memberId ID của thành viên
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách các lệnh
     */
    List<OrderJpaEntity> findAllByMemberIdAndCreateTimeBetween(Long memberId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Tìm tất cả các lệnh theo memberId và khoảng thời gian với phân trang
     * @param memberId ID của thành viên
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @param pageable Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    Page<OrderJpaEntity> findAllByMemberIdAndCreateTimeBetween(Long memberId, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * Tìm tất cả các lệnh theo status và thời gian hết hạn
     * @param status Trạng thái của lệnh
     * @param expireTime Thời gian hết hạn
     * @return Danh sách các lệnh
     */
    List<OrderJpaEntity> findByStatusAndExpireTimeBefore(OrderStatus status, LocalDateTime expireTime);

    /**
     * Tìm tất cả các lệnh theo status và thời gian hết hạn với phân trang
     * @param status Trạng thái của lệnh
     * @param expireTime Thời gian hết hạn
     * @param pageable Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    Page<OrderJpaEntity> findByStatusAndExpireTimeBefore(OrderStatus status, LocalDateTime expireTime, Pageable pageable);

    /**
     * Tìm tất cả các lệnh theo memberId, symbol và danh sách status
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param statuses Danh sách trạng thái lệnh
     * @return Danh sách các lệnh
     */
    List<OrderJpaEntity> findAllByMemberIdAndSymbolAndStatusIn(Long memberId, String symbol, List<String> statuses);

    /**
     * Tìm tất cả các lệnh theo memberId, symbol và danh sách status với phân trang
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param statuses Danh sách trạng thái lệnh
     * @param pageable Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    Page<OrderJpaEntity> findAllByMemberIdAndSymbolAndStatusIn(Long memberId, String symbol, List<String> statuses, Pageable pageable);

    /**
     * Cập nhật trạng thái lệnh
     * @param orderId ID của lệnh
     * @param status Trạng thái mới
     * @return Số lượng bản ghi đã cập nhật
     */
    @Modifying
    @Query("update OrderJpaEntity o set o.status = :status where o.orderId = :orderId")
    int updateStatus(@Param("orderId") String orderId, @Param("status") String status);

    /**
     * Cập nhật khối lượng đã giao dịch
     * @param orderId ID của lệnh
     * @param dealVolume Khối lượng đã giao dịch
     * @param dealMoney Số tiền đã giao dịch
     * @return Số lượng bản ghi đã cập nhật
     */
    @Modifying
    @Query("update OrderJpaEntity o set o.dealVolume = :dealVolume, o.dealMoney = :dealMoney where o.orderId = :orderId")
    int updateDealAmount(@Param("orderId") String orderId, @Param("dealVolume") java.math.BigDecimal dealVolume, @Param("dealMoney") java.math.BigDecimal dealMoney);

    /**
     * Tìm tất cả các lệnh theo symbol, direction và status
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của lệnh
     * @param status Trạng thái của lệnh
     * @return Danh sách các lệnh
     */
    List<OrderJpaEntity> findAllBySymbolAndDirectionAndStatus(String symbol, OrderDirection direction, OrderStatus status);

    /**
     * Tìm tất cả các lệnh theo symbol, direction và status với phân trang
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của lệnh
     * @param status Trạng thái của lệnh
     * @param pageable Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    Page<OrderJpaEntity> findAllBySymbolAndDirectionAndStatus(String symbol, OrderDirection direction, OrderStatus status, Pageable pageable);

    /**
     * Tìm tất cả các lệnh theo symbol, status và danh sách type
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái của lệnh
     * @param types Danh sách loại lệnh
     * @return Danh sách các lệnh
     */
    List<OrderJpaEntity> findAllBySymbolAndStatusAndTypeIn(String symbol, OrderStatus status, List<String> types);

    /**
     * Tìm tất cả các lệnh theo symbol, direction và status, sắp xếp theo giá giảm dần
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của lệnh
     * @param status Trạng thái của lệnh
     * @return Danh sách các lệnh
     */
    List<OrderJpaEntity> findAllBySymbolAndDirectionAndStatusOrderByPriceDesc(String symbol, OrderDirection direction, OrderStatus status);

    /**
     * Tìm tất cả các lệnh theo symbol, direction và status, sắp xếp theo giá tăng dần
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của lệnh
     * @param status Trạng thái của lệnh
     * @return Danh sách các lệnh
     */
    List<OrderJpaEntity> findAllBySymbolAndDirectionAndStatusOrderByPriceAsc(String symbol, OrderDirection direction, OrderStatus status);

    /**
     * Tìm tất cả các lệnh theo status và thời gian thực hiện
     * @param status Trạng thái của lệnh
     * @param executeTime Thời gian thực hiện
     * @return Danh sách các lệnh
     */
    @Query("SELECT o FROM OrderJpaEntity o WHERE o.status = :status AND o.executeTime IS NOT NULL AND o.executeTime < :executeTime")
    List<OrderJpaEntity> findByStatusAndExecuteTimeBefore(@Param("status") OrderStatus status, @Param("executeTime") LocalDateTime executeTime);

    /**
     * Lấy danh sách order book với phân trang
     * @param pageable Thông tin phân trang
     * @return Trang chứa các lệnh
     */
    Page<OrderJpaEntity> findBySymbolAndStatusInAndDirectionOrderByPriceDesc(String symbol, List<OrderStatus> status,
                                                                             OrderDirection direction, Pageable pageable);

}
