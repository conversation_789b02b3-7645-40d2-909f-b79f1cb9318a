package com.icetea.lotus.infrastructure.api.controller;

import com.icetea.lotus.application.dto.OrderBookDto;
import com.icetea.lotus.application.dto.OrderDto;
import com.icetea.lotus.application.dto.PlaceOrderCommand;
import com.icetea.lotus.application.dto.PlaceOrderResult;
import com.icetea.lotus.application.port.input.ManageOrderMatchingUseCase;
import com.icetea.lotus.application.port.input.PlaceOrderUseCase;
import com.icetea.lotus.core.domain.entity.AuthMember;
import com.icetea.lotus.infrastructure.api.dto.ApiResponse;
import com.icetea.lotus.infrastructure.api.request.FindOrderRequest;
import com.icetea.lotus.infrastructure.api.request.PlaceOrderRequest;
import com.icetea.lotus.infrastructure.api.validator.ApiInputValidator;
import com.icetea.lotus.infrastructure.config.security.CurrentUser;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.ratelimiter.annotation.RateLimiter;
import io.github.resilience4j.retry.annotation.Retry;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Controller tổng hợp cho quản lý giao dịch
 * Kết hợp chức năng của OrderController và OrderMatchingController
 */
@RestController
@RequestMapping("/api/v1/trading")
@RequiredArgsConstructor
@Slf4j
public class TradingController {

    private final PlaceOrderUseCase placeOrderUseCase;
    private final ManageOrderMatchingUseCase manageOrderMatchingUseCase;
    private final ApiInputValidator inputValidator;

    //
    // Các API từ OrderController
    //

    /**
     * Lấy thông tin các order-book
     *
     * @param request Request chứa thông tin tìm kiếm
     * @return Danh sách lệnh
     */
    @PostMapping("/orders/search")
    @CircuitBreaker(name = "orderService", fallbackMethod = "placeOrderFallback")
    @RateLimiter(name = "orderService")
    @Retry(name = "orderService")
    public ResponseEntity<ApiResponse<OrderBookDto>> fetchOpenOrders(@Valid @RequestBody FindOrderRequest request) {
        log.info("fetchOpenOrders==> FindOrderRequest: request = {}", request);
        return ResponseEntity.ok(ApiResponse.success(placeOrderUseCase.findOrder(request)));
    }

    /**
     * Đặt lệnh mới
     *
     * @param request Request chứa thông tin lệnh
     * @return Kết quả đặt lệnh
     */
    @PostMapping("/orders")
    @CircuitBreaker(name = "orderService", fallbackMethod = "placeOrderFallback")
    @RateLimiter(name = "orderService")
    @Retry(name = "orderService")
    public ResponseEntity<ApiResponse<PlaceOrderResult>> placeOrder(
            @Valid @RequestBody PlaceOrderRequest request,
            @CurrentUser AuthMember user) {

        log.info("Nhận yêu cầu đặt lệnh: {}", request);

        // Xác thực dữ liệu đầu vào bổ sung
        if (!inputValidator.isValidMemberId(user.getId())) {
            log.warn("Đặt lệnh thất bại: MemberId không hợp lệ, memberId = {}", user.getId());
            return ResponseEntity.badRequest().body(ApiResponse.error("MemberId không hợp lệ"));
        }

        if (!inputValidator.isValidSymbol(request.getSymbol())) {
            log.warn("Đặt lệnh thất bại: Symbol không hợp lệ, symbol = {}, định dạng symbol = {}",
                    request.getSymbol(),
                    request.getSymbol() != null ? request.getSymbol().getClass().getName() : "null");
            return ResponseEntity.badRequest().body(ApiResponse.error("Symbol không hợp lệ"));
        }

        try {
            // Chuyển đổi từ request sang command
            PlaceOrderCommand command = PlaceOrderCommand.builder()
                    .memberId(user.getId())
                    .symbol(request.getSymbol())
                    .direction(request.getDirection())
                    .type(request.getType())
                    .price(request.getPrice())
                    .triggerPrice(request.getTriggerPrice())
                    .volume(request.getVolume())
                    .timeInForce(request.getTimeInForce())
                    .expireTime(request.getExpireTime())
                    .leverage(request.getLeverage())
                    .reduceOnly(request.getReduceOnly())
                    .callbackRate(request.getCallbackRate())
                    .postOnly(request.getPostOnly())
                    .maxSlippage(request.getMaxSlippage())
                    .fillOrKill(request.getFillOrKill())
                    .immediateOrCancel(request.getImmediateOrCancel())
                    .build();

            log.info("Đã chuyển đổi request thành command: {}", command);

            // Đặt lệnh
            log.info("Gọi placeOrderUseCase.placeOrder với command: {}", command);
            PlaceOrderResult result = placeOrderUseCase.placeOrder(command);
            log.info("Kết quả đặt lệnh: {}", result);

            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("Đặt lệnh thất bại", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * Hủy lệnh
     *
     * @param orderId ID của lệnh
     * @param member  Thông tin của thành viên
     * @param symbol  Symbol của hợp đồng
     * @return OrderDto
     */
    @DeleteMapping("/orders/{orderId}")
    @CircuitBreaker(name = "orderService", fallbackMethod = "cancelOrderFallback")
    @RateLimiter(name = "orderService")
    @Retry(name = "orderService")
    public ResponseEntity<ApiResponse<OrderDto>> cancelOrder(
            @PathVariable String orderId,
            @CurrentUser AuthMember member,
            @RequestParam String symbol) {

        // Xác thực dữ liệu đầu vào
        if (!inputValidator.isValidOrderId(orderId)) {
            log.warn("Hủy lệnh thất bại: OrderId không hợp lệ, orderId = {}", orderId);
            return ResponseEntity.badRequest().body(ApiResponse.error("OrderId không hợp lệ"));
        }

        if (!inputValidator.isValidMemberId(member.getId())) {
            log.warn("Hủy lệnh thất bại: MemberId không hợp lệ, memberId = {}", member.getId());
            return ResponseEntity.badRequest().body(ApiResponse.error("MemberId không hợp lệ"));
        }

        if (!inputValidator.isValidSymbol(symbol)) {
            log.warn("Hủy lệnh thất bại: Symbol không hợp lệ, symbol = {}", symbol);
            return ResponseEntity.badRequest().body(ApiResponse.error("Symbol không hợp lệ"));
        }

        try {
            // Hủy lệnh
            OrderDto canceledOrder = placeOrderUseCase.cancelOrder(orderId, member.getId(), symbol);
            return ResponseEntity.ok(ApiResponse.success(canceledOrder));
        } catch (Exception e) {
            log.error("Hủy lệnh thất bại", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * Hủy tất cả các lệnh của một thành viên
     *
     * @return Số lượng lệnh đã hủy
     */
    @DeleteMapping("/orders/member/")
    public ResponseEntity<ApiResponse<Integer>> cancelAllOrders(
            @CurrentUser AuthMember user) {
        try {
            int canceledCount = placeOrderUseCase.cancelAllOrders(user.getId());
            return ResponseEntity.ok(ApiResponse.success(canceledCount));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * Hủy tất cả các lệnh của một thành viên cho một symbol
     *
     * @param symbol Symbol của hợp đồng
     * @return List<OrderDto>
     */
    @DeleteMapping("/orders/member/symbol/{symbol}")
    public ResponseEntity<ApiResponse<List<OrderDto>>> cancelAllOrders(
            @CurrentUser AuthMember user,
            @PathVariable String symbol) {
        try {
            List<OrderDto> canceledOrders = placeOrderUseCase.cancelAllOrders(user.getId(), symbol);
            return ResponseEntity.ok(ApiResponse.success(canceledOrders));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * Lấy thông tin lệnh
     *
     * @param orderId ID của lệnh
     * @return Thông tin lệnh
     */
    @GetMapping("/orders/{orderId}")
    public ResponseEntity<ApiResponse<OrderDto>> getOrder(
            @PathVariable String orderId) {
        try {
            OrderDto order = placeOrderUseCase.getOrder(orderId);
            return ResponseEntity.ok(ApiResponse.success(order));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * Lấy danh sách lệnh theo memberId và symbol
     *
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái lệnh
     * @param page   Số trang
     * @param size   Kích thước trang
     * @return Danh sách lệnh
     */
    @GetMapping("/orders/member/symbol/{symbol}")
    public ResponseEntity<ApiResponse<List<OrderDto>>> getOrders(
            @CurrentUser AuthMember user,
            @PathVariable String symbol,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            List<OrderDto> orders = placeOrderUseCase.getOrders(user.getId(), symbol, status, page, size);
            return ResponseEntity.ok(ApiResponse.success(orders));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * Lấy danh sách lệnh đang hoạt động theo memberId và symbol
     *
     * @param symbol Symbol của hợp đồng
     * @return Danh sách lệnh
     */
    @GetMapping("/orders/member/symbol/{symbol}/active")
    public ResponseEntity<ApiResponse<List<OrderDto>>> getActiveOrders(
            @CurrentUser AuthMember user,
            @PathVariable String symbol) {
        try {
            List<OrderDto> orders = placeOrderUseCase.getActiveOrders(user.getId(), symbol);
            return ResponseEntity.ok(ApiResponse.success(orders));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    // Các API từ OrderMatchingController đã được loại bỏ
    // Vui lòng sử dụng các API mới trong OrderController và PriceController

    /**
     * Fallback cho placeOrder
     */
    public ResponseEntity<ApiResponse<PlaceOrderResult>> placeOrderFallback(
            PlaceOrderRequest request, Exception e) {
        log.error("Fallback cho placeOrder được gọi", e);
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(ApiResponse.error("Dịch vụ tạm thời không khả dụng. Vui lòng thử lại sau."));
    }

    /**
     * Fallback cho cancelOrder
     */
    public ResponseEntity<ApiResponse<OrderDto>> cancelOrderFallback(
            String orderId, Long memberId, String symbol, Exception e) {
        log.error("Fallback cho cancelOrder được gọi", e);
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(ApiResponse.error("Dịch vụ tạm thời không khả dụng. Vui lòng thử lại sau."));
    }
}
