package com.icetea.lotus.infrastructure.api.controller;

import com.icetea.lotus.application.dto.AdjustLeverageCommand;
import com.icetea.lotus.application.dto.AdjustMarginCommand;
import com.icetea.lotus.application.dto.ClosePositionCommand;
import com.icetea.lotus.application.dto.PositionDto;
import com.icetea.lotus.application.port.input.ManagePositionUseCase;
import com.icetea.lotus.core.domain.entity.AuthMember;
import com.icetea.lotus.core.domain.entity.MarginMode;
import com.icetea.lotus.core.domain.entity.PositionStatus;
import com.icetea.lotus.infrastructure.api.dto.ApiResponse;
import com.icetea.lotus.infrastructure.api.request.AdjustLeverageRequest;
import com.icetea.lotus.infrastructure.api.request.AdjustMarginRequest;
import com.icetea.lotus.infrastructure.api.request.ClosePositionRequest;
import com.icetea.lotus.infrastructure.api.request.ChangeMarginModeRequest;
import com.icetea.lotus.infrastructure.config.security.CurrentUser;
import com.icetea.lotus.infrastructure.exception.BusinessException;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.exception.ValidationException;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.ratelimiter.annotation.RateLimiter;
import io.github.resilience4j.retry.annotation.Retry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.icetea.lotus.core.domain.valueobject.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * Controller cho quản lý vị thế
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/positions")
@RequiredArgsConstructor
public class PositionController {

    private final ManagePositionUseCase managePositionUseCase;

    /**
     * Lấy vị thế theo memberId và symbol
     * @param symbol Symbol của hợp đồng
     * @return Thông tin vị thế
     */
    @GetMapping("/{symbol}")
    @CircuitBreaker(name = "positionService", fallbackMethod = "getPositionFallback")
    @RateLimiter(name = "positionService")
    public ResponseEntity<ApiResponse<PositionDto>> getPosition(
            @CurrentUser AuthMember user,
            @PathVariable String symbol) {
        log.info("Lấy vị thế, memberId = {}, symbol = {}", user.getId(), symbol);

        try {
            PositionDto position = managePositionUseCase.getPosition(user.getId(), symbol);

            if (position == null) {
                log.warn("Không tìm thấy vị thế, memberId = {}, symbol = {}", user.getId(), symbol);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(ApiResponse.error("Không tìm thấy vị thế"));
            }

            return ResponseEntity.ok(ApiResponse.success(position));
        } catch (ValidationException e) {
            log.warn("Lỗi xác thực khi lấy vị thế: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (BusinessException e) {
            log.error("Lỗi nghiệp vụ khi lấy vị thế: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.CONFLICT).body(ApiResponse.error(e.getMessage()));
        } catch (DatabaseException e) {
            log.error("Lỗi cơ sở dữ liệu khi lấy vị thế: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error("Lỗi hệ thống, vui lòng thử lại sau"));
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lấy vị thế", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error("Lỗi hệ thống, vui lòng thử lại sau"));
        }
    }

    /**
     * Fallback cho lấy vị thế
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param e Ngoại lệ
     * @return Thông báo lỗi
     */
    public ResponseEntity<ApiResponse<PositionDto>> getPositionFallback(Long memberId, String symbol, Exception e) {
        log.error("Fallback cho lấy vị thế, memberId = {}, symbol = {}", memberId, symbol, e);
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(ApiResponse.error("Dịch vụ tạm thời không khả dụng, vui lòng thử lại sau"));
    }

    /**
     * Lấy tất cả các vị thế của một thành viên với các tham số lọc
     * @param user ID của thành viên
     * @param status Trạng thái của vị thế (OPEN, CLOSED, null để lấy tất cả)
     * @param startTime Thời gian bắt đầu (định dạng ISO: yyyy-MM-ddTHH:mm:ss)
     * @param endTime Thời gian kết thúc (định dạng ISO: yyyy-MM-ddTHH:mm:ss)
     * @param page Số trang (bắt đầu từ 0)
     * @param size Kích thước trang
     * @return Danh sách các vị thế phân trang
     * Lấy tất cả các vị thế của một thành viên
     * @return Danh sách các vị thế
     */
    @GetMapping()
    @CircuitBreaker(name = "positionService", fallbackMethod = "getAllPositionsFallback")
    @RateLimiter(name = "positionService")
    public ResponseEntity<ApiResponse<Object>> getAllPositions(
            @CurrentUser AuthMember user,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        log.info("Lấy tất cả các vị thế, memberId = {}, status = {}, startTime = {}, endTime = {}, page = {}, size = {}",
                user.getId(), status, startTime, endTime, page, size);
        try {
            // Nếu không có tham số lọc, sử dụng phương thức cũ
            if (status == null && startTime == null && endTime == null) {
                List<PositionDto> positions = managePositionUseCase.getAllPositions(user.getId());
                return ResponseEntity.ok(ApiResponse.success(positions));
            } else {
                // Chuyển đổi status từ String sang enum nếu có
                PositionStatus positionStatus = null;
                if (status != null && !status.isEmpty()) {
                    try {
                        positionStatus = PositionStatus.valueOf(status.toUpperCase());
                    } catch (IllegalArgumentException e) {
                        return ResponseEntity.badRequest().body(ApiResponse.error("Trạng thái không hợp lệ. Các giá trị hợp lệ: OPEN, CLOSED"));
                    }
                }

                // Chuyển đổi startTime và endTime từ String sang LocalDateTime nếu có
                LocalDateTime startDateTime = null;
                if (startTime != null && !startTime.isEmpty()) {
                    try {
                        startDateTime = LocalDateTime.parse(startTime);
                    } catch (Exception e) {
                        return ResponseEntity.badRequest().body(ApiResponse.error("Định dạng thời gian bắt đầu không hợp lệ. Sử dụng định dạng ISO: yyyy-MM-ddTHH:mm:ss"));
                    }
                }

                LocalDateTime endDateTime = null;
                if (endTime != null && !endTime.isEmpty()) {
                    try {
                        endDateTime = LocalDateTime.parse(endTime);
                    } catch (Exception e) {
                        return ResponseEntity.badRequest().body(ApiResponse.error("Định dạng thời gian kết thúc không hợp lệ. Sử dụng định dạng ISO: yyyy-MM-ddTHH:mm:ss"));
                    }
                }

                // Gọi phương thức tìm kiếm với các tham số lọc
                Page<PositionDto> positionsPage = managePositionUseCase.findPositions(
                        user.getId(), positionStatus, startDateTime, endDateTime, page, size);

                return ResponseEntity.ok(ApiResponse.success(positionsPage));
            }
        } catch (ValidationException e) {
            log.warn("Lỗi xác thực khi lấy tất cả các vị thế: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (BusinessException e) {
            log.error("Lỗi nghiệp vụ khi lấy tất cả các vị thế: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.CONFLICT).body(ApiResponse.error(e.getMessage()));
        } catch (DatabaseException e) {
            log.error("Lỗi cơ sở dữ liệu khi lấy tất cả các vị thế: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error("Lỗi hệ thống, vui lòng thử lại sau"));
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lấy tất cả các vị thế", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error("Lỗi hệ thống, vui lòng thử lại sau"));
        }
    }

    /**
     * Fallback cho lấy tất cả các vị thế
     * @param member thông tin của thành viên
     * @param e Ngoại lệ
     * @return Thông báo lỗi
     */
    public ResponseEntity<ApiResponse<List<PositionDto>>> getAllPositionsFallback(@CurrentUser AuthMember member, Exception e) {
        log.error("Fallback cho lấy tất cả các vị thế, member = {}", member.getId(), e);
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(ApiResponse.error("Dịch vụ tạm thời không khả dụng, vui lòng thử lại sau"));
    }

    /**
     * Đóng vị thế
     * @param request Request chứa thông tin đóng vị thế
     * @return Thông tin vị thế sau khi đóng
     */
    @PostMapping("/close")
    @CircuitBreaker(name = "positionService", fallbackMethod = "closePositionFallback")
    @RateLimiter(name = "positionService")
    @Retry(name = "positionService")
    public ResponseEntity<ApiResponse<PositionDto>> closePosition(
            @Valid @RequestBody ClosePositionRequest request,
            @CurrentUser AuthMember member) {
        log.info("closePosition==> Đóng vị thế, request = {}", request);

        ClosePositionCommand command = ClosePositionCommand.builder()
                .memberId(member.getId())
                .symbol(request.getSymbol())
                .volume(request.getVolume())
                .price(request.getPrice())
                .build();

        try {
            PositionDto position = managePositionUseCase.closePosition(command);
            return ResponseEntity.ok(ApiResponse.success(position));
        } catch (ValidationException e) {
            log.warn("Lỗi xác thực khi đóng vị thế: {}", e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (BusinessException e) {
            log.error("Lỗi nghiệp vụ khi đóng vị thế: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.CONFLICT).body(ApiResponse.error(e.getMessage()));
        } catch (DatabaseException e) {
            log.error("Lỗi cơ sở dữ liệu khi đóng vị thế: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error("Lỗi hệ thống, vui lòng thử lại sau"));
        } catch (Exception e) {
            log.error("Lỗi không xác định khi đóng vị thế", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponse.error("Lỗi hệ thống, vui lòng thử lại sau"));
        }
    }

    /**
     * Fallback cho đóng vị thế
     * @param request Request chứa thông tin đóng vị thế
     * @param e Ngoại lệ
     * @return Thông báo lỗi
     */
    public ResponseEntity<ApiResponse<PositionDto>> closePositionFallback(ClosePositionRequest request, Exception e) {
        log.error("Fallback cho đóng vị thế, request = {}", request, e);
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(ApiResponse.error("Dịch vụ tạm thời không khả dụng, vui lòng thử lại sau"));
    }

    /**
     * Đóng tất cả các vị thế của một thành viên
     * @return Số lượng vị thế đã đóng
     */
    @PostMapping("/close-all")
    public ResponseEntity<ApiResponse<Integer>> closeAllPositions(@CurrentUser AuthMember user) {
        try {
            int closedCount = managePositionUseCase.closeAllPositions(user.getId());
            return ResponseEntity.ok(ApiResponse.success(closedCount));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * Điều chỉnh đòn bẩy cho vị thế
     * @param request Request chứa thông tin điều chỉnh đòn bẩy
     * @return Thông tin vị thế sau khi điều chỉnh
     */
    @PostMapping("/adjust-leverage")
    public ResponseEntity<ApiResponse<PositionDto>> adjustLeverage(
            @Valid @RequestBody AdjustLeverageRequest request,
            @CurrentUser AuthMember member) {
        AdjustLeverageCommand command = AdjustLeverageCommand.builder()
                .memberId(member.getId())
                .symbol(request.getSymbol())
                .leverage(request.getLeverage())
                .build();

        try {
            PositionDto position = managePositionUseCase.adjustLeverage(command);
            return ResponseEntity.ok(ApiResponse.success(position));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * Điều chỉnh ký quỹ cho vị thế
     * @param request Request chứa thông tin điều chỉnh ký quỹ
     * @return Thông tin vị thế sau khi điều chỉnh
     */
    @PostMapping("/adjust-margin")
    public ResponseEntity<ApiResponse<PositionDto>> adjustMargin(
            @Valid @RequestBody AdjustMarginRequest request,
            @CurrentUser AuthMember member) {
        AdjustMarginCommand command = AdjustMarginCommand.builder()
                .memberId(member.getId())
                .symbol(request.getSymbol())
                .margin(request.getMargin())
                .isAdd(request.isAdd())
                .build();

        try {
            PositionDto position = managePositionUseCase.adjustMargin(command);
            return ResponseEntity.ok(ApiResponse.success(position));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * Thay đổi chế độ ký quỹ cho vị thế
     * @param request Request chứa thông tin thay đổi chế độ ký quỹ
     * @return Thông tin vị thế sau khi thay đổi
     */
    @PostMapping("/change-margin-mode")
    public ResponseEntity<ApiResponse<PositionDto>> changeMarginMode(
            @Valid @RequestBody ChangeMarginModeRequest request,
            @CurrentUser AuthMember member) {
        try {
            MarginMode marginMode = MarginMode.valueOf(request.getMarginMode());
            PositionDto position = managePositionUseCase.changeMarginMode(member.getId(), request.getSymbol(), marginMode);
            return ResponseEntity.ok(ApiResponse.success(position));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * Tính toán lợi nhuận không thực hiện cho vị thế
     * @param symbol Symbol của hợp đồng
     * @return Lợi nhuận không thực hiện
     */
    @GetMapping("/{symbol}/unrealized-profit")
    public ResponseEntity<ApiResponse<BigDecimal>> calculateUnrealizedProfit(
            @CurrentUser AuthMember user,
            @PathVariable String symbol) {
        BigDecimal unrealizedProfit = managePositionUseCase.calculateUnrealizedProfit(user.getId(), symbol);
        return ResponseEntity.ok(ApiResponse.success(unrealizedProfit));
    }

    /**
     * Tính toán tổng lợi nhuận không thực hiện cho tất cả các vị thế của một thành viên
     * @return Tổng lợi nhuận không thực hiện
     */
    @GetMapping("/total-unrealized-profit")
    public ResponseEntity<ApiResponse<BigDecimal>> calculateTotalUnrealizedProfit(
            @CurrentUser AuthMember user) {
        BigDecimal totalUnrealizedProfit = managePositionUseCase.calculateTotalUnrealizedProfit(user.getId());
        return ResponseEntity.ok(ApiResponse.success(totalUnrealizedProfit));
    }
}
