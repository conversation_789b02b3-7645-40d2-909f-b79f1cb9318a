server.port=7004
#\u5E94\u7528\u540D\u79F0\u540E\u7F00\u9700\u8981\u6539\u6210\u5E01\u79CD\u5355\u4F4D\uFF0C\u5C0F\u5199
spring.application.name=service-rpc-eusdt
#kafka
# \u6307\u5B9Akafka \u4EE3\u7406\u5730\u5740\uFF0C\u53EF\u4EE5\u591A\u4E2A
spring.kafka.bootstrap-servers=***************:9092
# \u6BCF\u6B21\u6279\u91CF\u53D1\u9001\u6D88\u606F\u7684\u6570\u91CF
spring.kafka.producer.batch-size= 10
# mongodb
spring.data.mongodb.uri=********************************************************
eureka.client.serviceUrl.defaultZone=http://***************:7000/eureka/

# \u6CE8\u518C\u65F6\u4F7F\u7528ip\u800C\u4E0D\u662F\u4E3B\u673A\u540D
eureka.instance.prefer-ip-address=true

#\u5E01\u79CD\u4FE1\u606F\u914D\u7F6E
coin.rpc=http://127.0.0.1:8545
coin.name=EUSDT
coin.unit=EUSDT
coin.keystore-path=/data/eth/data/keystore
#\u9ED8\u8BA4\u8F6C\u8D26\u6700\u5927Gas,\u9ED8\u8BA4\u503C\u4E3A5\u4E07
coin.gas-limit=50000
#\u63D0\u5E01\u94B1\u5305
coin.withdraw-wallet=UTC--2019-09-11T08-36-14.828000000Z--2b7d8aa02fccbd7bc69368fa30cabe22e3c2c2d.json
#\u63D0\u5E01\u94B1\u5305\u5BC6\u7801
coin.withdraw-wallet-password=
coin.min-collect-amount=3
#####\u5408\u7EA6\u914D\u7F6E\u4FE1\u606F#####
#\u5408\u7EA6\u5730\u5740
#EUSDT
contract.address=******************************************
#\u5408\u7EA6\u7CBE\u5EA6,\u6839\u636E\u5408\u7EA6decimals\u53C2\u6570\u53D6\u76F8\u5E94\u7684\u5355\u4F4D\u540D\u79F0
#wei:0,kwei:3,wwei:4,mwei:6,gwei:9,szabo:12,finney:15,ether:18,kether:21,mether:24,gether:27
contract.decimals=mwei
#\u5408\u7EA6\u8F6C\u8D26GasLimit
contract.gas-limit=50000
#Etherscan\u4E8B\u4EF6\u65E5\u5FD7(Token\u4E3Aetherscan.io\u4E0A\u7533\u8BF7\u7684apikey)
etherscan.token=BYFVKAANT5JVQPURWJMUAEUFCKGZ56P68
#topic0\u53EF\u901A\u8FC7\u67E5\u770B\u667A\u80FD\u5408\u7EA6\u627E\u5230\uFF08\u5982\u641C\u7D22Tether\u540E\uFF0C\u70B9\u51FB\u6253\u5F00\u4EFB\u610F\u4E00\u7B14\u4EA4\u6613\uFF0C\u7136\u540E\u5207\u6362Tab\u81F3Event Log)
contract.event-topic0=0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a1128f55a4df523b3ef

#\u9996\u6B21\u8FD0\u884C\u521D\u59CB\u5316\u9AD8\u5EA6
watcher.init-block-height=8551979
#\u533A\u5757\u6B65\u957F
watcher.step=5
#\u533A\u5757\u786E\u8BA4\u6570
watcher.confirmation=20
#\u533A\u5757\u540C\u6B65\u65F6\u95F4\u95F4\u9694
watcher.interval=20000
