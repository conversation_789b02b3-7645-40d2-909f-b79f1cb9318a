package com.icetea.lotus.validator;

import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.dto.AddOrderRequest;
import com.icetea.lotus.entity.Coin;
import com.icetea.lotus.entity.ExchangeCoin;
import com.icetea.lotus.entity.ExchangeCoinPublishType;
import com.icetea.lotus.entity.ExchangeOrder;
import com.icetea.lotus.entity.ExchangeOrderDirection;
import com.icetea.lotus.entity.ExchangeOrderStatus;
import com.icetea.lotus.entity.ExchangeOrderType;
import com.icetea.lotus.entity.Member;
import com.icetea.lotus.entity.MemberWallet;
import com.icetea.lotus.entity.transform.AuthMember;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Objects;

/**
 * Validator for AddOrderRequest
 */
@Component
@RequiredArgsConstructor
public class AddOrderRequestValidator {

    private final ExchangeCoinService exchangeCoinService;
    private final LocaleMessageSourceService msService;
    private final CoinService coinService;
    private final MemberService memberService;
    private final MemberWalletService walletService;

    /**
     * Validate the AddOrderRequest
     *
     * @param request the request to validate
     * @return MessageResult with error if validation fails, null if validation passes
     */
    public MessageResult validateOrderRequest(AddOrderRequest request) {
        // Check if direction and type are not null (already validated by annotations, but double-checking)
        if (request.getDirection() == null || request.getType() == null) {
            return MessageResult.error(500, msService.getMessage("ILLEGAL_ARGUMENT"));
        }

        // Determine whether the limit price input value is less than zero
        if (request.getPrice() != null &&
                request.getPrice().compareTo(BigDecimal.ZERO) <= 0 &&
                request.getType() == ExchangeOrderType.LIMIT_PRICE) {
            return MessageResult.error(500, msService.getMessage("EXORBITANT_PRICES"));
        }

        // Determine if the quantity is less than zero (already validated by @Positive, but double-checking)
        if (request.getAmount() == null || request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return MessageResult.error(500, msService.getMessage("NUMBER_OF_ILLEGAL"));
        }

        // Get transaction pair information based on transaction pair name (symbol)
        ExchangeCoin exchangeCoin = exchangeCoinService.findBySymbol(request.getSymbol());
        if (exchangeCoin == null) {
            return MessageResult.error(500, msService.getMessage("NONSUPPORT_COIN"));
        }

        if (exchangeCoin.getEnable() != 1 || exchangeCoin.getExchangeable() != 1) {
            return MessageResult.error(500, msService.getMessage("COIN_FORBIDDEN"));
        }

        // Validation passed
        return null;
    }

    /**
     * Validate the user order
     *
     * @param member the user information
     * @return MessageResult with error if validation fails, null if validation passes
     */
    public MessageResult validateUserOrder(Member member) {
        // Checking if the trade is prohibited
        if (member.getTransactionStatus().equals(BooleanEnum.IS_FALSE)) {
            return MessageResult.error(500, msService.getMessage("CANNOT_TRADE"));
        }

        return null;
    }

    /**
     * Validate the coin
     *
     * @param exchangeCoin the transaction pair information
     * @param direction    the transaction direction
     * @return MessageResult with error if validation fails, null if validation passes
     */
    public MessageResult validateCoin(ExchangeCoin exchangeCoin, ExchangeOrderDirection direction) {

        Coin coin;
        // Get currency information based on a transaction direction
        if (direction == ExchangeOrderDirection.SELL) {
            coin = coinService.findByUnit(exchangeCoin.getCoinSymbol());
        } else {
            coin = coinService.findByUnit(exchangeCoin.getBaseSymbol());
        }
        if (coin == null) {
            return MessageResult.error(500, msService.getMessage("NONSUPPORT_COIN"));
        }

        return null;
    }

    /**
     * Perform basic validations for the order request
     */
    public MessageResult performAllValidations(AddOrderRequest orderRequest, AuthMember authMember) {
        // Validate request using the validator
        MessageResult validationResult = validateOrderRequest(orderRequest);
        if (validationResult != null) {
            return validationResult;
        }

        // Get transaction pair information based on transaction pair name (symbol)
        ExchangeCoin exchangeCoin = exchangeCoinService.findBySymbol(orderRequest.getSymbol());

        Member member = memberService.findOne(authMember.getId());

        // Validate user order
        MessageResult validateUserOrder = validateUserOrder(member);
        if (validateUserOrder != null) {
            return validateUserOrder;
        }

        // Validate coin
        return validateCoin(exchangeCoin, orderRequest.getDirection());
    }


    /**
     * Validate amount and price for the order
     */
    public MessageResult validateAmountAndPrice(ExchangeOrderDirection direction, ExchangeOrderType type,
                                                BigDecimal price, BigDecimal amount, ExchangeCoin exchangeCoin) {
        // Number and precision control of delegation
        if (direction == ExchangeOrderDirection.BUY && type == ExchangeOrderType.MARKET_PRICE) {
            amount = amount.setScale(exchangeCoin.getBaseCoinScale(), RoundingMode.DOWN);
            // Minimum transaction volume control
            if (amount.compareTo(exchangeCoin.getMinTurnover()) < 0) {
                return MessageResult.error(500, "The transaction amount is at least " + exchangeCoin.getMinTurnover());
            }
        } else {
            amount = amount.setScale(exchangeCoin.getCoinScale(), RoundingMode.DOWN);
            // Volume range control
            if (exchangeCoin.getMaxVolume() != null && exchangeCoin.getMaxVolume().compareTo(BigDecimal.ZERO) != 0
                    && exchangeCoin.getMaxVolume().compareTo(amount) < 0) {
                return MessageResult.error(msService.getMessage("AMOUNT_OVER_SIZE") + " " + exchangeCoin.getMaxVolume());
            }
            if (exchangeCoin.getMinVolume() != null && exchangeCoin.getMinVolume().compareTo(BigDecimal.ZERO) != 0
                    && exchangeCoin.getMinVolume().compareTo(amount) > 0) {
                return MessageResult.error(msService.getMessage("AMOUNT_TOO_SMALL") + " " + exchangeCoin.getMinVolume());
            }
        }

        // Validate price for limit orders
        if (price.compareTo(BigDecimal.ZERO) <= 0 && type == ExchangeOrderType.LIMIT_PRICE) {
            return MessageResult.error(500, msService.getMessage("EXORBITANT_PRICES"));
        }

        // Validate amount
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return MessageResult.error(500, msService.getMessage("NUMBER_OF_ILLEGAL"));
        }

        return null;
    }

    /**
     * Validate user wallets
     */
    public MessageResult validateWallets(Long memberId, String baseCoin, String tradingCoin) {
        MemberWallet baseCoinWallet = walletService.findByCoinUnitAndMemberId(baseCoin, memberId);
        MemberWallet tradingCoinWallet = walletService.findByCoinUnitAndMemberId(tradingCoin, memberId);

        if (baseCoinWallet == null || tradingCoinWallet == null) {
            return MessageResult.error(500, msService.getMessage("NONSUPPORT_COIN"));
        }

        if (baseCoinWallet.getIsLock() == BooleanEnum.IS_TRUE || tradingCoinWallet.getIsLock() == BooleanEnum.IS_TRUE) {
            return MessageResult.error(500, msService.getMessage("WALLET_LOCKED"));
        }

        return null;
    }

    /**
     * Validate price limits and market trading
     */
    public MessageResult validatePriceLimits(ExchangeOrderDirection direction, ExchangeOrderType type,
                                             BigDecimal price, ExchangeCoin exchangeCoin) {
        // If there is a minimum selling price limit, the bid cannot be lower than this price, and market prices are prohibited
        if (direction == ExchangeOrderDirection.SELL && exchangeCoin.getMinSellPrice().compareTo(BigDecimal.ZERO) > 0
                && ((price.compareTo(exchangeCoin.getMinSellPrice()) < 0) || type == ExchangeOrderType.MARKET_PRICE)) {
            return MessageResult.error(500, "Cannot be lower than the minimum price: " + exchangeCoin.getMinSellPrice());
        }

        // If there is a maximum bid price limit, the bid cannot be higher than this price, and market price is prohibited
        if (direction == ExchangeOrderDirection.BUY && exchangeCoin.getMaxBuyPrice().compareTo(BigDecimal.ZERO) > 0
                && ((price.compareTo(exchangeCoin.getMaxBuyPrice()) > 0) || type == ExchangeOrderType.MARKET_PRICE)) {
            return MessageResult.error(500, "Cannot be higher than the maximum price: " + exchangeCoin.getMaxBuyPrice());
        }

        // Check whether to enable market trading
        if (type == ExchangeOrderType.MARKET_PRICE) {
            if (exchangeCoin.getEnableMarketBuy() == BooleanEnum.IS_FALSE && direction == ExchangeOrderDirection.BUY) {
                return MessageResult.error(500, "Market price purchase is not supported");
            } else if (exchangeCoin.getEnableMarketSell() == BooleanEnum.IS_FALSE && direction == ExchangeOrderDirection.SELL) {
                return MessageResult.error(500, "No market price support");
            }
        }

        return null;
    }


    /**
     * Validates that the order belongs to the current user
     *
     * @param order    the order to validate
     * @param memberId the ID of the current user
     * @return an error message if validation fails, null otherwise
     */
    public MessageResult validateOrderOwnership(ExchangeOrder order, Long memberId) {
        if (!Objects.equals(order.getMemberId(), memberId)) {
            return MessageResult.error(500, "Prohibited Operations");
        }
        return null;
    }


    /**
     * Validates that the order status is TRADING
     *
     * @param order the order to validate
     * @return an error message if validation fails, null otherwise
     */
    public MessageResult validateOrderStatus(ExchangeOrder order) {
        if (order.getStatus() != ExchangeOrderStatus.TRADING) {
            return MessageResult.error(500, "Order status error (fulfilled or cancelled)");
        }
        return null;
    }

    /**
     * Validates that the order can be cancelled based on liquidation period constraints
     *
     * @param order the order to validate
     * @return an error message if validation fails, null otherwise
     */
    public MessageResult validateLiquidationPeriod(ExchangeOrder order) {

        SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        ExchangeCoin exchangeCoin = exchangeCoinService.findBySymbol(order.getSymbol());
        if (exchangeCoin.getPublishType() != ExchangeCoinPublishType.NONE) {
            long currentTime = Calendar.getInstance().getTimeInMillis(); // Current timestamp
            try {
                // Between the end time of the event and the end time of the liquidation
                if (currentTime > dateTimeFormat.parse(exchangeCoin.getEndTime()).getTime() &&
                        currentTime < dateTimeFormat.parse(exchangeCoin.getClearTime()).getTime()) {
                    return MessageResult.error(500, "Orders cannot be cancelled during consolidation");
                }
            } catch (ParseException e) {
                return MessageResult.error(500, "Unknown error: 9003");
            }
        }
        return null;
    }


}