package com.icetea.lotus.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Getter
@AllArgsConstructor
public enum TransferEnum {
    SPOT_TO_FUTURE(1, "spot to future"),
    FUTURE_TO_SPOT(2, "future to spot");

    private int type;
    private String description;

    public static TransferEnum getTransferEnumByType(int type){
        for(TransferEnum transferEnum : TransferEnum.values()){
            if(transferEnum.getType() == type){
                return transferEnum;
            }
        }
        return null;
    }
}
