server.port=7005
spring.application.name=service-rpc-etc
eureka.client.serviceUrl.defaultZone=http://127.0.0.1:7000/eureka/
# 注册时使用ip而不是主机名
eureka.instance.prefer-ip-address=true

spring.data.mongodb.uri=mongodb://127.0.0.1:27017/wallet

coin.rpc=http://127.0.0.1:5990/v1
coin.name=ECT
coin.unit=ECT
coin.master-address=esV75BQfiEiKdgaivjasdw7EXk3BwJiscX
coin.init-block-height=39610
coin.withdraw-wallet=shxgAaZZGqaU9QdfedQvejadpGEqy
coin.withdraw-address=esV75BQfiEiKdasdejEYCt7EXk3BwJiscX