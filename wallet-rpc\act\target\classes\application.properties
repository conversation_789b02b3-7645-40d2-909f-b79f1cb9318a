server.port=7001
spring.application.name=service-rpc-act
#kafka
# 指定kafka 代理地址，可以多个
spring.kafka.bootstrap-servers=127.0.0.1:9092
# 指定默认消费者group id
spring.kafka.consumer.group-id=default-group
# 指定默认topic id
spring.kafka.template.default-topic= test
# 指定listener 容器中的线程数，用于提高并发量
spring.kafka.listener.concurrency= 3
# 每次批量发送消息的数量
spring.kafka.producer.batch-size= 1000
# mongodb
spring.data.mongodb.uri=**********************************************

eureka.client.serviceUrl.defaultZone=http://127.0.0.1:7000/eureka/
# 注册时使用ip而不是主机名
eureka.instance.prefer-ip-address=true

coin.name=ACT
coin.unit=ACT
coin.rpc=********************************/rpc
coin.master-address=ACT5i65XW1yRasdeLMD2rFJffRmndn91bho6