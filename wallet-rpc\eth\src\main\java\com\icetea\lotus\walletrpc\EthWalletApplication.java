package com.icetea.lotus.walletrpc;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.methods.response.NetVersion;
import org.web3j.protocol.core.methods.response.Web3ClientVersion;

import java.math.BigInteger;

@SpringBootApplication
@EnableScheduling
@EnableDiscoveryClient
public class EthWalletApplication implements CommandLineRunner {
    @Autowired
    private Web3j web3j;
    public static void main(String[] args){
        SpringApplication.run(EthWalletApplication.class,args);
    }

    @Override
    public void run(String... strings) throws Exception {
        NetVersion netVersion = web3j.netVersion().send();

        String chainIdStr = netVersion.getNetVersion(); // Ví dụ: "1", "5", "11155111"
        long chainId = Long.parseLong(chainIdStr);

        System.out.println("Chain ID = " + chainId);
    }
}
