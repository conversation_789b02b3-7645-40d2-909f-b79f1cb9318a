package com.icetea.lotus.core.domain.service.impl;

import com.icetea.lotus.core.domain.entity.KLine;
import com.icetea.lotus.core.domain.entity.Trade;
import com.icetea.lotus.core.domain.repository.KLineRepository;
import com.icetea.lotus.core.domain.service.KLineManagementService;
import com.icetea.lotus.core.domain.service.MarketDataService;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.websocket.MarketHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Implementation của domain service KLineManagementService
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KLineManagementServiceImpl implements KLineManagementService {

    private final KLineRepository kLineRepository;
    private final MarketDataService marketDataService;
    private final MarketHandler marketHandler;

    // Cache cho KLine mới nhất
    private final Map<String, KLine> kLineCache = new ConcurrentHashMap<>();

    // Mảng các khoảng thời gian
    private static final String[] PERIODS = {"1min", "5min", "15min", "30min", "60min", "4hour", "1day", "1week", "1mon"};

    /**
     * Tạo K-line mới
     * @param symbol Symbol của hợp đồng
     * @param period Khoảng thời gian
     * @param time Thời gian
     * @param openPrice Giá mở cửa
     * @param highestPrice Giá cao nhất
     * @param lowestPrice Giá thấp nhất
     * @param closePrice Giá đóng cửa
     * @param count Số lượng giao dịch
     * @param volume Khối lượng giao dịch
     * @param turnover Giá trị giao dịch
     * @return KLine
     */
    @Override
    public KLine createKLine(Symbol symbol, String period, Long time, Money openPrice, Money highestPrice,
                            Money lowestPrice, Money closePrice, Integer count,
                            BigDecimal volume, BigDecimal turnover) {
        log.debug("Tạo K-line mới, symbol = {}, period = {}, time = {}", symbol.getValue(), period, time);

        KLine kLine = KLine.builder()
                .symbol(symbol)
                .period(period)
                .time(time)
                .openPrice(openPrice)
                .highestPrice(highestPrice)
                .lowestPrice(lowestPrice)
                .closePrice(closePrice)
                .count(count)
                .volume(volume)
                .turnover(turnover)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        KLine savedKLine = kLineRepository.save(kLine);

        // Cập nhật cache
        String cacheKey = getCacheKey(symbol, period);
        kLineCache.put(cacheKey, savedKLine);

        // Gửi K-line qua WebSocket
        marketHandler.handleKLine(symbol.getValue(), savedKLine);

        return savedKLine;
    }

    /**
     * Lưu K-line đã có
     * @param kLine K-line cần lưu
     * @return K-line đã được lưu
     */
    @Override
    public KLine saveKLine(KLine kLine) {
        log.debug("Lưu K-line, symbol = {}, period = {}, time = {}",
                kLine.getSymbol().getValue(), kLine.getPeriod(), kLine.getTime());

        KLine savedKLine = kLineRepository.save(kLine);

        // Cập nhật cache
        String cacheKey = getCacheKey(kLine.getSymbol(), kLine.getPeriod());
        kLineCache.put(cacheKey, savedKLine);

        // Gửi K-line qua WebSocket
        marketHandler.handleKLine(kLine.getSymbol().getValue(), savedKLine);

        return savedKLine;
    }

    /**
     * Cập nhật K-line với một giao dịch mới
     *
     * @param trade Giao dịch
     */
    @Override
    public void updateKLineWithTrade(Trade trade) {
        log.debug("Cập nhật K-line với giao dịch mới, symbol = {}, price = {}, volume = {}",
                trade.getSymbol().getValue(), trade.getPrice().getValue(), trade.getVolume());

        // Lấy K-line hiện tại cho khoảng thời gian 1 phút
        Symbol symbol = trade.getSymbol();
        String period = "1min";

        // Lấy thời gian hiện tại và làm tròn xuống phút
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long time = calendar.getTimeInMillis();

        // Tìm K-line hiện tại
        KLine kLine = kLineRepository.findTopBySymbolAndPeriodOrderByTimeDesc(symbol, period);

        // Nếu không tìm thấy K-line hoặc K-line không phải là K-line hiện tại, tạo K-line mới
        if (kLine == null || kLine.getTime() != time) {
            Money price = trade.getPrice();
            BigDecimal volume = trade.getVolume();
            BigDecimal turnover = price.getValue().multiply(volume);

            // Tìm K-line trước đó
            KLine previousKLine = getLatestKLine(symbol, period);

            // Thiết lập giá trị ban đầu cho K-line mới
            Money openPrice = price;
            Money highestPrice = price;
            Money lowestPrice = price;

            // Nếu có K-line trước đó, sử dụng giá đóng cửa của nó làm giá mở cửa và giá thấp nhất ban đầu
            if (previousKLine != null && previousKLine.getClosePrice() != null && !previousKLine.getClosePrice().isZero()) {
                openPrice = previousKLine.getClosePrice();
                lowestPrice = previousKLine.getClosePrice();
            }

            createKLine(symbol, period, time, openPrice, highestPrice, lowestPrice, price, 1, volume, turnover);
        } else {
            // Cập nhật K-line hiện tại
            kLine.update(trade.getPrice(), trade.getVolume(), trade.getPrice().getValue().multiply(trade.getVolume()));
            saveKLine(kLine);
        }
    }



    /**
     * Tạo cache key từ symbol và period
     * @param symbol Symbol của hợp đồng
     * @param period Khoảng thời gian
     * @return Cache key
     */
    private String getCacheKey(Symbol symbol, String period) {
        return symbol.getValue() + ":" + period;
    }

    /**
     * Tạo K-line cho các khoảng thời gian khác nhau
     * @param symbol Symbol của hợp đồng
     * @param time Thời gian
     * @param minute Phút
     * @param hour Giờ
     */
    @Override
    public void generateKLine(Symbol symbol, Long time, int minute, int hour) {
        log.debug("Tạo K-line cho các khoảng thời gian khác nhau, symbol = {}, time = {}, minute = {}, hour = {}",
                symbol.getValue(), time, minute, hour);

        // Tạo K-line cho khoảng thời gian 1 phút
        generateKLine(symbol, 1, Calendar.MINUTE, time);

        // Tạo K-line cho khoảng thời gian 5 phút
        if (minute % 5 == 0) {
            generateKLine(symbol, 5, Calendar.MINUTE, time);
        }

        // Tạo K-line cho khoảng thời gian 15 phút
        if (minute % 15 == 0) {
            generateKLine(symbol, 15, Calendar.MINUTE, time);
        }

        // Tạo K-line cho khoảng thời gian 30 phút
        if (minute % 30 == 0) {
            generateKLine(symbol, 30, Calendar.MINUTE, time);
        }

        // Tạo K-line cho khoảng thời gian 1 giờ
        if (minute == 0) {
            generateKLine(symbol, 1, Calendar.HOUR_OF_DAY, time);
        }

        // Tạo K-line cho khoảng thời gian 4 giờ
        if (hour % 4 == 0 && minute == 0) {
            generateKLine(symbol, 4, Calendar.HOUR_OF_DAY, time);
        }
    }

    /**
     * Tạo K-line cho một khoảng thời gian cụ thể
     * @param symbol Symbol của hợp đồng
     * @param value Giá trị (1, 5, 15, 30, 60, 4, 1, 1, 1)
     * @param field Trường thời gian (Calendar.MINUTE, Calendar.HOUR_OF_DAY, Calendar.DAY_OF_YEAR, Calendar.WEEK_OF_MONTH, Calendar.MONTH)
     * @param time Thời gian
     */
    @Override
    public void generateKLine(Symbol symbol, int value, int field, Long time) {
        String period;

        // Xác định khoảng thời gian dựa trên value và field
        if (field == Calendar.MINUTE) {
            if (value == 1) {
                period = "1min";
            } else if (value == 5) {
                period = "5min";
            } else if (value == 15) {
                period = "15min";
            } else if (value == 30) {
                period = "30min";
            } else {
                return;
            }
        } else if (field == Calendar.HOUR_OF_DAY) {
            if (value == 1) {
                period = "60min";
            } else if (value == 4) {
                period = "4hour";
            } else {
                return;
            }
        } else if (field == Calendar.DAY_OF_YEAR) {
            period = "1day";
        } else if (field == Calendar.WEEK_OF_MONTH) {
            period = "1week";
        } else if (field == Calendar.MONTH) {
            period = "1mon";
        } else {
            return;
        }

        log.debug("Tạo K-line cho khoảng thời gian cụ thể, symbol = {}, period = {}, time = {}",
                symbol.getValue(), period, time);

        // Tìm các giao dịch trong khoảng thời gian
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);

        // Tính thời gian bắt đầu
        Calendar startCalendar = (Calendar) calendar.clone();
        if (field == Calendar.MINUTE) {
            startCalendar.add(field, -value);
        } else if (field == Calendar.HOUR_OF_DAY) {
            startCalendar.add(field, -value);
        } else if (field == Calendar.DAY_OF_YEAR) {
            startCalendar.add(field, -1);
        } else if (field == Calendar.WEEK_OF_MONTH) {
            startCalendar.add(field, -1);
        } else if (field == Calendar.MONTH) {
            startCalendar.add(field, -1);
        }

        // Tìm K-line trước đó
        KLine previousKLine = getLatestKLine(symbol, period);

        // Logic đơn giản theo market module: Khi không có giao dịch, O,H,L,C = closePrice của nến trước
        Money currentPrice = marketDataService.getLastPrice(symbol);
        Money openPrice;
        Money highestPrice;
        Money lowestPrice;
        Money closePrice;
        BigDecimal volume = BigDecimal.ZERO; // Mặc định volume = 0 khi không có giao dịch
        BigDecimal turnover = BigDecimal.ZERO; // Mặc định turnover = 0 khi không có giao dịch

        if (previousKLine != null && previousKLine.getClosePrice() != null && !previousKLine.getClosePrice().isZero()) {
            // Sử dụng giá đóng cửa của nến trước cho tất cả O,H,L,C (theo logic market module)
            Money previousClosePrice = previousKLine.getClosePrice();
            openPrice = previousClosePrice;
            highestPrice = previousClosePrice;
            lowestPrice = previousClosePrice;
            closePrice = previousClosePrice;

            log.debug("Sử dụng giá đóng cửa của nến trước: {}", previousClosePrice.getValue());
        } else {
            // Nếu không có nến trước, sử dụng giá hiện tại
            openPrice = currentPrice;
            highestPrice = currentPrice;
            lowestPrice = currentPrice;
            closePrice = currentPrice;

            log.debug("Không có nến trước, sử dụng giá hiện tại: {}", currentPrice.getValue());
        }

        // Tạo K-line mới với logic đã sửa (theo market module)
        createKLine(symbol, period, time, openPrice, highestPrice, lowestPrice, closePrice, 0, volume, turnover);
    }

    /**
     * Lấy K-line mới nhất theo symbol và period
     * @param symbol Symbol của hợp đồng
     * @param period Khoảng thời gian
     * @return KLine
     */
    @Override
    public KLine getLatestKLine(Symbol symbol, String period) {
        log.debug("Lấy K-line mới nhất, symbol = {}, period = {}", symbol.getValue(), period);

        // Kiểm tra cache
        String cacheKey = getCacheKey(symbol, period);
        KLine cachedKLine = kLineCache.get(cacheKey);

        if (cachedKLine != null) {
            log.debug("Đã lấy K-line từ cache, symbol = {}, period = {}, closePrice = {}",
                    symbol.getValue(), period,
                    cachedKLine.getClosePrice() != null ? cachedKLine.getClosePrice().getValue() : "null");
            return cachedKLine;
        }

        // Nếu không có trong cache, lấy từ repository
        KLine kLine = kLineRepository.findTopBySymbolAndPeriodOrderByTimeDesc(symbol, period);

        if (kLine == null) {
            log.warn("Không tìm thấy K-line nào trong cơ sở dữ liệu cho symbol = {}, period = {}", symbol.getValue(), period);
            return null;
        }

        // Kiểm tra giá closePrice
        if (kLine.getClosePrice() == null || kLine.getClosePrice().isZero()) {
            log.warn("Giá closePrice là null hoặc 0 cho symbol = {}, period = {}", symbol.getValue(), period);
        } else {
            log.debug("Đã lấy được K-line từ cơ sở dữ liệu cho symbol = {}, period = {}, closePrice = {}",
                    symbol.getValue(), period, kLine.getClosePrice().getValue());

            // Cập nhật cache
            kLineCache.put(cacheKey, kLine);
        }

        return kLine;
    }

    /**
     * Lấy danh sách K-line theo symbol, period và khoảng thời gian
     * @param symbol Symbol của hợp đồng
     * @param period Khoảng thời gian
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách K-line
     */
    @Override
    public List<KLine> getKLinesByTimeRange(Symbol symbol, String period, Long startTime, Long endTime) {
        log.debug("Lấy danh sách K-line theo khoảng thời gian, symbol = {}, period = {}, startTime = {}, endTime = {}",
                symbol.getValue(), period, startTime, endTime);
        return kLineRepository.findBySymbolAndPeriodAndTimeBetween(symbol, period, startTime, endTime);
    }

    /**
     * Lấy danh sách K-line theo symbol và period, giới hạn số lượng kết quả
     * @param symbol Symbol của hợp đồng
     * @param period Khoảng thời gian
     * @param limit Số lượng kết quả tối đa
     * @return Danh sách K-line
     */
    @Override
    public List<KLine> getKLines(Symbol symbol, String period, int limit) {
        log.debug("Lấy danh sách K-line, symbol = {}, period = {}, limit = {}", symbol.getValue(), period, limit);
        return kLineRepository.findBySymbolAndPeriodOrderByTimeDesc(symbol, period, limit);
    }

    /**
     * Đồng bộ K-line từ nguồn dữ liệu bên ngoài
     * @param symbol Symbol của hợp đồng
     */
    @Override
    public void syncKLine(Symbol symbol) {
        log.debug("Đồng bộ K-line từ nguồn dữ liệu bên ngoài, symbol = {}", symbol.getValue());

        // Lấy thời gian hiện tại (giây)
        long currentTime = System.currentTimeMillis() / 1000;

        // Đồng bộ K-line cho từng khoảng thời gian
        for (String period : PERIODS) {
            // Lấy thời gian lớn nhất của K-line
            long fromTime = kLineRepository.findMaxTimeBySymbolAndPeriod(symbol, period);
            if (fromTime <= 1) {
                fromTime = 0;
            } else {
                fromTime = fromTime / 1000;
            }

            long timeGap = currentTime - fromTime;
            log.debug("Đồng bộ K-line, symbol = {}, period = {}, currentTime = {}, fromTime = {}, timeGap = {}",
                    symbol.getValue(), period, currentTime, fromTime, timeGap);

            // Đồng bộ K-line cho từng khoảng thời gian
            if (period.equals("1min") && timeGap >= 60) {
                syncKLineForPeriod(symbol, period, fromTime, currentTime, 60);
            } else if (period.equals("5min") && timeGap >= 60 * 5) {
                syncKLineForPeriod(symbol, period, fromTime, currentTime, 60 * 5);
            } else if (period.equals("15min") && timeGap >= 60 * 15) {
                syncKLineForPeriod(symbol, period, fromTime, currentTime, 60 * 15);
            } else if (period.equals("30min") && timeGap >= 60 * 30) {
                syncKLineForPeriod(symbol, period, fromTime, currentTime, 60 * 30);
            } else if (period.equals("60min") && timeGap >= 60 * 60) {
                syncKLineForPeriod(symbol, period, fromTime, currentTime, 60 * 60);
            } else if (period.equals("4hour") && timeGap >= 60 * 60 * 4) {
                syncKLineForPeriod(symbol, period, fromTime, currentTime, 60 * 60 * 4);
            } else if (period.equals("1day") && timeGap >= 60 * 60 * 24) {
                syncKLineForPeriod(symbol, period, fromTime, currentTime, 60 * 60 * 24);
            } else if (period.equals("1week") && timeGap >= 60 * 60 * 24 * 7) {
                syncKLineForPeriod(symbol, period, fromTime, currentTime, 60 * 60 * 24 * 7);
            } else if (period.equals("1mon") && timeGap >= 60 * 60 * 24 * 30) {
                syncKLineForPeriod(symbol, period, fromTime, currentTime, 60 * 60 * 24 * 30);
            }
        }
    }

    /**
     * Đồng bộ K-line cho một khoảng thời gian cụ thể
     * @param symbol Symbol của hợp đồng
     * @param period Khoảng thời gian
     * @param fromTime Thời gian bắt đầu
     * @param currentTime Thời gian hiện tại
     * @param interval Khoảng thời gian
     */
    private void syncKLineForPeriod(Symbol symbol, String period, long fromTime, long currentTime, long interval) {
        log.debug("Đồng bộ K-line cho khoảng thời gian cụ thể, symbol = {}, period = {}, fromTime = {}, currentTime = {}, interval = {}",
                symbol.getValue(), period, fromTime, currentTime, interval);

        if (fromTime == 0) {
            // Khởi tạo K-line, lấy K-line gần đây
            long time = currentTime * 1000;
            Money currentPrice = marketDataService.getLastPrice(symbol);

            // Tìm K-line trước đó
            KLine previousKLine = getLatestKLine(symbol, period);

            Money openPrice;
            Money highestPrice;
            Money lowestPrice;
            Money closePrice;
            BigDecimal volume;
            BigDecimal turnover;

            // Logic đơn giản theo market module: Khi không có giao dịch, O,H,L,C = closePrice của nến trước
            if (previousKLine != null && previousKLine.getClosePrice() != null && !previousKLine.getClosePrice().isZero()) {
                // Sử dụng giá đóng cửa của nến trước cho tất cả O,H,L,C (theo logic market module)
                Money previousClosePrice = previousKLine.getClosePrice();
                openPrice = previousClosePrice;
                highestPrice = previousClosePrice;
                lowestPrice = previousClosePrice;
                closePrice = previousClosePrice;

                log.debug("Sync K-line: Sử dụng giá đóng cửa của nến trước: {}", previousClosePrice.getValue());
            } else {
                // Nếu không có nến trước, sử dụng giá hiện tại
                openPrice = currentPrice;
                highestPrice = currentPrice;
                lowestPrice = currentPrice;
                closePrice = currentPrice;

                log.debug("Sync K-line: Không có nến trước, sử dụng giá hiện tại: {}", currentPrice.getValue());
            }
            // Không có giao dịch thì volume và turnover = 0 (theo logic market module)
            volume = BigDecimal.ZERO;
            turnover = BigDecimal.ZERO;

            createKLine(symbol, period, time, openPrice, highestPrice, lowestPrice, closePrice, 0, volume, turnover);
        } else {
            // Không phải khởi tạo, lấy K-line mới
            long timeGap = currentTime - fromTime;
            long toTime = fromTime + (timeGap / interval) * interval - 5;

            // Lấy K-line cuối cùng
            KLine lastKLine = getLatestKLine(symbol, period);

            // Tạo K-line cho từng khoảng thời gian bị thiếu
            for (long t = fromTime + interval; t <= toTime; t += interval) {
                long time = t * 1000;
                Money currentPrice = marketDataService.getLastPrice(symbol);

                Money openPrice;
                Money highestPrice;
                Money lowestPrice;
                Money closePrice;
                BigDecimal volume;
                BigDecimal turnover;

                // Logic đơn giản theo market module: Khi không có giao dịch, O,H,L,C = closePrice của nến trước
                if (lastKLine != null && lastKLine.getClosePrice() != null && !lastKLine.getClosePrice().isZero()) {
                    // Sử dụng giá đóng cửa của nến trước cho tất cả O,H,L,C (theo logic market module)
                    Money previousClosePrice = lastKLine.getClosePrice();
                    openPrice = previousClosePrice;
                    highestPrice = previousClosePrice;
                    lowestPrice = previousClosePrice;
                    closePrice = previousClosePrice;

                    log.debug("Sync K-line loop: Sử dụng giá đóng cửa của nến trước: {}", previousClosePrice.getValue());
                } else {
                    // Nếu không có nến trước, sử dụng giá hiện tại
                    openPrice = currentPrice;
                    highestPrice = currentPrice;
                    lowestPrice = currentPrice;
                    closePrice = currentPrice;

                    log.debug("Sync K-line loop: Không có nến trước, sử dụng giá hiện tại: {}", currentPrice.getValue());
                }
                // Không có giao dịch thì volume và turnover = 0 (theo logic market module)
                volume = BigDecimal.ZERO;
                turnover = BigDecimal.ZERO;

                createKLine(symbol, period, time, openPrice, highestPrice, lowestPrice, closePrice, 0, volume, turnover);

                // Cập nhật lastKLine cho lần lặp tiếp theo
                lastKLine = getLatestKLine(symbol, period);
            }
        }
    }
}
