<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="932b1a6d-7ce3-4a5e-b887-4fcc187efb34" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence"><![CDATA[{}]]></component>
  <component name="KubernetesApiProvider"><![CDATA[{
  "isMigrated": true
}]]></component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 5
}]]></component>
  <component name="ProjectId" id="2xfH8dNw8x3EZobMyt239E93KNS" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "dev",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Project/cex-be/wallet-rpc",
    "nodejs_package_manager_path": "npm",
    "run.configurations.included.in.services": "true",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.ActApplication">
    <configuration name="ActApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="act" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.icetea.lotus.walletrpc.ActApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BchWalletRpcApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="bch" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.icetea.lotus.walletrpc.BchWalletRpcApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BitcoinWalletRpcApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="bitcoin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.icetea.lotus.walletrpc.BitcoinWalletRpcApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BtmWalletRpcApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="btm" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.icetea.lotus.walletrpc.BtmWalletRpcApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="EctApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ect" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.icetea.lotus.walletrpc.EctApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="EosWalletRpcApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="eos" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.icetea.lotus.walletrpc.EosWalletRpcApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ErcTokenApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="erc-token" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.icetea.lotus.walletrpc.ErcTokenApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ErcTokenApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="erc-eusdt" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.icetea.lotus.walletrpc.ErcTokenApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="EthWalletApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="eth" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.icetea.lotus.walletrpc.EthWalletApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LtcWalletRpcApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ltc" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.icetea.lotus.walletrpc.LtcWalletRpcApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UsdtWalletRpcApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="usdt" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.icetea.lotus.walletrpc.UsdtWalletRpcApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WalletRpcApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="bsv" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.icetea.lotus.walletrpc.WalletRpcApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="XmrWalletRpcApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="xmr" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.icetea.lotus.walletrpc.XmrWalletRpcApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="932b1a6d-7ce3-4a5e-b887-4fcc187efb34" name="Changes" comment="" />
      <created>1748322049190</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748322049190</updated>
      <workItem from="1748322050244" duration="101000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>