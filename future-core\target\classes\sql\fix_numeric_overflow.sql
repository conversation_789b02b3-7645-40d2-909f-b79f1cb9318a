-- <PERSON><PERSON>t để kiểm tra và sửa lỗi numeric overflow trong PostgreSQL
-- Lỗi: numeric field overflow - A field with precision 18, scale 8 must round to an absolute value less than 10^10

-- 1. Kiểm tra dữ liệu vượt quá giới hạn NUMERIC(18,8) trong các bảng

-- Kiểm tra bảng contract_trade
SELECT 'contract_trade' as table_name, 'price' as column_name, COUNT(*) as overflow_count
FROM contract_trade 
WHERE ABS(price) >= 10000000000
UNION ALL
SELECT 'contract_trade' as table_name, 'volume' as column_name, COUNT(*) as overflow_count
FROM contract_trade 
WHERE ABS(volume) >= 10000000000
UNION ALL
SELECT 'contract_trade' as table_name, 'buy_fee' as column_name, COUNT(*) as overflow_count
FROM contract_trade 
WHERE ABS(buy_fee) >= 10000000000
UNION ALL
SELECT 'contract_trade' as table_name, 'sell_fee' as column_name, COUNT(*) as overflow_count
FROM contract_trade 
WHERE ABS(sell_fee) >= 10000000000
UNION ALL
SELECT 'contract_trade' as table_name, 'buy_turnover' as column_name, COUNT(*) as overflow_count
FROM contract_trade 
WHERE ABS(buy_turnover) >= 10000000000
UNION ALL
SELECT 'contract_trade' as table_name, 'sell_turnover' as column_name, COUNT(*) as overflow_count
FROM contract_trade 
WHERE ABS(sell_turnover) >= 10000000000;

-- Kiểm tra bảng contract_order
SELECT 'contract_order' as table_name, 'price' as column_name, COUNT(*) as overflow_count
FROM contract_order 
WHERE ABS(price) >= 10000000000
UNION ALL
SELECT 'contract_order' as table_name, 'trigger_price' as column_name, COUNT(*) as overflow_count
FROM contract_order 
WHERE ABS(trigger_price) >= 10000000000
UNION ALL
SELECT 'contract_order' as table_name, 'volume' as column_name, COUNT(*) as overflow_count
FROM contract_order 
WHERE ABS(volume) >= 10000000000
UNION ALL
SELECT 'contract_order' as table_name, 'deal_volume' as column_name, COUNT(*) as overflow_count
FROM contract_order 
WHERE ABS(deal_volume) >= 10000000000
UNION ALL
SELECT 'contract_order' as table_name, 'deal_money' as column_name, COUNT(*) as overflow_count
FROM contract_order 
WHERE ABS(deal_money) >= 10000000000
UNION ALL
SELECT 'contract_order' as table_name, 'fee' as column_name, COUNT(*) as overflow_count
FROM contract_order 
WHERE ABS(fee) >= 10000000000;

-- Kiểm tra bảng contract_position
SELECT 'contract_position' as table_name, 'volume' as column_name, COUNT(*) as overflow_count
FROM contract_position 
WHERE ABS(volume) >= 10000000000
UNION ALL
SELECT 'contract_position' as table_name, 'open_price' as column_name, COUNT(*) as overflow_count
FROM contract_position 
WHERE ABS(open_price) >= 10000000000
UNION ALL
SELECT 'contract_position' as table_name, 'close_price' as column_name, COUNT(*) as overflow_count
FROM contract_position 
WHERE ABS(close_price) >= 10000000000
UNION ALL
SELECT 'contract_position' as table_name, 'liquidation_price' as column_name, COUNT(*) as overflow_count
FROM contract_position 
WHERE ABS(liquidation_price) >= 10000000000
UNION ALL
SELECT 'contract_position' as table_name, 'maintenance_margin' as column_name, COUNT(*) as overflow_count
FROM contract_position 
WHERE ABS(maintenance_margin) >= 10000000000
UNION ALL
SELECT 'contract_position' as table_name, 'margin' as column_name, COUNT(*) as overflow_count
FROM contract_position 
WHERE ABS(margin) >= 10000000000
UNION ALL
SELECT 'contract_position' as table_name, 'profit' as column_name, COUNT(*) as overflow_count
FROM contract_position 
WHERE ABS(profit) >= 10000000000;

-- Kiểm tra bảng contract_financial_record
SELECT 'contract_financial_record' as table_name, 'amount' as column_name, COUNT(*) as overflow_count
FROM contract_financial_record 
WHERE ABS(amount) >= 10000000000
UNION ALL
SELECT 'contract_financial_record' as table_name, 'fee' as column_name, COUNT(*) as overflow_count
FROM contract_financial_record 
WHERE ABS(fee) >= 10000000000
UNION ALL
SELECT 'contract_financial_record' as table_name, 'profit' as column_name, COUNT(*) as overflow_count
FROM contract_financial_record 
WHERE ABS(profit) >= 10000000000;

-- 2. Sửa dữ liệu vượt quá giới hạn bằng cách cap về giá trị tối đa

-- Sửa bảng contract_trade
UPDATE contract_trade 
SET price = CASE 
    WHEN price > 9999999999.99999999 THEN 9999999999.99999999
    WHEN price < -9999999999.99999999 THEN -9999999999.99999999
    ELSE price
END
WHERE ABS(price) >= 10000000000;

UPDATE contract_trade 
SET volume = CASE 
    WHEN volume > 9999999999.99999999 THEN 9999999999.99999999
    WHEN volume < -9999999999.99999999 THEN -9999999999.99999999
    ELSE volume
END
WHERE ABS(volume) >= 10000000000;

UPDATE contract_trade 
SET buy_fee = CASE 
    WHEN buy_fee > 9999999999.99999999 THEN 9999999999.99999999
    WHEN buy_fee < -9999999999.99999999 THEN -9999999999.99999999
    ELSE buy_fee
END
WHERE ABS(buy_fee) >= 10000000000;

UPDATE contract_trade 
SET sell_fee = CASE 
    WHEN sell_fee > 9999999999.99999999 THEN 9999999999.99999999
    WHEN sell_fee < -9999999999.99999999 THEN -9999999999.99999999
    ELSE sell_fee
END
WHERE ABS(sell_fee) >= 10000000000;

UPDATE contract_trade 
SET buy_turnover = CASE 
    WHEN buy_turnover > 9999999999.99999999 THEN 9999999999.99999999
    WHEN buy_turnover < -9999999999.99999999 THEN -9999999999.99999999
    ELSE buy_turnover
END
WHERE ABS(buy_turnover) >= 10000000000;

UPDATE contract_trade 
SET sell_turnover = CASE 
    WHEN sell_turnover > 9999999999.99999999 THEN 9999999999.99999999
    WHEN sell_turnover < -9999999999.99999999 THEN -9999999999.99999999
    ELSE sell_turnover
END
WHERE ABS(sell_turnover) >= 10000000000;

-- Sửa bảng contract_order
UPDATE contract_order 
SET price = CASE 
    WHEN price > 9999999999.99999999 THEN 9999999999.99999999
    WHEN price < -9999999999.99999999 THEN -9999999999.99999999
    ELSE price
END
WHERE ABS(price) >= 10000000000;

UPDATE contract_order 
SET trigger_price = CASE 
    WHEN trigger_price > 9999999999.99999999 THEN 9999999999.99999999
    WHEN trigger_price < -9999999999.99999999 THEN -9999999999.99999999
    ELSE trigger_price
END
WHERE ABS(trigger_price) >= 10000000000;

UPDATE contract_order 
SET volume = CASE 
    WHEN volume > 9999999999.99999999 THEN 9999999999.99999999
    WHEN volume < -9999999999.99999999 THEN -9999999999.99999999
    ELSE volume
END
WHERE ABS(volume) >= 10000000000;

UPDATE contract_order 
SET deal_volume = CASE 
    WHEN deal_volume > 9999999999.99999999 THEN 9999999999.99999999
    WHEN deal_volume < -9999999999.99999999 THEN -9999999999.99999999
    ELSE deal_volume
END
WHERE ABS(deal_volume) >= 10000000000;

UPDATE contract_order 
SET deal_money = CASE 
    WHEN deal_money > 9999999999.99999999 THEN 9999999999.99999999
    WHEN deal_money < -9999999999.99999999 THEN -9999999999.99999999
    ELSE deal_money
END
WHERE ABS(deal_money) >= 10000000000;

UPDATE contract_order 
SET fee = CASE 
    WHEN fee > 9999999999.99999999 THEN 9999999999.99999999
    WHEN fee < -9999999999.99999999 THEN -9999999999.99999999
    ELSE fee
END
WHERE ABS(fee) >= 10000000000;

-- Sửa bảng contract_position
UPDATE contract_position 
SET volume = CASE 
    WHEN volume > 9999999999.99999999 THEN 9999999999.99999999
    WHEN volume < -9999999999.99999999 THEN -9999999999.99999999
    ELSE volume
END
WHERE ABS(volume) >= 10000000000;

UPDATE contract_position 
SET open_price = CASE 
    WHEN open_price > 9999999999.99999999 THEN 9999999999.99999999
    WHEN open_price < -9999999999.99999999 THEN -9999999999.99999999
    ELSE open_price
END
WHERE ABS(open_price) >= 10000000000;

UPDATE contract_position 
SET close_price = CASE 
    WHEN close_price > 9999999999.99999999 THEN 9999999999.99999999
    WHEN close_price < -9999999999.99999999 THEN -9999999999.99999999
    ELSE close_price
END
WHERE ABS(close_price) >= 10000000000;

UPDATE contract_position 
SET liquidation_price = CASE 
    WHEN liquidation_price > 9999999999.99999999 THEN 9999999999.99999999
    WHEN liquidation_price < -9999999999.99999999 THEN -9999999999.99999999
    ELSE liquidation_price
END
WHERE ABS(liquidation_price) >= 10000000000;

UPDATE contract_position 
SET maintenance_margin = CASE 
    WHEN maintenance_margin > 9999999999.99999999 THEN 9999999999.99999999
    WHEN maintenance_margin < -9999999999.99999999 THEN -9999999999.99999999
    ELSE maintenance_margin
END
WHERE ABS(maintenance_margin) >= 10000000000;

UPDATE contract_position 
SET margin = CASE 
    WHEN margin > 9999999999.99999999 THEN 9999999999.99999999
    WHEN margin < -9999999999.99999999 THEN -9999999999.99999999
    ELSE margin
END
WHERE ABS(margin) >= 10000000000;

UPDATE contract_position 
SET profit = CASE 
    WHEN profit > 9999999999.99999999 THEN 9999999999.99999999
    WHEN profit < -9999999999.99999999 THEN -9999999999.99999999
    ELSE profit
END
WHERE ABS(profit) >= 10000000000;

-- Sửa bảng contract_financial_record
UPDATE contract_financial_record 
SET amount = CASE 
    WHEN amount > 9999999999.99999999 THEN 9999999999.99999999
    WHEN amount < -9999999999.99999999 THEN -9999999999.99999999
    ELSE amount
END
WHERE ABS(amount) >= 10000000000;

UPDATE contract_financial_record 
SET fee = CASE 
    WHEN fee > 9999999999.99999999 THEN 9999999999.99999999
    WHEN fee < -9999999999.99999999 THEN -9999999999.99999999
    ELSE fee
END
WHERE ABS(fee) >= 10000000000;

UPDATE contract_financial_record 
SET profit = CASE 
    WHEN profit > 9999999999.99999999 THEN 9999999999.99999999
    WHEN profit < -9999999999.99999999 THEN -9999999999.99999999
    ELSE profit
END
WHERE ABS(profit) >= 10000000000;

-- 3. Kiểm tra lại sau khi sửa
SELECT 'Kiểm tra lại sau khi sửa:' as message;

SELECT 'contract_trade' as table_name, 'price' as column_name, COUNT(*) as overflow_count
FROM contract_trade 
WHERE ABS(price) >= 10000000000
UNION ALL
SELECT 'contract_order' as table_name, 'price' as column_name, COUNT(*) as overflow_count
FROM contract_order 
WHERE ABS(price) >= 10000000000
UNION ALL
SELECT 'contract_position' as table_name, 'volume' as column_name, COUNT(*) as overflow_count
FROM contract_position 
WHERE ABS(volume) >= 10000000000
UNION ALL
SELECT 'contract_financial_record' as table_name, 'amount' as column_name, COUNT(*) as overflow_count
FROM contract_financial_record 
WHERE ABS(amount) >= 10000000000;
