package com.icetea.lotus.controller;

import com.icetea.lotus.config.security.CurrentUser;
import com.icetea.lotus.entity.FavorSymbol;
import com.icetea.lotus.entity.transform.AuthMember;
import com.icetea.lotus.service.FavorSymbolService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/favor")
public class FavorController {

    private final FavorSymbolService favorSymbolService;
    private final LocaleMessageSourceService msService;

    /**
     * Add a favor
     *
     * @param member
     * @param symbol
     * @return
     */
    @RequestMapping("add")
    public MessageResult addFavor(@CurrentUser AuthMember member, String symbol) {
        if (StringUtils.isEmpty(symbol)) {
            return MessageResult.error(500, msService.getMessage("SYMBOL_CANNOT_BE_EMPTY"));
        }
        FavorSymbol favorSymbol = favorSymbolService.findByMemberIdAndSymbol(member.getId(), symbol);
        if (favorSymbol != null) {
            return MessageResult.error(500, msService.getMessage("SYMBOL_ALREADY_FAVORED"));
        }
        FavorSymbol favor = favorSymbolService.add(member.getId(), symbol);
        if (favor != null) {
            return MessageResult.success(msService.getMessage("EXAPI_SUCCESS"));
        }
        return MessageResult.error(msService.getMessage("EXAPI_ERROR"));
    }

    /**
     * Find a favor
     *
     * @param member
     * @return
     */
    @RequestMapping("find")
    public List<FavorSymbol> findFavor(@CurrentUser AuthMember member) {
        return favorSymbolService.findByMemberId(member.getId());
    }

    /**
     * Delete a favor
     *
     * @param member
     * @param symbol
     * @return
     */
    @RequestMapping("delete")
    public MessageResult deleteFavor(@CurrentUser AuthMember member, String symbol) {
        if (StringUtils.isEmpty(symbol)) {
            return MessageResult.error(msService.getMessage("SYMBOL_CANNOT_BE_EMPTY"));
        }
        FavorSymbol favorSymbol = favorSymbolService.findByMemberIdAndSymbol(member.getId(), symbol);
        if (favorSymbol == null) {
            return MessageResult.error(msService.getMessage("FAVOR_NOT_EXISTS"));
        }
        favorSymbolService.delete(member.getId(), symbol);
        return MessageResult.success(msService.getMessage("EXAPI_SUCCESS"));
    }
}
