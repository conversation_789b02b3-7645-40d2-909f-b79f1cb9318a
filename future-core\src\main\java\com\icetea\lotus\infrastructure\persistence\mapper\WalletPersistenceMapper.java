package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.Wallet;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.WalletId;
import com.icetea.lotus.infrastructure.persistence.entity.WalletJpaEntity;
import com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator;
import org.springframework.stereotype.Component;

/**
 * Mapper cho Wallet và WalletJpaEntity
 * Chuyển đổi giữa domain entity và JPA entity
 */
@Component
public class WalletPersistenceMapper {

    /**
     * <PERSON>y<PERSON>n đổi từ JPA entity sang domain entity
     * @param entity JPA entity
     * @return Domain entity
     */
    public Wallet entityToDomain(WalletJpaEntity entity) {
        if (entity == null) {
            return null;
        }

        return Wallet.builder()
                .id(WalletId.of(entity.getId()))
                .memberId(entity.getMemberId())
                .coin(entity.getCoin())
                .balance(Money.of(entity.getBalance()))
                .frozenBalance(Money.of(entity.getFrozenBalance()))
                .availableBalance(Money.of(entity.getAvailableBalance()))
                .address(entity.getAddress())
                .unrealizedPnl(Money.of(entity.getUnrealizedPnl()))
                .realizedPnl(Money.of(entity.getRealizedPnl()))
                .usedMargin(Money.of(entity.getUsedMargin()))
                .totalFee(Money.of(entity.getTotalFee()))
                .totalFundingFee(Money.of(entity.getTotalFundingFee()))
                .isLocked(entity.isLocked())
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .build();
    }

    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domain Domain entity
     * @return JPA entity
     */
    public WalletJpaEntity domainToEntity(Wallet domain) {
        if (domain == null) {
            return null;
        }

        return WalletJpaEntity.builder()
                .id(domain.getId() != null ? domain.getId().getValue() : null)
                .memberId(domain.getMemberId())
                .coin(domain.getCoin())
                .balance(BigDecimalValidator.validateAndScale26_16(domain.getBalance().getValue(), "balance"))
                .frozenBalance(BigDecimalValidator.validateAndScale26_16(domain.getFrozenBalance().getValue(), "frozenBalance"))
                .availableBalance(BigDecimalValidator.validateAndScale(domain.getAvailableBalance().getValue(), "availableBalance"))
                .address(domain.getAddress())
                .unrealizedPnl(BigDecimalValidator.validateAndScale(domain.getUnrealizedPnl().getValue(), "unrealizedPnl"))
                .realizedPnl(BigDecimalValidator.validateAndScale(domain.getRealizedPnl().getValue(), "realizedPnl"))
                .usedMargin(BigDecimalValidator.validateAndScale(domain.getUsedMargin().getValue(), "usedMargin"))
                .totalFee(BigDecimalValidator.validateAndScale(domain.getTotalFee().getValue(), "totalFee"))
                .totalFundingFee(BigDecimalValidator.validateAndScale(domain.getTotalFundingFee().getValue(), "totalFundingFee"))
                .isLocked(domain.isLocked())
                .createTime(domain.getCreateTime())
                .updateTime(domain.getUpdateTime())
                .build();
    }
}
