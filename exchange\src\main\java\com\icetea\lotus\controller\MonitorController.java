package com.icetea.lotus.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.Trader.CoinTrader;
import com.icetea.lotus.Trader.CoinTraderFactory;
import com.icetea.lotus.entity.ExchangeCoin;
import com.icetea.lotus.entity.ExchangeOrder;
import com.icetea.lotus.entity.ExchangeOrderDetail;
import com.icetea.lotus.entity.ExchangeOrderDirection;
import com.icetea.lotus.entity.ExchangeOrderType;
import com.icetea.lotus.entity.TradePlateItem;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.service.ExchangeOrderDetailService;
import com.icetea.lotus.service.ExchangeOrderService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Controller for monitoring trading operations, managing CoinTrader instances, and interacting with trading engines.
 * It provides endpoints for retrieving trader details, trade plates, and trading engine statuses,
 * as well as for resetting trade engines and fetching trading symbols.
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/monitor")
public class MonitorController {
    private final Logger log = LoggerFactory.getLogger(MonitorController.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    private final CoinTraderFactory factory;

    private final ExchangeOrderService exchangeOrderService;

    private final ExchangeOrderDetailService exchangeOrderDetailService;

    private final KafkaTemplate<String, String> kafkaTemplate;

    private final ExchangeCoinService exchangeCoinService;
    /**
     * Reset the trading engine and start over (the application scenario is mainly to prevent dirty data on the plate disk (inverted, etc.)
     *
     * @param symbol
     * @return
     */
    @Autowired
    private LocaleMessageSourceService msService;

    @RequestMapping("overview")
    public ObjectNode traderOverview(String symbol) {
        CoinTrader trader = factory.getTrader(symbol);
        if (trader == null) {
            return null;
        }
        ObjectNode result = objectMapper.createObjectNode();
        // Selling information
        ObjectNode ask = objectMapper.createObjectNode();
        // Buying information
        ObjectNode bid = objectMapper.createObjectNode();
        ask.put("limit_price_order_count", trader.getLimitPriceOrderCount(ExchangeOrderDirection.SELL));
        ask.put("market_price_order_count", trader.getSellMarketQueue().size());
        ask.put("depth", trader.getTradePlate(ExchangeOrderDirection.SELL).getDepth());
        bid.put("limit_price_order_count", trader.getLimitPriceOrderCount(ExchangeOrderDirection.BUY));
        bid.put("market_price_order_count", trader.getBuyMarketQueue().size());
        bid.put("depth", trader.getTradePlate(ExchangeOrderDirection.BUY).getDepth());
        result.set("ask", ask);
        result.set("bid", bid);
        return result;
    }

    @RequestMapping("trader-detail")
    public ObjectNode traderDetail(String symbol) {
        CoinTrader trader = factory.getTrader(symbol);
        if (trader == null) {
            return null;
        }
        ObjectNode result = objectMapper.createObjectNode();
        // Selling information
        ObjectNode ask = objectMapper.createObjectNode();
        // Buying information
        ObjectNode bid = objectMapper.createObjectNode();

        // For complex objects like TreeMap and LinkedList, we need to convert them to JSON strings
        try {
            ask.put("limit_price_queue", objectMapper.writeValueAsString(trader.getSellLimitPriceQueue()));
            ask.put("market_price_queue", objectMapper.writeValueAsString(trader.getSellMarketQueue()));
            bid.put("limit_price_queue", objectMapper.writeValueAsString(trader.getBuyLimitPriceQueue()));
            bid.put("market_price_queue", objectMapper.writeValueAsString(trader.getBuyMarketQueue()));
        } catch (JsonProcessingException e) {
            log.error("Error serializing trader detail to JSON", e);
        }

        result.set("ask", ask);
        result.set("bid", bid);
        return result;
    }

    @RequestMapping("plate")
    public Map<String, List<TradePlateItem>> traderPlate(String symbol) {
        Map<String, List<TradePlateItem>> result = new HashMap<>();
        CoinTrader trader = factory.getTrader(symbol);
        if (trader == null) {
            return null;
        }
        result.put("bid", trader.getTradePlate(ExchangeOrderDirection.BUY).getItems());
        result.put("ask", trader.getTradePlate(ExchangeOrderDirection.SELL).getItems());
        return result;
    }

    @RequestMapping("plate-mini")
    public Map<String, ObjectNode> traderPlateMini(String symbol) {
        Map<String, ObjectNode> result = new HashMap<>();
        CoinTrader trader = factory.getTrader(symbol);
        if (trader == null) {
            return null;
        }
        // Convert the JSONObject returned by toJSON to ObjectNode
        try {
            String bidJson = objectMapper.writeValueAsString(trader.getTradePlate(ExchangeOrderDirection.BUY).toJSON(24));
            String askJson = objectMapper.writeValueAsString(trader.getTradePlate(ExchangeOrderDirection.SELL).toJSON(24));
            result.put("bid", (ObjectNode) objectMapper.readTree(bidJson));
            result.put("ask", (ObjectNode) objectMapper.readTree(askJson));
        } catch (JsonProcessingException e) {
            log.error("Error converting trade plate to JSON", e);
        }
        return result;
    }

    @RequestMapping("plate-full")
    public Map<String, ObjectNode> traderPlateFull(String symbol) {
        Map<String, ObjectNode> result = new HashMap<>();
        CoinTrader trader = factory.getTrader(symbol);
        if (trader == null) {
            return null;
        }
        // Convert the JSONObject returned by toJSON to ObjectNode
        try {
            String bidJson = objectMapper.writeValueAsString(trader.getTradePlate(ExchangeOrderDirection.BUY).toJSON(100));
            String askJson = objectMapper.writeValueAsString(trader.getTradePlate(ExchangeOrderDirection.SELL).toJSON(100));
            result.put("bid", (ObjectNode) objectMapper.readTree(bidJson));
            result.put("ask", (ObjectNode) objectMapper.readTree(askJson));
        } catch (JsonProcessingException e) {
            log.error("Error converting trade plate to JSON", e);
        }
        return result;
    }

    @RequestMapping("symbols")
    public List<String> symbols() {
        Map<String, CoinTrader> traders = factory.getTraderMap();
        List<String> symbols = new ArrayList<>();
        traders.forEach((key, trader) -> {
            symbols.add(key);
        });
        return symbols;
    }

    @RequestMapping("engines")
    public Map<String, Integer> engines() {
        log.info("START engines:");
        Map<String, CoinTrader> traders = factory.getTraderMap();
        log.info("engines traders: {}", traders);
        Map<String, Integer> symbols = new HashMap<String, Integer>();
        traders.forEach((key, trader) -> {
            if (trader.isTradingHalt()) {
                symbols.put(key, 2);
            } else {
                symbols.put(key, 1);
            }
        });
        return symbols;
    }

    /**
     * Find an order
     *
     * @param symbol
     * @param orderId
     * @param direction
     * @param type
     * @return
     */
    @RequestMapping("order")
    public ExchangeOrder findOrder(String symbol, String orderId, ExchangeOrderDirection direction,
                                   ExchangeOrderType type) {
        CoinTrader trader = factory.getTrader(symbol);
        return trader.findOrder(orderId, type, direction);
    }

    @RequestMapping("reset-trader")
    public MessageResult resetTrader(String symbol) {
        log.info("======[Start]Reset CoinTrader: {} ======", symbol);
        if (factory.containsTrader(symbol)) {
            // The transaction pair engine does not exist, then it is created
            // Check whether the currency exists in the database definition
            ExchangeCoin coin = exchangeCoinService.findBySymbol(symbol);
            if (coin == null || coin.getEnable() != 1) {
                return MessageResult.error(500, "CURRENCY_PAIR_DOES_NOT_EXIST");
            }

            if (coin.getEnable() != 1) {
                return MessageResult.error(500, "PROHIBITION_OF_CURRENCY_PAIRS");
            }

            CoinTrader trader = factory.getTrader(symbol);
            if (!trader.isTradingHalt()) {
                return MessageResult.error(500, "STOP_CURRENT_ENGINE");
            }
            CoinTrader newTrader = new CoinTrader(symbol);
            newTrader.setKafkaTemplate(kafkaTemplate);
            newTrader.setBaseCoinScale(coin.getBaseCoinScale());
            newTrader.setCoinScale(coin.getCoinScale());
            newTrader.setPublishType(coin.getPublishType());
            newTrader.setClearTime(coin.getClearTime());

            // After successful creation, unprocessed orders need to be preprocessed
            log.info("======CoinTrader Process: " + symbol + "======");
            List<ExchangeOrder> orders = exchangeOrderService.findAllTradingOrderBySymbol(symbol);
            List<ExchangeOrder> tradingOrders = new ArrayList<>();
            List<ExchangeOrder> completedOrders = new ArrayList<>();
            orders.forEach(order -> {
                BigDecimal tradedAmount = BigDecimal.ZERO;
                BigDecimal turnover = BigDecimal.ZERO;
                List<ExchangeOrderDetail> details = exchangeOrderDetailService.findAllByOrderId(order.getOrderId());

                for (ExchangeOrderDetail od : details) {
                    tradedAmount = tradedAmount.add(od.getAmount());
                    turnover = turnover.add(od.getAmount().multiply(od.getPrice()));
                }
                order.setTradedAmount(tradedAmount);
                order.setTurnover(turnover);
                if (!order.isCompleted()) {
                    tradingOrders.add(order);
                } else {
                    completedOrders.add(order);
                }
            });
            try {
                newTrader.trade(tradingOrders);
            } catch (ParseException e) {
                e.printStackTrace();
                log.info("Abnormal：trader.trade(tradingOrders);");
                return MessageResult.error(500, symbol + msService.getMessage("ENGINE_CREATION_FAILED"));
            }
            // Determine the completed order send message notification
            if (!completedOrders.isEmpty()) {
                try {
                    kafkaTemplate.send("exchange-order-completed", objectMapper.writeValueAsString(completedOrders));
                } catch (JsonProcessingException e) {
                    log.error("Error serializing completedOrders to JSON", e);
                }
            }
            newTrader.setReady(true);

            factory.resetTrader(symbol, newTrader);
            log.info("======[END]Reset CoinTrader: {} successful======", symbol);
            return MessageResult.success(symbol + msService.getMessage("ENGINE_CREATED_SUCCESSFULLY"));
        } else {
            return MessageResult.error(500, symbol + msService.getMessage("ENGINE_DOES_NOT_EXIST"));
        }
    }

    @RequestMapping("start-trader")
    public MessageResult startTrader(String symbol) {
        log.info("======Start CoinTrader: {}======", symbol);
        if (!factory.containsTrader(symbol)) {
            // The transaction pair engine does not exist, then it is created
            // Check whether the currency exists in the database definition
            ExchangeCoin coin = exchangeCoinService.findBySymbol(symbol);
            if (coin == null || coin.getEnable() != 1) {
                return MessageResult.error(500, "CURRENCY_PAIR_DOES_NOT_EXIST");
            }
            if (coin.getEnable() != 1) {
                return MessageResult.error(500, "PROHIBITION_OF_CURRENCY_PAIRS");
            }
            CoinTrader newTrader = new CoinTrader(symbol);
            newTrader.setKafkaTemplate(kafkaTemplate);
            newTrader.setBaseCoinScale(coin.getBaseCoinScale());
            newTrader.setCoinScale(coin.getCoinScale());
            newTrader.setPublishType(coin.getPublishType());
            newTrader.setClearTime(coin.getClearTime());

            // After successful creation, unprocessed orders need to be preprocessed
            log.info("======CoinTrader Process: {}======", symbol);
            List<ExchangeOrder> orders = exchangeOrderService.findAllTradingOrderBySymbol(symbol);
            List<ExchangeOrder> tradingOrders = new ArrayList<>();
            List<ExchangeOrder> completedOrders = new ArrayList<>();
            orders.forEach(order -> {
                BigDecimal tradedAmount = BigDecimal.ZERO;
                BigDecimal turnover = BigDecimal.ZERO;
                List<ExchangeOrderDetail> details = exchangeOrderDetailService.findAllByOrderId(order.getOrderId());

                for (ExchangeOrderDetail od : details) {
                    tradedAmount = tradedAmount.add(od.getAmount());
                    turnover = turnover.add(od.getAmount().multiply(od.getPrice()));
                }
                order.setTradedAmount(tradedAmount);
                order.setTurnover(turnover);
                if (!order.isCompleted()) {
                    tradingOrders.add(order);
                } else {
                    completedOrders.add(order);
                }
            });
            try {
                newTrader.trade(tradingOrders);
            } catch (ParseException e) {
                e.printStackTrace();
                log.info("Abnormal：trader.trade(tradingOrders);");
                return MessageResult.error(500, "ENGINE_CREATION_FAILED");
            }
            // Determine the completed order send message notification
            if (!completedOrders.isEmpty()) {
                try {
                    kafkaTemplate.send("exchange-order-completed", objectMapper.writeValueAsString(completedOrders));
                } catch (JsonProcessingException e) {
                    log.error("Error serializing completedOrders to JSON", e);
                }
            }
            newTrader.setReady(true);
            factory.addTrader(symbol, newTrader);

            return MessageResult.success("CURRENCY_PAIR_CREATED_SUCCESSFULLY");
        } else {
            CoinTrader trader = factory.getTrader(symbol);
            if (trader.isTradingHalt()) {
                trader.resumeTrading();
                return MessageResult.success("ENGINE_STATE_HAS_STOPPED");
            } else {
                return MessageResult.error(500, "ENGINE_STATUS_IS_RUNNING");
            }
        }
    }

    @RequestMapping("stop-trader")
    public MessageResult stopTrader(String symbol) {
        CoinTrader trader = factory.getTrader(symbol);
        log.info("======Stop CoinTrader: {}======", symbol);
        if (trader == null) {
            return MessageResult.error(500, symbol + msService.getMessage("CURRENCY_PAIR_ENGINE_DOES_NOT_EXIST"));
        } else {
            if (trader.isTradingHalt()) {
                return MessageResult.error(500, symbol + msService.getMessage("ENGINE_STATE_HAS_STOPPED"));
            } else {
                trader.haltTrading();
                return MessageResult.success("ENGINE_STOPPED_SUCCESSFULLY");
            }
        }
    }
}
