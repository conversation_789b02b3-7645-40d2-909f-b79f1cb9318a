server.port=7003
spring.application.name=service-rpc-eth
#server.context-path=/wallet-rpc-eth
#kafka
spring.kafka.bootstrap-servers=${KAFKA_BOOTSTRAP_SERVER:************:9092}
spring.kafka.consumer.group-id=default-group
spring.kafka.template.default-topic=test
spring.kafka.listener.concurrency=1
spring.kafka.producer.batch-size=1000

# mongodb

spring.data.mongodb.uri=${MONGO_URI:****************************************************}

# Consul Host and Port
spring.cloud.consul.host=${CONSUL_HOST:************}
spring.cloud.consul.port=${CONSUL_PORT:8500}

# Enable Consul Config & Discovery
spring.cloud.consul.discovery.enabled=${CONSUL_DISCOVERY:true}
spring.cloud.consul.discovery.service-name=service-rpc-eth
#spring.cloud.consul.config.enabled=${CONSUL_CONFIG:true}
#spring.cloud.consul.config.fail-fast=false
spring.cloud.consul.discovery.register=true
spring.cloud.consul.discovery.instance-id=service-rpc-eth

# Optional: Add health check configuration
spring.cloud.consul.discovery.health-check-path=/actuator/health
spring.cloud.consul.discovery.prefer-ip-address=true
spring.cloud.consul.discovery.health-check-interval=10s

management.context-path=/actuator

# Actuator settings
endpoints.health.sensitive=false
endpoints.info.sensitive=false
endpoints.metrics.sensitive=false
management.security.enabled=false

coin.rpc=${COIN_RPC:http://127.0.0.1:8545}
coin.name=Sepolia ETH
coin.unit=ETH
coin.keystore-path=/home/<USER>/ethereum/sepolia/keystore
#coin.keystore-path=/home/<USER>/code/cex/wallet-rpc/eth

#coin.init-block-height=8336120
#coin.step=10
coin.withdraw-wallet=UTC--2025-04-23T03-40-30.585399612Z--0ec88c72b33b35d6af67c447132bfc85bc62c7f8
coin.withdraw-wallet-password=Icetea@123
coin.gas-limit=40000
coin.min-collect-amount=0.00001
coin.ignore-from-address=******************************************
watcher.init-block-height=latest
watcher.step=10
watcher.confirmation=0
watcher.interval=5000

#coin.withdraw-wallet=UTC--2025-05-15T07-13-06.721000000Z--81b50fa310971009e089aa539afab2766590063b.json
#coin.withdraw-wallet-password=123123
#coin.gas-limit=40000
#coin.min-collect-amount=0.00001
#coin.ignore-from-address=******************************************
#watcher.init-block-height=latest
#watcher.step=10
#watcher.confirmation=0
#watcher.interval=5000
