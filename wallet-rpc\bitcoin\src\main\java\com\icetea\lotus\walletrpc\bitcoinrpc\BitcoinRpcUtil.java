package com.icetea.lotus.walletrpc.bitcoinrpc;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Utility class for Bitcoin operations
 */
@Slf4j
public class BitcoinRpcUtil {

    /**
     * Sends a transaction to the given address with the specified amount and fee
     *
     * @param client the Bitcoin RPC client
     * @param address the address to send to
     * @param amount the amount to send
     * @param fee the fee to pay
     * @return the transaction ID
     * @throws BitcoinRpcException if there's an error making the request
     */
    public static String sendTransaction(BitcoinRpcClient client, String address, BigDecimal amount, BigDecimal fee) throws BitcoinRpcException {
        try {
            // For simple transactions, we can use sendtoaddress
            if (fee == null || fee.compareTo(BigDecimal.ZERO) <= 0) {
                return client.sendToAddress(address, amount);
            }
            
            // For transactions with a specific fee, we need to create a raw transaction
            // Get unspent outputs
            List<String> addresses = new ArrayList<>();
            List<BitcoinRpcClient.Unspent> unspent = client.listUnspent(1, 9999999, addresses);
            
            // Calculate total amount needed (amount + fee)
            BigDecimal totalNeeded = amount.add(fee);
            
            // Find inputs that cover the amount
            List<Map<String, Object>> inputs = new ArrayList<>();
            BigDecimal totalInput = BigDecimal.ZERO;
            
            for (BitcoinRpcClient.Unspent output : unspent) {
                if (output.isSpendable()) {
                    Map<String, Object> input = new HashMap<>();
                    input.put("txid", output.getTxid());
                    input.put("vout", output.getVout());
                    inputs.add(input);
                    
                    totalInput = totalInput.add(output.getAmount());
                    if (totalInput.compareTo(totalNeeded) >= 0) {
                        break;
                    }
                }
            }
            
            if (totalInput.compareTo(totalNeeded) < 0) {
                throw new BitcoinRpcException("Not enough funds to cover amount and fee");
            }
            
            // Calculate change
            BigDecimal change = totalInput.subtract(totalNeeded);
            
            // Create outputs
            Map<String, BigDecimal> outputs = new HashMap<>();
            outputs.put(address, amount);
            
            // Add change output if needed
            if (change.compareTo(BigDecimal.ZERO) > 0) {
                // Get a change address
                String changeAddress = client.getNewAddress("change");
                outputs.put(changeAddress, change);
            }
            
            // Create and sign the transaction
            String rawTx = client.createRawTransaction(inputs, outputs);
            BitcoinRpcClient.SignedTransaction signedTx = client.signRawTransaction(rawTx);
            
            if (!signedTx.isComplete()) {
                throw new BitcoinRpcException("Failed to sign transaction");
            }
            
            // Send the transaction
            return client.sendRawTransaction(signedTx.getHex());
        } catch (BitcoinRpcException e) {
            log.error("Error sending transaction: {}", e.getMessage());
            throw e;
        }
    }
}