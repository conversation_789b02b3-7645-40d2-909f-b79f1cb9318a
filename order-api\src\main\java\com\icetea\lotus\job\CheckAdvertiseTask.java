package com.icetea.lotus.job;

import com.icetea.lotus.coin.CoinExchangeFactory;
import com.icetea.lotus.core.DataException;
import com.icetea.lotus.entity.OtcCoin;
import com.icetea.lotus.exception.InformationExpiredException;
import com.icetea.lotus.service.AdvertiseService;
import com.icetea.lotus.service.OtcCoinService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

//@Component
@Slf4j
@RequiredArgsConstructor
public class CheckAdvertiseTask {

    private final CoinExchangeFactory coins;

    private final OtcCoinService otcCoinService;

    private final AdvertiseService advertiseService;

    @Scheduled(fixedRate = 60000 * 30)
    public void checkExpireOrder() {
        log.info("=========Start checking automatically delisted advertisements===========");
        // Supported coins
        List<OtcCoin> list = otcCoinService.getNormalCoin();
        Map<String, BigDecimal> map = coins.getCoins().get("CNY");
        list.forEach(
                x -> {
                    BigDecimal marketPrice = map.get(x.getUnit());
                    try {
                        List<Map<String, String>> list1 = advertiseService.selectSellAutoOffShelves(x.getId(), marketPrice, x.getJyRate());
                        List<Map<String, String>> list2 = advertiseService.selectBuyAutoOffShelves(x.getId(), marketPrice);
                        list1.addAll(list2);
                        list1.forEach(
                                y -> {
                                    try {
                                        advertiseService.autoPutOffShelves(y, x);
                                    } catch (InformationExpiredException e) {
                                        e.printStackTrace();
                                        log.warn("{} number ad: automatic removal failed", y.get("id"));
                                    }
                                }
                        );
                    } catch (SQLException | DataException e) {
                        e.printStackTrace();
                    }
                }
        );
        log.info("=========End checking automatically delinted advertisements===========");
    }
}
