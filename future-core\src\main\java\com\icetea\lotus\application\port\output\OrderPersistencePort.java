package com.icetea.lotus.application.port.output;

import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.entity.OrderDirection;
import com.icetea.lotus.core.domain.entity.OrderStatus;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.api.request.FindOrderRequest;
import com.icetea.lotus.infrastructure.persistence.entity.OrderJpaEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Port interface cho việc lưu trữ Order
 * Định nghĩa các phương thức để lưu trữ Order
 */
public interface OrderPersistencePort {

    /**
     * Tìm lệnh theo orderId
     * @param orderId ID của lệnh
     * @return Optional chứa lệnh nếu tìm thấy
     */
    Optional<Order> findByOrderId(OrderId orderId);

    /**
     * Tìm các lệnh theo memberId
     * @param memberId ID của thành viên
     * @return Danh sách các lệnh
     */
    List<Order> findByMemberId(Long memberId);

    /**
     * Tìm các lệnh theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Danh sách các lệnh
     */
    List<Order> findByMemberIdAndSymbol(Long memberId, Symbol symbol);

    /**
     * Tìm các lệnh theo status
     * @param status Trạng thái lệnh
     * @return Danh sách các lệnh
     */
    List<Order> findByStatus(OrderStatus status);

    /**
     * Tìm các lệnh theo status và thời gian hết hạn
     * @param status Trạng thái lệnh
     * @param expireTime Thời gian hết hạn
     * @return Danh sách các lệnh
     */
    List<Order> findByStatusAndExpireTimeBefore(OrderStatus status, LocalDateTime expireTime);

    /**
     * Tìm các lệnh theo status và thời gian thực hiện
     * @param status Trạng thái lệnh
     * @param executeTime Thời gian thực hiện
     * @return Danh sách các lệnh
     */
    List<Order> findByStatusAndExecuteTimeBefore(OrderStatus status, LocalDateTime executeTime);

    /**
     * Tìm các lệnh theo memberId, symbol và danh sách status
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param statuses Danh sách trạng thái lệnh
     * @return Danh sách các lệnh
     */
    List<Order> findByMemberIdAndSymbolAndStatusIn(Long memberId, Symbol symbol, List<OrderStatus> statuses);

    /**
     * Lưu lệnh
     * @param order Lệnh cần lưu
     * @return Lệnh đã được lưu
     */
    Order save(Order order);

    /**
     * Xóa lệnh
     * @param order Lệnh cần xóa
     */
    void delete(Order order);

    /**
     * Xóa lệnh theo orderId
     * @param orderId ID của lệnh
     */
    void deleteByOrderId(OrderId orderId);

    /**
     * Lấy danh sách order theo yêu cầu
     * @param request yêu cầu
     * @return Danh sách order book
     */
    Page<OrderJpaEntity> findOrder(FindOrderRequest request, OrderDirection direction, Pageable pageable);
}
