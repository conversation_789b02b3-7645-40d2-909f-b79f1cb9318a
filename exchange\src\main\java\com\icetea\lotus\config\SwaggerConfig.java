package com.icetea.lotus.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("exchange")
                        .version("1.0")
                        .description("API Exchange documentation"));
    }

//    @Bean
//    public GroupedOpenApi apiAdmin() {
//        return GroupedOpenApi.builder()
//                .group("admin")
//                .packagesToScan("com.icetea.lotus.controller")
//                .build();
//    }

}
