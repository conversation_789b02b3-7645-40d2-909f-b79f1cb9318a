package com.icetea.lotus.walletrpc.controller;

import com.icetea.lotus.walletrpc.bitcoinrpc.BitcoinRpcClient;
import com.icetea.lotus.walletrpc.bitcoinrpc.BitcoinRpcException;
import com.icetea.lotus.walletrpc.bitcoinrpc.BitcoinRpcUtil;
import com.icetea.lotus.walletrpc.service.AccountService;
import com.icetea.lotus.walletrpc.util.MessageResult;
import com.spark.blockchain.rpcclient.BitcoinException;
import com.spark.blockchain.rpcclient.BitcoinRPCClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.util.Map;

@RestController
@RequestMapping("/rpc")
@Slf4j
@RequiredArgsConstructor
public class WalletController {

    private final BitcoinRpcClient rpcClient;
    private final AccountService accountService;

    @Value("${coin.rpc}")
    private String uri;

    @GetMapping("height")
    public MessageResult getHeight() {
        try {
            int height = rpcClient.getBlockCount();
            MessageResult result = new MessageResult(0, "success");
            result.setData(height - 1);
            return result;
        } catch (BitcoinRpcException e) {
            log.error("Failed to get block height: {}", e.getMessage(), e);
            return MessageResult.error(500, "The query failed, error:" + e.getMessage());
        }
    }

    @GetMapping("address/{account}")
    public MessageResult getNewAddress(@PathVariable String account) {
        log.info("Creating new address for account: {}", account);
        try {
            StringBuilder uriBuilder = new StringBuilder(uri);
            uriBuilder.append("wallet/");
            uriBuilder.append(account);
            BitcoinRPCClient client = new BitcoinRPCClient(uri.toString());
            String address = client.getNewAddress(account);
//            String address = rpcClient.getNewAddress(account);
            accountService.saveOne(account, address);
            MessageResult result = new MessageResult(0, "success");
            result.setData(address);
            return result;
        } catch (BitcoinException e) {
            log.error("RPC error creating address: {}", e.getMessage(), e);
            return MessageResult.error(500, "RPC error: " + e.getMessage());
        } catch (Exception e) {
            log.error("ERROR: ", e);
            throw new RuntimeException(e);
        }
    }

    @GetMapping({"transfer", "withdraw"})
    public MessageResult withdraw(String address, BigDecimal amount, BigDecimal fee) {
        log.info("Withdraw request: address={}, amount={}, fee={}", address, amount, fee);
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return MessageResult.error(500, "The amount must be greater than 0");
        }
        try {
            String txId = BitcoinRpcUtil.sendTransaction(rpcClient, address, amount, fee);
            MessageResult result = new MessageResult(0, "success");
            result.setData(txId);
            return result;
        } catch (BitcoinRpcException e) {
            log.error("Error sending transaction: {}", e.getMessage(), e);
            return MessageResult.error(500, "Error: " + e.getMessage());
        }
    }

    @GetMapping("balance/{address}")
    public MessageResult balance(@PathVariable String address) {
        try {
            BitcoinRpcClient walletClient = new BitcoinRpcClient(uri + "wallet/" + address);
            BigDecimal balance = walletClient.getBalance();
            MessageResult result = new MessageResult(0, "success");
            result.setData(balance);
            return result;
        } catch (MalformedURLException e) {
            log.error("Invalid wallet URL: {}", e.getMessage(), e);
            return MessageResult.error(500, "Invalid wallet URL: " + e.getMessage());
        } catch (BitcoinRpcException e) {
            log.error("Error getting balance: {}", e.getMessage(), e);
            return MessageResult.error(500, "Error: " + e.getMessage());
        }
    }

    /**
     * Creates a new wallet
     *
     * @param walletName the name of the wallet to create
     * @return a MessageResult containing information about the created wallet
     */
    @GetMapping("create-wallet/{walletName}")
    public MessageResult createWallet(@PathVariable String walletName) {
        log.info("Creating new wallet: {}", walletName);
        try {
            Map<String, Object> result = rpcClient.createWallet(walletName);
            MessageResult response = new MessageResult(0, "success");
            response.setData(result);
            return response;
        } catch (BitcoinRpcException e) {
            log.error("Error creating wallet: {}", e.getMessage(), e);
            return MessageResult.error(500, "Error: " + e.getMessage());
        }
    }

    /**
     * Creates a new wallet with advanced options
     *
     * @param walletName the name of the wallet to create
     * @param disablePrivateKeys if true, the wallet will be created without private keys
     * @param blank if true, the wallet will be created without an HD seed
     * @param passphrase the passphrase to encrypt the wallet with (null for no encryption)
     * @param avoidReuse if true, the wallet will avoid reusing addresses
     * @return a MessageResult containing information about the created wallet
     */
    @GetMapping("create-wallet-advanced")
    public MessageResult createWalletAdvanced(
            String walletName,
            boolean disablePrivateKeys,
            boolean blank,
            String passphrase,
            boolean avoidReuse) {
        log.info("Creating new wallet with advanced options: {}", walletName);
        try {
            Map<String, Object> result = rpcClient.createWallet(
                    walletName, disablePrivateKeys, blank, passphrase, avoidReuse);
            MessageResult response = new MessageResult(0, "success");
            response.setData(result);
            return response;
        } catch (BitcoinRpcException e) {
            log.error("Error creating wallet: {}", e.getMessage(), e);
            return MessageResult.error(500, "Error: " + e.getMessage());
        }
    }
}
