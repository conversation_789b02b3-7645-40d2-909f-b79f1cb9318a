package com.icetea.lotus.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.KafkaTemplate;

import com.icetea.lotus.Trader.CoinTrader;
import com.icetea.lotus.Trader.CoinTraderFactory;
import com.icetea.lotus.entity.ExchangeCoin;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.service.ExchangeOrderService;

import java.util.List;

@Slf4j
@Configuration
public class CoinTraderConfig {

    /**
     * Configure transaction processing classes
     * @param exchangeCoinService
     * @param kafkaTemplate
     * @return
     */
    @Bean
    public CoinTraderFactory getCoinTrader(ExchangeCoinService exchangeCoinService, KafkaTemplate<String,String> kafkaTemplate, ExchangeOrderService exchangeOrderService){
        CoinTraderFactory factory = new CoinTraderFactory();
        List<ExchangeCoin> coins = exchangeCoinService.findAllEnabled();
        for(ExchangeCoin coin:coins) {
            log.info("======CoinTrader Init Processing: {}======", coin.getSymbol());
            CoinTrader trader = new CoinTrader(coin.getSymbol());
            trader.setKafkaTemplate(kafkaTemplate);
            trader.setBaseCoinScale(coin.getBaseCoinScale());
            trader.setCoinScale(coin.getCoinScale());
            trader.setPublishType(coin.getPublishType());
            trader.setClearTime(coin.getClearTime());
            trader.stopTrading();
            factory.addTrader(coin.getSymbol(),trader);
            log.info("======CoinTrader Init Completed for: {}======", coin.getSymbol());
        }
        return factory;
    }

}
