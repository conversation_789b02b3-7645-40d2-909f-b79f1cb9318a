#Ti\u1EBFng Anh
#\u0110\u0103ng nh\u1EADp v\u00E0 \u0111\u0103ng k\u00FD
SEND_LOGIN_EMAIL_SUCCESS=Email k\u00EDch ho\u1EA1t \u0111\u0103ng k\u00FD \u0111\u00E3 \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn email c\u1EE7a b\u1EA1n, vui l\u00F2ng k\u00EDch ho\u1EA1t \u0111\u0103ng k\u00FD trong v\u00F2ng 12 gi\u1EDD!
EMAIL_ALREADY_BOUND=Email n\u00E0y \u0111\u00E3 \u0111\u01B0\u1EE3c li\u00EAn k\u1EBFt
USERNAME_ALREADY_EXISTS=T\u00EAn ng\u01B0\u1EDDi d\u00F9ng \u0111\u00E3 t\u1ED3n t\u1EA1i
LOGIN_EMAIL_ALREADY_SEND=Email k\u00EDch ho\u1EA1t \u0111\u0103ng k\u00FD \u0111\u00E3 \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn email c\u1EE7a b\u1EA1n, vui l\u00F2ng kh\u00F4ng y\u00EAu c\u1EA7u l\u1EA1i!

REGISTRATION_FAILED=\u0111\u0103ng k\u00FD kh\u00F4ng th\u00E0nh c\u00F4ng
PHONE_ALREADY_EXISTS=S\u1ED1 \u0111i\u1EC7n tho\u1EA1i \u0111\u00E3 t\u1ED3n t\u1EA1i
VERIFICATION_CODE_NOT_EXISTS=Vui l\u00F2ng l\u1EA5y m\u00E3 x\u00E1c minh tr\u01B0\u1EDBc
VERIFICATION_CODE_INCORRECT=M\u00E3 x\u00E1c minh kh\u00F4ng \u0111\u00FAng
USER_PASSWORD_ERROR=T\u00EAn ng\u01B0\u1EDDi d\u00F9ng ho\u1EB7c m\u1EADt kh\u1EA9u kh\u00F4ng \u0111\u00FAng
REGISTRATION_SUCCESS=\u0111\u0103ng k\u00FD th\u00E0nh c\u00F4ng
WRONG_EMAIL=\u0110\u1ECBnh d\u1EA1ng email kh\u00F4ng \u0111\u00FAng
SEND_EMAIL_LOGIN_SUBJECT=\u0110\u0103ng k\u00FD th\u00E0nh vi\u00EAn th\u00E0nh c\u00F4ng\uFF08BIZZAN\uFF09
INVALID_LINK=Li\u00EAn k\u1EBFt kh\u00F4ng h\u1EE3p l\u1EC7
ACTIVATION_SUCCESSFUL=K\u00EDch ho\u1EA1t th\u00E0nh c\u00F4ng
ACTIVATION_FAILS=K\u00EDch ho\u1EA1t kh\u00F4ng th\u00E0nh c\u00F4ng
ACTIVATION_FAILS_EMAIL=K\u00EDch ho\u1EA1t kh\u00F4ng th\u00E0nh c\u00F4ng v\u00EC h\u1ED9p th\u01B0 \u0111\u00E3 t\u1ED3n t\u1EA1i
ACCOUNT_ACTIVATION_DISABLED=T\u00E0i kho\u1EA3n \u1EDF tr\u1EA1ng th\u00E1i kh\u00F4ng ho\u1EA1t \u0111\u1ED9ng/b\u1ECB v\u00F4 hi\u1EC7u h\u00F3a, vui l\u00F2ng li\u00EAn h\u1EC7 v\u1EDBi kh\u00E1ch h\u00E0ng d\u1ECBch v\u1EE5
ACTIVATION_FAILS_USERNAME=K\u00EDch ho\u1EA1t kh\u00F4ng th\u00E0nh c\u00F4ng v\u00EC t\u00EAn ng\u01B0\u1EDDi d\u00F9ng \u0111\u00E3 t\u1ED3n t\u1EA1i
MISSING_USERNAME=Vui l\u00F2ng nh\u1EADp s\u1ED1 \u0111i\u1EC7n tho\u1EA1i ho\u1EB7c email
MISSING_PASSWORD=Vui l\u00F2ng nh\u1EADp m\u1EADt kh\u1EA9u
LOGOUT_FAILED=\u0110\u0103ng xu\u1EA5t kh\u00F4ng th\u00E0nh c\u00F4ng
LOGOUT_SUCCESS=\u0110\u0103ng xu\u1EA5t th\u00E0nh c\u00F4ng
RE_LOGIN=vui l\u00F2ng \u0111\u0103ng nh\u1EADp l\u1EA1i
GEETEST_FAIL=x\u00E1c minh kh\u00F4ng th\u00E0nh c\u00F4ng
FREQUENTLY_REQUEST=Y\u00EAu c\u1EA7u qu\u00E1 th\u01B0\u1EDDng xuy\u00EAn, vui l\u00F2ng th\u1EED l\u1EA1i sau
USER_PROMOTION_CODE_EXISTS=M\u00E3 khuy\u1EBFn m\u1EA1i kh\u00F4ng t\u1ED3n t\u1EA1i
VERIFICATION_PICTURE_NOT_CORRECT=M\u00E3 x\u00E1c minh \u0111\u1ED3 h\u1ECDa kh\u00F4ng ch\u00EDnh x\u00E1c
# Trung t\u00E2m c\u00E1 nh\u00E2n
BIND_EMAIL_REPEAT=Kh\u00F4ng li\u00EAn k\u1EBFt h\u1ED9p th\u01B0 nhi\u1EC1u l\u1EA7n
EMAIL_ALREADY_SEND=M\u00E3 x\u00E1c minh \u0111\u00E3 \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn email c\u1EE7a b\u1EA1n, vui l\u00F2ng kh\u00F4ng l\u1EB7p l\u1EA1i y\u00EAu c\u1EA7u
SEND_FAILED=Kh\u00F4ng g\u1EEDi \u0111\u01B0\u1EE3c
SENT_SUCCESS_TEN=\u0110\u00E3 g\u1EEDi th\u00E0nh c\u00F4ng, m\u00E3 x\u00E1c minh h\u1EBFt h\u1EA1n sau m\u1ED9t ph\u00FAt
NOT_BIND_EMAIL=Vui l\u00F2ng li\u00EAn k\u1EBFt h\u1ED9p th\u01B0 tr\u01B0\u1EDBc
NOT_BIND_PHONE=Vui l\u00F2ng li\u00EAn k\u1EBFt \u0111i\u1EC7n tho\u1EA1i tr\u01B0\u1EDBc
PHONE_EMPTY_OR_INCORRECT=S\u1ED1 \u0111i\u1EC7n tho\u1EA1i tr\u1ED1ng ho\u1EB7c \u0111\u1ECBnh d\u1EA1ng kh\u00F4ng \u0111\u00FAng
SEND_SMS_FAILED=G\u1EEDi SMS kh\u00F4ng th\u00E0nh c\u00F4ng
SEND_SMS_SUCCESS=G\u1EEDi SMS th\u00E0nh c\u00F4ng
SEND_EMAIL_CODE_SUBJECT=M\u00E3 x\u00E1c minh\uFF08BIZZAN\uFF09
MUST_PUT_OFF_ALL_ADVERTISE=Vui l\u00F2ng x\u00F3a t\u1EA5t c\u1EA3 qu\u1EA3ng c\u00E1o tr\u01B0\u1EDBc
HAVE_ORDER_ING=C\u00F3 \u0111\u01A1n h\u00E0ng \u0111ang x\u1EED l\u00FD v\u00E0 kh\u00F4ng th\u1EC3 thay \u0111\u1ED5i
MISSING_JY_PASSWORD=Vui l\u00F2ng nh\u1EADp m\u1EADt kh\u1EA9u qu\u1EF9
JY_PASSWORD_LENGTH_ILLEGAL=\u0110\u1ED9 d\u00E0i c\u1EE7a m\u1EADt kh\u1EA9u qu\u1EF9 kh\u00F4ng h\u1EE3p l\u1EC7
PASSWORD_LENGTH_ILLEGAL=\u0110\u1ED9 d\u00E0i m\u1EADt kh\u1EA9u kh\u00F4ng h\u1EE3p l\u1EC7
REPEAT_SETTING=Kh\u00F4ng l\u1EB7p l\u1EA1i c\u00E0i \u0111\u1EB7t
SETTING_JY_PASSWORD=\u0110\u1EB7t th\u00E0nh c\u00F4ng
MISSING_OLD_JY_PASSWORD=Vui l\u00F2ng nh\u1EADp m\u1EADt kh\u1EA9u qu\u1EF9 c\u0169
MISSING_OLD_PASSWORD=Vui l\u00F2ng nh\u1EADp m\u1EADt kh\u1EA9u \u0111\u0103ng nh\u1EADp c\u0169
MISSING_NEW_PASSWORD=Vui l\u00F2ng nh\u1EADp m\u1EADt kh\u1EA9u \u0111\u0103ng nh\u1EADp m\u1EDBi
MISSING_NEW_JY_PASSWORD=Vui l\u00F2ng nh\u1EADp m\u1EADt kh\u1EA9u qu\u1EF9 m\u1EDBi
NO_GET_VERIFICATION_CODE=Vui l\u00F2ng l\u1EA5y m\u00E3 x\u00E1c minh tr\u01B0\u1EDBc
SETTING_SUCCESS=Thi\u1EBFt l\u1EADp th\u00E0nh c\u00F4ng
REAL_APPLY_SUCCESS=\u1EE8ng d\u1EE5ng x\u00E1c th\u1EF1c t\u00EAn th\u1EADt \u0111\u00E3 th\u00E0nh c\u00F4ng, vui l\u00F2ng \u0111\u1EE3i xem x\u00E9t
NO_REAL_NAME=Vui l\u00F2ng ho\u00E0n t\u1EA5t x\u00E1c th\u1EF1c t\u00EAn th\u1EADt tr\u01B0\u1EDBc
NO_JY_PASSWORD=Vui l\u00F2ng \u0111\u1EB7t m\u1EADt kh\u1EA9u qu\u1EF9 tr\u01B0\u1EDBc
MISSING_REAL_NAME=Vui l\u00F2ng nh\u1EADp t\u00EAn th\u1EADt c\u1EE7a b\u1EA1n
MISSING_ID_CARD=Vui l\u00F2ng nh\u1EADp s\u1ED1 ID
MISSING_ID_CARD_FRONT=Vui l\u00F2ng t\u1EA3i \u1EA3nh m\u1EB7t tr\u01B0\u1EDBc c\u1EE7a CMND l\u00EAn
MISSING_ID_CARD_BACK=Vui l\u00F2ng t\u1EA3i \u1EA3nh m\u1EB7t sau c\u1EE7a CMND l\u00EAn th\u1EBB
MISSING_ID_CARD_HAND=Vui l\u00F2ng t\u1EA3i l\u00EAn \u1EA3nh ID c\u1EA7m tay
REAL_NAME_ILLEGAL=T\u00EAn kh\u00F4ng h\u1EE3p l\u1EC7
ID_CARD_ILLEGAL=S\u1ED1 ID kh\u00F4ng h\u1EE3p l\u1EC7
REPEAT_REAL_NAME_REQUEST=X\u00E1c th\u1EF1c t\u00EAn th\u1EADt \u0111\u00E3 \u0111\u01B0\u1EE3c th\u1EF1c hi\u1EC7n v\u00E0 kh\u00F4ng th\u1EC3 x\u00E1c th\u1EF1c l\u1EA1i
REPEAT_PHONE_REQUEST=S\u1ED1 \u0111i\u1EC7n tho\u1EA1i di \u0111\u1ED9ng \u0111\u00E3 \u0111\u01B0\u1EE3c li\u00EAn k\u1EBFt
PHONE_ALREADY_BOUND=S\u1ED1 \u0111i\u1EC7n tho\u1EA1i \u0111\u00E3 \u0111\u01B0\u1EE3c li\u00EAn k\u1EBFt
REPEAT_EMAIL_REQUEST=Email \u0111\u00E3 \u0111\u01B0\u1EE3c li\u00EAn k\u1EBFt
MISSING_LOGIN_PASSWORD=Vui l\u00F2ng nh\u1EADp m\u1EADt kh\u1EA9u \u0111\u0103ng nh\u1EADp
MISSING_PHONE=Vui l\u00F2ng nh\u1EADp s\u1ED1 \u0111i\u1EC7n tho\u1EA1i c\u1EE7a b\u1EA1n
PHONE_FORMAT_ERROR=L\u1ED7i \u0111\u1ECBnh d\u1EA1ng s\u1ED1 \u0111i\u1EC7n tho\u1EA1i di \u0111\u1ED9ng
EMAIL_FORMAT_ERROR=L\u1ED7i \u0111\u1ECBnh d\u1EA1ng email
PASSWORD_ERROR=L\u1ED7i m\u1EADt kh\u1EA9u
MISSING_EMAIL=Vui l\u00F2ng nh\u1EADp email
REPEAT_APPLICATION=Vui l\u00F2ng kh\u00F4ng \u0111\u0103ng k\u00FD l\u1EA1i
#Chuy\u1EC3n kho\u1EA3n
FEE_ERROR=S\u1ED1 ti\u1EC1n ph\u00ED kh\u00F4ng \u0111\u00FAng

#Withdrawal
MISSING_COIN_ADDRESS=Vui l\u00F2ng nh\u1EADp \u0111\u1ECBa ch\u1EC9 \u0111\u1EC3 r\u00FAt ti\u1EC1n
MISSING_COIN_TYPE=Kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng ti\u1EC1n t\u1EC7
MISSING_VERIFICATION_CODE=Kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng m\u00E3 x\u00E1c minh
MISSING_PHONE_OR_EMAIL=Kh\u00F4ng \u0111\u01B0\u1EE3c \u0111\u1EC3 tr\u1ED1ng s\u1ED1 \u0111i\u1EC7n tho\u1EA1i di \u0111\u1ED9ng ho\u1EB7c email
ADD_ADDRESS_FAILED=Kh\u00F4ng th\u00EAm \u0111\u01B0\u1EE3c
ADD_ADDRESS_SUCCESS=\u0110\u00E3 th\u00EAm th\u00E0nh c\u00F4ng
DELETE_ADDRESS_SUCCESS=X\u00F3a th\u00E0nh c\u00F4ng
DELETE_ADDRESS_FAILED=X\u00F3a kh\u00F4ng th\u00E0nh c\u00F4ng
COIN_NOT_SUPPORT=Ti\u1EC1n t\u1EC7 kh\u00F4ng h\u1ED7 tr\u1EE3 r\u00FAt ti\u1EC1n
COIN_ILLEGAL=Ti\u1EC1n t\u1EC7 b\u1EA5t h\u1EE3p ph\u00E1p
CHARGE_MIN=Ph\u00ED x\u1EED l\u00FD t\u1ED1i thi\u1EC3u ph\u1EA3i l\u1EDBn h\u01A1n ho\u1EB7c b\u1EB1ng
CHARGE_MAX=Ph\u00ED x\u1EED l\u00FD t\u1ED1i \u0111a ph\u1EA3i nh\u1ECF h\u01A1n ho\u1EB7c b\u1EB1ng
WITHDRAW_MIN=S\u1ED1 ti\u1EC1n r\u00FAt t\u1ED1i thi\u1EC3u ph\u1EA3i l\u1EDBn h\u01A1n ho\u1EB7c b\u1EB1ng
WITHDRAW_MAX=S\u1ED1 ti\u1EC1n r\u00FAt t\u1ED1i \u0111a ph\u1EA3i nh\u1ECF h\u01A1n ho\u1EB7c b\u1EB1ng
INSUFFICIENT_BALANCE=Kh\u00F4ng \u0111\u1EE7 s\u1ED1 d\u01B0 kh\u1EA3 d\u1EE5ng
WRONG_ADDRESS=\u0110\u1ECBa ch\u1EC9 kh\u00F4ng \u0111\u00FAng
APPLY_SUCCESS=\u0110\u01A1n \u0111\u0103ng k\u00FD th\u00E0nh c\u00F4ng v\u00E0 \u0111ang trong qu\u00E1 tr\u00ECnh r\u00FAt ti\u1EC1n
APPLY_AUDIT=\u0110\u01A1n \u0111\u0103ng k\u00FD r\u00FAt ti\u1EC1n th\u00E0nh c\u00F4ng, vui l\u00F2ng ch\u1EDD xem x\u00E9t
#advertising
MISSING_PAY=Vui l\u00F2ng nh\u1EADp ph\u01B0\u01A1ng th\u1EE9c thanh to\u00E1n
MISSING_JYPASSWORD=Vui l\u00F2ng nh\u1EADp m\u1EADt kh\u1EA9u qu\u1EF9
NO_REALNAME=Vui l\u00F2ng x\u00E1c minh t\u00EAn th\u1EADt c\u1EE7a b\u1EA1n tr\u01B0\u1EDBc
NO_ALI=Vui l\u00F2ng thi\u1EBFt l\u1EADp Alipay tr\u01B0\u1EDBc
NO_BANK=Vui l\u00F2ng thi\u1EBFt l\u1EADp th\u1EBB ng\u00E2n h\u00E0ng tr\u01B0\u1EDBc
NO_WECHAT=Vui l\u00F2ng thi\u1EBFt l\u1EADp WeChat tr\u01B0\u1EDBc
NO_SET_JYPASSWORD=Vui l\u00F2ng \u0111\u1EB7t m\u1EADt kh\u1EA9u qu\u1EF9 tr\u01B0\u1EDBc
ERROR_JYPASSWORD=M\u1EADt kh\u1EA9u qu\u1EF9 kh\u00F4ng \u0111\u00FAng
SELL_NUMBER_MIN=S\u1ED1 l\u01B0\u1EE3ng b\u00E1n ra ph\u1EA3i l\u1EDBn h\u01A1n
BUY_NUMBER_MIN=S\u1ED1 l\u01B0\u1EE3ng mua ph\u1EA3i l\u1EDBn h\u01A1n
CREATE_SUCCESS=\u0110\u00E3 t\u1EA1o th\u00E0nh c\u00F4ng
CREATE_FAILED=T\u1EA1o kh\u00F4ng th\u00E0nh c\u00F4ng
UPDATE_FAILED=Kh\u00F4ng th\u1EC3 s\u1EEDa \u0111\u1ED5i
UPDATE_SUCCESS=\u0110\u00E3 s\u1EEDa \u0111\u1ED5i th\u00E0nh c\u00F4ng
AFTER_OFF_SHELVES=Vui l\u00F2ng x\u00F3a qu\u1EA3ng c\u00E1o tr\u01B0\u1EDBc khi ch\u1EC9nh s\u1EEDa
PUT_OFF_SHELVES_FAILED=Kh\u00F4ng th\u1EC3 x\u00F3a k\u1EC7
PUT_OFF_SHELVES_SUCCESS=\u0110\u00E3 x\u00F3a th\u00E0nh c\u00F4ng
PUT_ON_SHELVES_SUCCESS=\u0110\u00E3 kh\u1EDFi ch\u1EA1y th\u00E0nh c\u00F4ng
PUT_ON_SHELVES_FAILED=K\u1EC7 kh\u00F4ng th\u00E0nh c\u00F4ng
DELETE_ADVERTISE_SUCCESS=X\u00F3a th\u00E0nh c\u00F4ng
DELETE_ADVERTISE_FAILED=X\u00F3a kh\u00F4ng th\u00E0nh c\u00F4ng
DELETE_AFTER_OFF_SHELVES=C\u00F3 th\u1EC3 x\u00F3a qu\u1EA3ng c\u00E1o sau khi \u0111\u00E3 x\u00F3a
MEMBER_NOT_EXISTS=Kh\u00F4ng c\u00F3 ng\u01B0\u1EDDi d\u00F9ng n\u00E0o nh\u01B0 v\u1EADy
#Order
PARAMETER_ERROR=L\u1ED7i tham s\u1ED1
ALREADY_PUT_OFF=Qu\u1EA3ng c\u00E1o n\u00E0y \u0111\u00E3 b\u1ECB x\u00F3a
NOT_ALLOW_BUY_BY_SELF=Kh\u00F4ng th\u1EC3 mua qua qu\u1EA3ng c\u00E1o c\u1EE7a ri\u00EAng b\u1EA1n
NOT_ALLOW_SELL_BY_SELF=Kh\u00F4ng th\u1EC3 b\u00E1n qua qu\u1EA3ng c\u00E1o c\u1EE7a ri\u00EAng b\u1EA1n
CREATE_ORDER_SUCCESS=T\u1EA1o \u0111\u01A1n h\u00E0ng th\u00E0nh c\u00F4ng
PRICE_EXPIRED=Gi\u00E1 \u0111\u00E3 h\u1EBFt h\u1EA1n, vui l\u00F2ng l\u00E0m m\u1EDBi v\u00E0 th\u1EED l\u1EA1i
NUMBER_ERROR=L\u1ED7i s\u1ED1
MONEY_MIN=S\u1ED1 ti\u1EC1n kh\u00F4ng \u0111\u01B0\u1EE3c nh\u1ECF h\u01A1n
MONEY_MAX=S\u1ED1 ti\u1EC1n kh\u00F4ng \u0111\u01B0\u1EE3c cao h\u01A1n
AMOUNT_NOT_ENOUGH=S\u1ED1 l\u01B0\u1EE3ng c\u00F2n l\u1EA1i kh\u00F4ng \u0111\u1EE7, vui l\u00F2ng l\u00E0m m\u1EDBi v\u00E0 th\u1EED l\u1EA1i
AT_LEAST_SUPPORT_PAY=Vui l\u00F2ng h\u1ED7 tr\u1EE3 \u00EDt nh\u1EA5t m\u1ED9t ph\u01B0\u01A1ng th\u1EE9c thanh to\u00E1n trong qu\u1EA3ng c\u00E1o
ORDER_NOT_EXISTS=\u0110\u01A1n h\u00E0ng kh\u00F4ng t\u1ED3n t\u1EA1i
REQUEST_ILLEGAL=Y\u00EAu c\u1EA7u b\u1EA5t h\u1EE3p ph\u00E1p
ORDER_NOT_ALLOW_CANCEL=Kh\u00F4ng th\u1EC3 h\u1EE7y \u0111\u01A1n h\u00E0ng n\u00E0y
CANCEL_SUCCESS=\u0110\u00E3 h\u1EE7y th\u00E0nh c\u00F4ng
ORDER_STATUS_EXPIRED=Tr\u1EA1ng th\u00E1i \u0111\u01A1n h\u00E0ng \u0111\u00E3 thay \u0111\u1ED5i
ORDER_ALREADY_AUTO_CANCEL=\u0110\u01A1n h\u00E0ng n\u00E0y \u0111\u00E3 t\u1EF1 \u0111\u1ED9ng \u0111\u01B0\u1EE3c h\u1EE7y \u0111\u00E3 h\u1EE7y
PAY_SUCCESS=Thanh to\u00E1n th\u00E0nh c\u00F4ng
RELEASE_SUCCESS=Ph\u00E1t h\u00E0nh th\u00E0nh c\u00F4ng
NO_APPEAL=Kh\u00F4ng th\u1EC3 kh\u00E1ng c\u00E1o \u0111\u01A1n h\u00E0ng
APPEAL_SUCCESS=Kh\u00E1ng c\u00E1o th\u00E0nh c\u00F4ng
# T\u1EA3i l\u00EAn
FORM_FORMAT_ERROR=L\u1ED7i \u0111\u1ECBnh d\u1EA1ng bi\u1EC3u m\u1EABu
NOT_FIND_FILE=Kh\u00F4ng t\u00ECm th\u1EA5y d\u1EEF li\u1EC7u t\u1EA3i l\u00EAn
UPLOAD_SUCCESS=\u0110\u00E3 t\u1EA3i l\u00EAn th\u00E0nh c\u00F4ng
SYSTEM_ERROR=L\u1ED7i h\u1EC7 th\u1ED1ng
FORMAT_NOT_SUPPORT=Kh\u00F4ng h\u1ED7 tr\u1EE3 \u0111\u1ECBnh d\u1EA1ng n\u00E0y
DATA_ILLEGAL=D\u1EEF li\u1EC7u kh\u00F4ng h\u1EE3p l\u1EC7
FAILED_TO_WRITE=Kh\u00F4ng ghi \u0111\u01B0\u1EE3c t\u1EC7p

REGISTER_TO_PC=Vui l\u00F2ng \u0111\u0103ng k\u00FD tr\u00EAn trang web PC

WITHDRAW_TO_PC=Vui l\u00F2ng r\u00FAt ti\u1EC1n xu tr\u00EAn PC ho\u1EB7c n\u00E2ng c\u1EA5p APP

#\u56FD\u5BB6\u591A\u8BED\u8A00
COUNTRY_TRANSLATIONS_Taiwan=\u0110\u00E0i Loan
COUNTRY_TRANSLATIONS_Singapore=Singapore
COUNTRY_TRANSLATIONS_H\u00E0n Qu\u1ED1c=H\u00E0n Qu\u1ED1c
COUNTRY_TRANSLATIONS_Nh\u1EADt B\u1EA3n=Nh\u1EADt B\u1EA3n
COUNTRY_TRANSLATIONS_Th\u00E1i Lan=Th\u00E1i Lan
COUNTRY_TRANSLATIONS_Nga=Nga
COUNTRY_TRANSLATIONS_Anh=Anh
COUNTRY_TRANSLATIONS_Vi\u1EC7t Nam am=vietnam
COUNTRY_TRANSLATIONS_India=india
COUNTRY_TRANSLATIONS_Italy=italy
COUNTRY_TRANSLATIONS_HK=hk
COUNTRY_TRANSLATIONS_Malaysia=malaysia
COUNTRY_TRANSLATIONS_Turkey=turkey
COUNTRY_TRANSLATIONS_Germany=germany
COUNTRY_TRANSLATIONS_France=france
COUNTRY_TRANSLATIONS_Spain=spain

absolutBank_name= name
absolutBank_name_placeholder= Vui l\u00F2ng nh\u1EADp h\u1ECD t\u00EAn \u0111\u1EA7y \u0111\u1EE7 c\u1EE7a b\u1EA1n
absolutBank_no= s\u1ED1 t\u00E0i kho\u1EA3n
absolutBank_no_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 t\u00E0i kho\u1EA3n ng\u00E2n h\u00E0ng c\u1EE7a b\u1EA1n
absolutBank_detail= Chi ti\u1EBFt thanh to\u00E1n (t\u00F9y ch\u1ECDn)
absolutBank_detail_placeholder= Vui l\u00F2ng nh\u1EADp chi ti\u1EBFt thanh to\u00E1n c\u1EE7a b\u1EA1n
absolutBank_tip= Nh\u1EAFc nh\u1EDF \u0111\u1EB7c bi\u1EC7t
absolutBank_tip_placeholder= \u0110\u1EA3m b\u1EA3o th\u00EAm s\u1ED1 th\u1EBB ng\u00E2n h\u00E0ng c\u1EE7a b\u1EA1n \u0111\u1EC3 thanh to\u00E1n ngay l\u1EADp t\u1EE9c. Kh\u00F4ng bao g\u1ED3m chi ti\u1EBFt v\u1EC1 c\u00E1c ng\u00E2n h\u00E0ng ho\u1EB7c ph\u01B0\u01A1ng th\u1EE9c thanh to\u00E1n kh\u00E1c. B\u1EA1n ph\u1EA3i th\u00EAm th\u00F4ng tin thanh to\u00E1n/bi\u00EAn lai cho ng\u00E2n h\u00E0ng \u0111\u00E3 ch\u1ECDn.

bankTransfer_name= t\u00EAn (t\u00F9y ch\u1ECDn)
bankTransfer_name_placeholder= Vui l\u00F2ng nh\u1EADp h\u1ECD t\u00EAn \u0111\u1EA7y \u0111\u1EE7 c\u1EE7a b\u1EA1n
bankTransfer_id=S\u1ED1 ID (t\u00F9y ch\u1ECDn)
bankTransfer_id_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 ID c\u1EE7a b\u1EA1n
bankTransfer_iban=S\u1ED1 IBAN
bankTransfer_iban_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 IBAN c\u1EE7a b\u1EA1n

BACCostaRica_name= T\u00EAn (t\u00F9y ch\u1ECDn)
BACCostaRica_name_placeholder= Vui l\u00F2ng nh\u1EADp h\u1ECD t\u00EAn \u0111\u1EA7y \u0111\u1EE7 c\u1EE7a b\u1EA1n
BACCostaRica_id=S\u1ED1 ID (t\u00F9y ch\u1ECDn)
BACCostaRica_id_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 ID c\u1EE7a b\u1EA1n
BACCostaRica_iban_or_BAC=S\u1ED1 t\u00E0i kho\u1EA3n IBAN ho\u1EB7c BAC
BACCostaRica_iban_or_BAC_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 t\u00E0i kho\u1EA3n ng\u00E2n h\u00E0ng IBAN ho\u1EB7c BAC c\u1EE7a b\u1EA1n

zelle_fullName= h\u1ECD t\u00EAn \u0111\u1EA7y \u0111\u1EE7
zelle_fullName_placeholder= Vui l\u00F2ng nh\u1EADp h\u1ECD t\u00EAn \u0111\u1EA7y \u0111\u1EE7 c\u1EE7a b\u1EA1n
zelle_email= E-mail
zelle_email_placeholder= Vui l\u00F2ng nh\u1EADp \u0111\u1ECBa ch\u1EC9 email c\u1EE7a b\u1EA1n

zinli_email= Email (t\u00F9y ch\u1ECDn)
zinli_email_placeholder= Vui l\u00F2ng nh\u1EADp \u0111\u1ECBa ch\u1EC9 email c\u1EE7a b\u1EA1n
zinli_name= t\u00EAn
zinli_name_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn c\u1EE7a b\u1EA1n

uphold_email_or_username= email ho\u1EB7c t\u00EAn ng\u01B0\u1EDDi d\u00F9ng
uphold_email_or_username_placeholder= Vui l\u00F2ng nh\u1EADp email ho\u1EB7c t\u00EAn ng\u01B0\u1EDDi d\u00F9ng c\u1EE7a b\u1EA1n
uphold_fullName= t\u00EAn \u0111\u1EA7y \u0111\u1EE7 (t\u00F9y ch\u1ECDn)
uphold_fullName_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn \u0111\u1EA7y \u0111\u1EE7 c\u1EE7a b\u1EA1n

perfectMoney_name= t\u00EAn
perfectMoney_name_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn c\u1EE7a b\u1EA1n
perfectMoney_accountName= t\u00EAn t\u00E0i kho\u1EA3n
perfectMoney_accountName_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn t\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n

creditBankOfPeru_fullName= t\u00EAn \u0111\u1EA7y \u0111\u1EE7
creditBankOfPeru_fullName_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn \u0111\u1EA7y \u0111\u1EE7 c\u1EE7a b\u1EA1n
creditBankOfPeru_account= s\u1ED1 t\u00E0i kho\u1EA3n
creditBankOfPeru_account_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 t\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n

mercantilBankPanama_fullName= t\u00EAn \u0111\u1EA7y \u0111\u1EE7
mercantilBankPanama_fullName_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn \u0111\u1EA7y \u0111\u1EE7 c\u1EE7a b\u1EA1n
mercantilBankPanama_identityNumber= S\u1ED1 ID
mercantilBankPanama_identityNumber_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 ID c\u1EE7a b\u1EA1n
mercantilBankPanama_account= S\u1ED1 t\u00E0i kho\u1EA3n
mercantilBankPanama_account_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 t\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n

transferenciaACH_accountType= Lo\u1EA1i t\u00E0i kho\u1EA3n
transferenciaACH_accountType_placeholder= Vui l\u00F2ng nh\u1EADp lo\u1EA1i t\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n
transferenciaACH_payeeType= Lo\u1EA1i ng\u01B0\u1EDDi nh\u1EADn thanh to\u00E1n
transferenciaACH_payeeType_placeholder= Vui l\u00F2ng nh\u1EADp lo\u1EA1i ng\u01B0\u1EDDi nh\u1EADn thanh to\u00E1n c\u1EE7a b\u1EA1n
transferenciaACH_accountNo= S\u1ED1 t\u00E0i kho\u1EA3n
transferenciaACH_accountNo_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 t\u00E0i kho\u1EA3n ng\u00E2n h\u00E0ng c\u1EE7a b\u1EA1n
transferenciaACH_accountOwner= Ch\u1EE7 t\u00E0i kho\u1EA3n
transferenciaACH_accountOwner_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn ch\u1EE7 t\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n
transferenciaACH_bandName= Ng\u00E2n h\u00E0ng name
transferenciaACH_bandName_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn ng\u00E2n h\u00E0ng c\u1EE7a b\u1EA1n

banistmoPanama_fullName= t\u00EAn \u0111\u1EA7y \u0111\u1EE7
banistmoPanama_fullName_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn \u0111\u1EA7y \u0111\u1EE7 c\u1EE7a b\u1EA1n
banistmoPanama_email= Email (t\u00F9y ch\u1ECDn)
banistmoPanama_email_placeholder= Vui l\u00F2ng nh\u1EADp email c\u1EE7a b\u1EA1n
banistmoPanama_bankName= t\u00EAn ng\u00E2n h\u00E0ng
banistmoPanama_bankName_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn ng\u00E2n h\u00E0ng c\u1EE7a b\u1EA1n
banistmoPanama_accountType= Lo\u1EA1i t\u00E0i kho\u1EA3n
banistmoPanama_accountType_placeholder= Vui l\u00F2ng nh\u1EADp lo\u1EA1i t\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n
banistmoPanama_account= s\u1ED1 t\u00E0i kho\u1EA3n
banistmoPanama_account_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 t\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n

banescoPanama_name= name
banescoPanama_name_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn c\u1EE7a b\u1EA1n
banescoPanama_account= s\u1ED1 t\u00E0i kho\u1EA3n ng\u00E2n h\u00E0ng
banescoPanama_account_placeholder= Vui l\u00F2ng nh\u1EADp t\u00E0i kho\u1EA3n ng\u00E2n h\u00E0ng c\u1EE7a b\u1EA1n s\u1ED1

BACCredomatic_fullName= t\u00EAn \u0111\u1EA7y \u0111\u1EE7
BACCredomatic_fullName_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn \u0111\u1EA7y \u0111\u1EE7 c\u1EE7a b\u1EA1n
BACCredomatic_accoun= s\u1ED1 t\u00E0i kho\u1EA3n
BACCredomatic_account_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 t\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n
BACCredomatic_email= Email (t\u00F9y ch\u1ECDn)
BACCredomatic_email_placeholder= Vui l\u00F2ng nh\u1EADp email c\u1EE7a b\u1EA1n

payeer_name= t\u00EAn
payeer_name_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn c\u1EE7a b\u1EA1n
payeer_account= T\u00E0i kho\u1EA3n Payeer
payeer_account_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 t\u00E0i kho\u1EA3n Payeer c\u1EE7a b\u1EA1n


bancoGeneralPanama_fullName= t\u00EAn \u0111\u1EA7y \u0111\u1EE7
bancoGeneralPanama_fullName_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn \u0111\u1EA7y \u0111\u1EE7 c\u1EE7a b\u1EA1n
bancoGeneralPanama_email= Email (t\u00F9y ch\u1ECDn)
bancoGeneralPanama_email_placeholder= Vui l\u00F2ng nh\u1EADp email c\u1EE7a b\u1EA1n
bancoGeneralPanama_bankName= t\u00EAn ng\u00E2n h\u00E0ng
bancoGeneralPanama_bankName_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn ng\u00E2n h\u00E0ng c\u1EE7a b\u1EA1n
bancoGeneralPanama_account= s\u1ED1 t\u00E0i kho\u1EA3n
bancoGeneralPanama_account_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 t\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n
bancoGeneralPanama_remark= nh\u1EADn x\u00E9t (t\u00F9y ch\u1ECDn)
bancoGeneralPanama_remark_placeholder= Vui l\u00F2ng nh\u1EADp nh\u1EADn x\u00E9t c\u1EE7a b\u1EA1n

advcash_fullName= t\u00EAn \u0111\u1EA7y \u0111\u1EE7
advcash_fullName_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn \u0111\u1EA7y \u0111\u1EE7 c\u1EE7a b\u1EA1n
advcash_username= t\u00EAn ng\u01B0\u1EDDi d\u00F9ng (t\u00F9y ch\u1ECDn)
advcash_username_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn ng\u01B0\u1EDDi d\u00F9ng c\u1EE7a b\u1EA1n
advcash_email= h\u1ED9p th\u01B0
advcash_email_placeholder= Vui l\u00F2ng nh\u1EADp email
advcash_walletID= ID v\u00ED
advcash_walletID_placeholder= Vui l\u00F2ng nh\u1EADp ID Vallet c\u1EE7a b\u1EA1n

bankTransferVietnam_name= t\u00EAn
bankTransferVietnam_name_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn c\u1EE7a b\u1EA1n
bankTransferVietnam_bankName= t\u00EAn ng\u00E2n h\u00E0ng
bankTransferVietnam_bankName_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn ng\u00E2n h\u00E0ng c\u1EE7a b\u1EA1n
bankTransferVietnam_bankBranch= Chi nh\u00E1nh ng\u00E2n h\u00E0ng (t\u00F9y ch\u1ECDn)
bankTransferVietnam_bankBranch_placeholder= Vui l\u00F2ng nh\u1EADp chi nh\u00E1nh ng\u00E2n h\u00E0ng c\u1EE7a b\u1EA1n
bankTransferVietnam_bankNo= s\u1ED1 t\u00E0i kho\u1EA3n
bankTransferVietnam_bankNo_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 t\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n
bankTransferVietnam_referralPaymentNotes= Ghi ch\u00FA thanh to\u00E1n gi\u1EDBi thi\u1EC7u
bankTransferVietnam_referralPaymentNotes_placeholder= Vui l\u00F2ng nh\u1EADp ghi ch\u00FA thanh to\u00E1n gi\u1EDBi thi\u1EC7u c\u1EE7a b\u1EA1n
bankTransferVietnam_additionalInformation= Th\u00F4ng tin b\u1ED5 sung (t\u00F9y ch\u1ECDn)
bankTransferVietnam_additionalInformation_placeholder= Vui l\u00F2ng nh\u1EADp th\u00F4ng tin b\u1ED5 sung c\u1EE7a b\u1EA1n

bancoDeCredito_name= T\u00EAn (t\u00F9y ch\u1ECDn)
bancoDeCredito_name_placeholder= Vui l\u00F2ng nh\u1EADp h\u1ECD t\u00EAn \u0111\u1EA7y \u0111\u1EE7 c\u1EE7a b\u1EA1n
bancoDeCredito_IDNo=S\u1ED1 ID (t\u00F9y ch\u1ECDn)
bancoDeCredito_IDNo_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 ID c\u1EE7a b\u1EA1n
bancoDeCredito_accountType= Lo\u1EA1i t\u00E0i kho\u1EA3n
bancoDeCredito_accountType_placeholder= Vui l\u00F2ng nh\u1EADp lo\u1EA1i t\u00E0i kho\u1EA3n ng\u00E2n h\u00E0ng c\u1EE7a b\u1EA1n
bancoDeCredito_accountNo= s\u1ED1 t\u00E0i kho\u1EA3n
bancoDeCredito_accountNo_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 t\u00E0i kho\u1EA3n ng\u00E2n h\u00E0ng c\u1EE7a b\u1EA1n
bancoDeCredito_email= Email (t\u00F9y ch\u1ECDn)
bancoDeCredito_email_placeholder= Vui l\u00F2ng nh\u1EADp \u0111\u1ECBa ch\u1EC9 email c\u1EE7a b\u1EA1n
bancoDeCredito_address=\u0110\u1ECBa ch\u1EC9 (t\u00F9y ch\u1ECDn)
bancoDeCredito_address_placeholder= Vui l\u00F2ng nh\u1EADp \u0111\u1ECBa ch\u1EC9 \u0111\u1EA7y \u0111\u1EE7 c\u1EE7a b\u1EA1n

interbank_fullName= h\u1ECD t\u00EAn \u0111\u1EA7y \u0111\u1EE7
interbank_fullName_placeholder= Vui l\u00F2ng nh\u1EADp h\u1ECD t\u00EAn \u0111\u1EA7y \u0111\u1EE7 c\u1EE7a b\u1EA1n
interbank_IDNo= S\u1ED1 ID
interbank_IDNo_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 ID c\u1EE7a b\u1EA1n
interbank_account= s\u1ED1 t\u00E0i kho\u1EA3n
interbank_account_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 t\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n
interbank_accountType=
interbank_accountType_placeholder=Vui l\u00F2ng nh\u1EADp lo\u1EA1i t\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n

redPagos_name= t\u00EAn
redPagos_name_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn c\u1EE7a b\u1EA1n
redPagos_lastName= h\u1ECD
redPagos_lastName_placeholder= Vui l\u00F2ng nh\u1EADp h\u1ECD c\u1EE7a b\u1EA1n
redPagos_email= Email
redPagos_email_placeholder= Vui l\u00F2ng nh\u1EADp email c\u1EE7a b\u1EA1n
redPagos_phoneNo= s\u1ED1 \u0111i\u1EC7n tho\u1EA1i
redPagos_phoneNo_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 \u0111i\u1EC7n tho\u1EA1i c\u1EE7a b\u1EA1n

bandesUruguay_fullName= t\u00EAn \u0111\u1EA7y \u0111\u1EE7
bandesUruguay_fullName_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn \u0111\u1EA7y \u0111\u1EE7 c\u1EE7a b\u1EA1n
bandesUruguay_account= s\u1ED1 t\u00E0i kho\u1EA3n
bandesUruguay_account_placeholder= Vui l\u00F2ng nh\u1EADp s\u1ED1 t\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n
bandesUruguay_remark= nh\u1EADn x\u00E9t (t\u00F9y ch\u1ECDn)
bandesUruguay_remark_placeholder= Vui l\u00F2ng nh\u1EADp nh\u1EADn x\u00E9t c\u1EE7a b\u1EA1n

internationalWireTransfer_name= t\u00EAn
internationalWireTransfer_name_placeholder= Vui l\u00F2ng nh\u1EADp t\u00EAn c\u1EE7a b\u1EA1n
internationalWireTransfer_details= chi ti\u1EBFt
internationalWireTransfer_details_placeholder= Vui l\u00F2ng nh\u1EADp th\u00F4ng tin thanh to\u00E1n c\u1EE7a b\u1EA1n

idfcFirstBank_code=M\u00E3 IDFC
idfcFirstBank_code_placeholder=Vui l\u00F2ng nh\u1EADp M\u00E3 IDFC c\u1EE7a b\u1EA1n
idfcFirstBank_account_number=S\u1ED1 t\u00E0i kho\u1EA3n
idfcFirstBank_account_number_placeholder=Vui l\u00F2ng nh\u1EADp s\u1ED1 t\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n
idfcFirstBank_account_name=T\u00EAn t\u00E0i kho\u1EA3n
idfcFirstBank_account_name_placeholder=Vui l\u00F2ng nh\u1EADp t\u00EAn t\u00E0i kho\u1EA3n c\u1EE7a b\u1EA1n

REFRESH_TOKEN_NOT_NULL=Kh\u00F4ng \u0111\u1EC3 tr\u1ED1ng refresh token
