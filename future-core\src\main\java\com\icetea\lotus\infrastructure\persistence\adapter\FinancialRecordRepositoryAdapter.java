package com.icetea.lotus.infrastructure.persistence.adapter;

import com.icetea.lotus.core.domain.entity.FinancialRecord;
import com.icetea.lotus.core.domain.repository.FinancialRecordRepository;
import com.icetea.lotus.core.domain.valueobject.FinancialRecordType;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.PositionId;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.persistence.entity.FinancialRecordJpaEntity;
import com.icetea.lotus.infrastructure.persistence.repository.FinancialRecordJpaRepository;
import com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator;
import com.icetea.lotus.infrastructure.persistence.util.EmergencyBigDecimalValidator;
import com.icetea.lotus.infrastructure.util.SnowflakeIdGenerator;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

/**
 * Adapter cho FinancialRecordRepository
 */
@Slf4j
@Component
@Transactional
@RequiredArgsConstructor
public class FinancialRecordRepositoryAdapter implements FinancialRecordRepository {

    private final FinancialRecordJpaRepository financialRecordJpaRepository;
    private final SnowflakeIdGenerator snowflakeIdGenerator;

    /**
     * Lưu bản ghi tài chính với xử lý ngoại lệ và thử lại
     * @param financialRecord Bản ghi tài chính
     * @return FinancialRecord
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public FinancialRecord save(FinancialRecord financialRecord) {
        try {
            // Log giá trị trước khi validate
            log.info("FINANCIAL_RECORD_SAVE_DEBUG: Lưu bản ghi tài chính, memberId = {}, positionId = {}, type = {}, " +
                    "amount = {}, fee = {}, profit = {}",
                    financialRecord.getMemberId(),
                    financialRecord.getPositionId() != null ? financialRecord.getPositionId().getValue() : "null",
                    financialRecord.getType(),
                    financialRecord.getAmount() != null ? financialRecord.getAmount().getValue() : "null",
                    financialRecord.getFee() != null ? financialRecord.getFee().getValue() : "null",
                    financialRecord.getProfit() != null ? financialRecord.getProfit().getValue() : "null");

            FinancialRecordJpaEntity jpaEntity = mapToJpaEntity(financialRecord);

            // Log giá trị sau khi validate
            log.info("FINANCIAL_RECORD_SAVE_DEBUG: Sau khi validate, amount = {}, fee = {}, profit = {}",
                    jpaEntity.getAmount(), jpaEntity.getFee(), jpaEntity.getProfit());

            FinancialRecordJpaEntity savedEntity = financialRecordJpaRepository.save(jpaEntity);

            log.debug("Đã lưu bản ghi tài chính, id = {}", savedEntity.getId());

            return mapToDomainEntity(savedEntity);
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu bản ghi tài chính, memberId = {}, positionId = {}, type = {}",
                    financialRecord.getMemberId(),
                    financialRecord.getPositionId() != null ? financialRecord.getPositionId().getValue() : "null",
                    financialRecord.getType(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu bản ghi tài chính, memberId = {}, positionId = {}, type = {}",
                    financialRecord.getMemberId(),
                    financialRecord.getPositionId() != null ? financialRecord.getPositionId().getValue() : "null",
                    financialRecord.getType(), e);
            throw new DatabaseException("Lỗi khi lưu bản ghi tài chính: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lưu bản ghi tài chính thất bại
     * @param e Ngoại lệ
     * @param financialRecord Bản ghi tài chính
     * @return FinancialRecord
     */
    @Recover
    public FinancialRecord recoverSave(Exception e, FinancialRecord financialRecord) {
        log.error("Đã thử lại lưu bản ghi tài chính 3 lần nhưng thất bại, memberId = {}, positionId = {}, type = {}",
                financialRecord.getMemberId(),
                financialRecord.getPositionId() != null ? financialRecord.getPositionId().getValue() : "null",
                financialRecord.getType(), e);
        throw new DatabaseException("Không thể lưu bản ghi tài chính sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tìm tất cả bản ghi tài chính của một thành viên với xử lý ngoại lệ
     * @param memberId ID của thành viên
     * @return Danh sách bản ghi tài chính
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<FinancialRecord> findAllByMemberId(Long memberId) {
        try {
            log.debug("Tìm tất cả bản ghi tài chính của một thành viên, memberId = {}", memberId);

            if (ObjectUtils.isEmpty(memberId)) {
                return List.of();
            }

            List<FinancialRecordJpaEntity> entities = financialRecordJpaRepository.findAllByMemberId(memberId);
            return entities.stream()
                    .map(this::mapToDomainEntity)
                    .toList();
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả bản ghi tài chính của một thành viên, memberId = {}", memberId, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả bản ghi tài chính của một thành viên, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi tìm tất cả bản ghi tài chính của một thành viên: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm tất cả bản ghi tài chính của một thành viên thất bại
     * @param e Ngoại lệ
     * @param memberId ID của thành viên
     * @return Danh sách rỗng
     */
    @Recover
    public List<FinancialRecord> recoverFindAllByMemberId(Exception e, String memberId) {
        log.error("Đã thử lại tìm tất cả bản ghi tài chính của một thành viên 3 lần nhưng thất bại, memberId = {}", memberId, e);
        return List.of();
    }

    /**
     * Tìm tất cả bản ghi tài chính của một vị thế với xử lý ngoại lệ
     * @param positionId ID của vị thế
     * @return Danh sách bản ghi tài chính
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<FinancialRecord> findAllByPositionId(PositionId positionId) {
        try {
            log.debug("Tìm tất cả bản ghi tài chính của một vị thế, positionId = {}",
                    positionId != null ? positionId.getValue() : "null");

            if (positionId == null) {
                return List.of();
            }

            List<FinancialRecordJpaEntity> entities = financialRecordJpaRepository.findAllByPositionId(positionId.getValue());
            return entities.stream()
                    .map(this::mapToDomainEntity)
                    .toList();
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả bản ghi tài chính của một vị thế, positionId = {}",
                    positionId != null ? positionId.getValue() : "null", e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả bản ghi tài chính của một vị thế, positionId = {}",
                    positionId != null ? positionId.getValue() : "null", e);
            throw new DatabaseException("Lỗi khi tìm tất cả bản ghi tài chính của một vị thế: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm tất cả bản ghi tài chính của một vị thế thất bại
     * @param e Ngoại lệ
     * @param positionId ID của vị thế
     * @return Danh sách rỗng
     */
    @Recover
    public List<FinancialRecord> recoverFindAllByPositionId(Exception e, PositionId positionId) {
        log.error("Đã thử lại tìm tất cả bản ghi tài chính của một vị thế 3 lần nhưng thất bại, positionId = {}",
                positionId != null ? positionId.getValue() : "null", e);
        return List.of();
    }

    /**
     * Tìm tất cả bản ghi tài chính theo loại với xử lý ngoại lệ
     * @param type Loại bản ghi tài chính
     * @return Danh sách bản ghi tài chính
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<FinancialRecord> findAllByType(FinancialRecordType type) {
        try {
            log.debug("Tìm tất cả bản ghi tài chính theo loại, type = {}", type);

            if (type == null) {
                return List.of();
            }

            List<FinancialRecordJpaEntity> entities = financialRecordJpaRepository.findAllByType(type);
            return entities.stream()
                    .map(this::mapToDomainEntity)
                    .toList();
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả bản ghi tài chính theo loại, type = {}", type, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả bản ghi tài chính theo loại, type = {}", type, e);
            throw new DatabaseException("Lỗi khi tìm tất cả bản ghi tài chính theo loại: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm tất cả bản ghi tài chính theo loại thất bại
     * @param e Ngoại lệ
     * @param type Loại bản ghi tài chính
     * @return Danh sách rỗng
     */
    @Recover
    public List<FinancialRecord> recoverFindAllByType(Exception e, FinancialRecordType type) {
        log.error("Đã thử lại tìm tất cả bản ghi tài chính theo loại 3 lần nhưng thất bại, type = {}", type, e);
        return List.of();
    }

    /**
     * Tìm tất cả bản ghi tài chính của một thành viên trong khoảng thời gian với xử lý ngoại lệ
     * @param memberId ID của thành viên
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách bản ghi tài chính
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<FinancialRecord> findAllByMemberIdAndCreateTimeBetween(Long memberId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.debug("Tìm tất cả bản ghi tài chính của một thành viên trong khoảng thời gian, memberId = {}, startTime = {}, endTime = {}",
                    memberId, startTime, endTime);

            if (ObjectUtils.isEmpty(memberId) || startTime == null || endTime == null) {
                return List.of();
            }

            List<FinancialRecordJpaEntity> entities = financialRecordJpaRepository.findAllByMemberIdAndCreateTimeBetween(memberId, startTime, endTime);
            return entities.stream()
                    .map(this::mapToDomainEntity)
                    .toList();
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm tất cả bản ghi tài chính của một thành viên trong khoảng thời gian, memberId = {}, startTime = {}, endTime = {}",
                    memberId, startTime, endTime, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm tất cả bản ghi tài chính của một thành viên trong khoảng thời gian, memberId = {}, startTime = {}, endTime = {}",
                    memberId, startTime, endTime, e);
            throw new DatabaseException("Lỗi khi tìm tất cả bản ghi tài chính của một thành viên trong khoảng thời gian: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm tất cả bản ghi tài chính của một thành viên trong khoảng thời gian thất bại
     * @param e Ngoại lệ
     * @param memberId ID của thành viên
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @return Danh sách rỗng
     */
    @Recover
    public List<FinancialRecord> recoverFindAllByMemberIdAndCreateTimeBetween(Exception e, String memberId, LocalDateTime startTime, LocalDateTime endTime) {
        log.error("Đã thử lại tìm tất cả bản ghi tài chính của một thành viên trong khoảng thời gian 3 lần nhưng thất bại, memberId = {}, startTime = {}, endTime = {}",
                memberId, startTime, endTime, e);
        return List.of();
    }

    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domainEntity Domain entity
     * @return JPA entity
     */
    private FinancialRecordJpaEntity mapToJpaEntity(FinancialRecord domainEntity) {
        // Emergency validation để đảm bảo không có overflow
        BigDecimal originalAmount = domainEntity.getAmount() != null ? domainEntity.getAmount().getValue() : null;
        BigDecimal originalFee = domainEntity.getFee() != null ? domainEntity.getFee().getValue() : null;
        BigDecimal originalProfit = domainEntity.getProfit() != null ? domainEntity.getProfit().getValue() : null;

        // Log original values
        log.info("FINANCIAL_RECORD_MAPPING: Original values - amount: {}, fee: {}, profit: {}",
                originalAmount, originalFee, originalProfit);

        // Use emergency validator to force cap values
        BigDecimal[] validatedValues = EmergencyBigDecimalValidator.validateFinancialRecordValues(
                originalAmount, originalFee, originalProfit);

        BigDecimal validatedAmount = validatedValues[0];
        BigDecimal validatedFee = validatedValues[1];
        BigDecimal validatedProfit = validatedValues[2];

        // Double check with regular validator as backup
        if (validatedAmount != null) {
            validatedAmount = BigDecimalValidator.validateAndScale(validatedAmount, "amount");
        }
        if (validatedFee != null) {
            validatedFee = BigDecimalValidator.validateAndScale(validatedFee, "fee");
        }
        if (validatedProfit != null) {
            validatedProfit = BigDecimalValidator.validateAndScale(validatedProfit, "profit");
        }

        // Log final values
        log.info("FINANCIAL_RECORD_MAPPING: Final validated values - amount: {}, fee: {}, profit: {}",
                validatedAmount, validatedFee, validatedProfit);

        return FinancialRecordJpaEntity.builder()
                .id(domainEntity.getId() != null ? domainEntity.getId() : snowflakeIdGenerator.nextIdWithPrefix("FR"))
                .memberId(domainEntity.getMemberId())
                .positionId(domainEntity.getPositionId() != null ? domainEntity.getPositionId().getValue() : null)
                .type(domainEntity.getType())
                .amount(validatedAmount)
                .fee(validatedFee)
                .profit(validatedProfit)
                .createTime(domainEntity.getCreateTime())
                .remark(domainEntity.getRemark())
                .build();
    }

    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param jpaEntity JPA entity
     * @return Domain entity
     */
    private FinancialRecord mapToDomainEntity(FinancialRecordJpaEntity jpaEntity) {
        return FinancialRecord.builder()
                .id(jpaEntity.getId())
                .memberId(jpaEntity.getMemberId())
                .positionId(jpaEntity.getPositionId() != null ? PositionId.of(jpaEntity.getPositionId()) : null)
                .type(jpaEntity.getType())
                .amount(jpaEntity.getAmount() != null ? Money.of(jpaEntity.getAmount()) : null)
                .fee(jpaEntity.getFee() != null ? Money.of(jpaEntity.getFee()) : null)
                .profit(jpaEntity.getProfit() != null ? Money.of(jpaEntity.getProfit()) : null)
                .createTime(jpaEntity.getCreateTime())
                .remark(jpaEntity.getRemark())
                .build();
    }
}
