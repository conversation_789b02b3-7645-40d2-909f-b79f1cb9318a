spring:

  zipkin:
    base-url: http://************:9411
    enabled: true
    sender:
      type: web

  sleuth:
    sampler:
      percentage: 1.0
      probability: 1.0
    enabled: true

  datasource:
    url: ${DATASOURCE_URL:*******************************************************************}
    username: ${DATASOURCE_USERNAME:lotus_root}
    password: ${DATASOURCE_PASSWORD:5mxFmM53B0BnKli34RlhfyXBjVf2A4TDMzd1yRRHtVgIieJrw2IRpOnEfSLVJ3si}
    hikari:
      minimum-idle: 1
      maximum-pool-size: 2

  jpa:
    properties:
      hibernate:
        default_schema: ${DEFAULT_SCHEMA:public}
    hibernate:
      ddl-auto: none
    show-sql: ${SHOW_SQL:true}
    database: postgresql


  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:9092}
    consumer:
      group-id: ${KAFKA_CONSUMER_GROUP_ID:default-group}
    properties:
      sasl.mechanism: PLAIN

  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: http://************:8082/realms/cex-lotus
      client:
        registration:
          keycloak:
            client-id: internal-service
            client-secret: X9EopoZ44E0gmdBrVpia8zZI9xDsR37e
            authorization-grant-type: client_credentials
            scope: openid
        provider:
          keycloak:
            issuer-uri: http://************:8082/realms/cex-lotus
            token-uri: http://************:8082/realms/cex-lotus/protocol/openid-connect/token


  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:8500}

  data:
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:2m0881Xc30Wh}
    mongodb:
      uri: ${SPRING_MONGODB_URI:****************************************************}

keycloak:
  auth-server-url: http://************:8082
  realm: cex-lotus
  resource: cex-exchange-api
  credentials:
    secret: UTj5XuHBZiLHJSZYUd7IJ2uOpyL4IG8S

cex-security:
  permit-all-endpoints:
    - "/**"
  resource-server-enabled: true
