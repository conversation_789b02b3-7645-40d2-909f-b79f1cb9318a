<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <springProperty scope="context" name="SERVICE_NAME" source="spring.application.name"/>
    <!-- Application Context Name -->
    <contextName>logback-spring</contextName>
    <!-- Console Appender for Local Logs -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="LOGSTASH" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
        <destination>10.242.30.45:50000</destination>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <includeMdc>true</includeMdc>
            <includeContext>true</includeContext>
            <customFields>{"service.name":"${SERVICE_NAME}"}</customFields>
        </encoder>
    </appender>
    <!-- Configure the Root Logger -->
    <root level="info">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="LOGSTASH"/>
    </root>
</configuration>