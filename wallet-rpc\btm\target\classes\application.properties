server.port=7011
spring.application.name=service-rpc-btm
#kafka
# Kafka\u5355\u673A\u914D\u7F6E
spring.kafka.bootstrap-servers=***************:9092
# \u9ED8\u8BA4\u7EC4
spring.kafka.consumer.group-id=default-group
# \u9ED8\u8BA4\u4E3B\u9898
spring.kafka.template.default-topic= test
# \u5728\u4FA6\u542C\u5668\u5BB9\u5668\u4E2D\u8FD0\u884C\u7684\u7EBF\u7A0B\u6570\u0087\u008F
spring.kafka.listener.concurrency= 3
# \u8FD9\u6709\u52A9\u4E8E\u63D0\u5347\u5BA2\u6237\u7AEF\u548C\u670D\u52A1\u5668\u4E0A\u7684\u6027\u80FD\uFF0C\u6B64\u914D\u7F6E\u63A7\u5236\u9ED8\u8BA4\u6279\u91CF\u5927\u5C0F\uFF08\u4EE5\u5B57\u8282\u4E3A\u5355\u4F4D\uFF09\uFF0C\u9ED8\u8BA4\u503C\u4E3A16384\u0087\u008F
spring.kafka.producer.batch-size= 1000

# mongodb
spring.data.mongodb.uri=********************************************************

eureka.client.serviceUrl.defaultZone=http://***************:7000/eureka/
# \u4EE5IP\u5730\u5740\u6CE8\u518C\u5230\u670D\u52A1\u4E2D\u5FC3\uFF0C\u76F8\u4E92\u6CE8\u518C\u4F7F\u7528IP\u5730\u5740\u0090\u008D
eureka.instance.prefer-ip-address=true

bytom.api.url=http://***************:9888/
client.access.token=bizzan:b44774eea4ed40c055d713c676b00c80eb461dd1d829329e92e8ef12b07e01d

bytom.alias=bizzan
bytom.password=fdafdssddds

coin.rpc=
coin.name=Bytom
coin.unit=BTM

watcher.init-block-height=334504
watcher.step=5
watcher.confirmation=3
watcher.interval=20000
