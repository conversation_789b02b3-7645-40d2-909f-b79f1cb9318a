package com.icetea.lotus.walletrpc.controller;

import com.icetea.lotus.walletrpc.component.EthWatcher;
import com.icetea.lotus.walletrpc.entity.Coin;
import com.icetea.lotus.walletrpc.service.AccountService;
import com.icetea.lotus.walletrpc.service.EthService;
import com.icetea.lotus.walletrpc.util.MessageResult;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.methods.response.EthBlockNumber;
import org.web3j.protocol.core.methods.response.EthGasPrice;
import org.web3j.protocol.core.methods.response.EthTransaction;
import org.web3j.utils.Convert;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/rpc")
public class WalletController {

    private final Logger logger = LoggerFactory.getLogger(WalletController.class);

    @Autowired
    private EthService service;

    @Autowired
    private Web3j web3j;

    @Autowired
    private EthWatcher watcher;

    @Autowired
    private Coin coin;

    @Autowired
    private AccountService accountService;

    @GetMapping("height")
    public MessageResult getHeight() {
        try {
            EthBlockNumber blockNumber = web3j.ethBlockNumber().send();
            long rpcBlockNumber = blockNumber.getBlockNumber().longValue();
            MessageResult result = new MessageResult(0, "success");
            result.setData(rpcBlockNumber);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return MessageResult.error(500, "getHeight error:" + e.getMessage());
        }
    }


    @GetMapping("address/{account}")
    public MessageResult getNewAddress(@PathVariable String account, @RequestParam(required = false, defaultValue = "") String password) {
        logger.info("create new account={},password={}", account, password);
        logger.info("==========================Thread::{}", Thread.currentThread().getName());
        try {
            String address = service.createNewWallet(account, password);
            MessageResult result = new MessageResult(0, "success");
            result.setData(address);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return MessageResult.error(500, "rpc error:" + e.getMessage());
        }
    }

    @GetMapping("transfer")
    public MessageResult transfer(@RequestParam String address, @RequestParam BigDecimal amount, @RequestParam BigDecimal fee) {
        logger.info("transfer:address={},amount={},fee={}", address, amount, fee);
        try {
            if (fee == null || fee.compareTo(BigDecimal.ZERO) <= 0) {
                fee = service.getMinerFee(coin.getGasLimit());
            }
            MessageResult result = service.transferFromWallet(address, amount, fee, coin.getMinCollectAmount());
            logger.info("transfer : {}", result.toString());
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return MessageResult.error(500, "error:" + e.getMessage());
        }
    }

    @GetMapping("withdraw")
    public MessageResult withdraw(@RequestParam String address,
                                  @RequestParam BigDecimal amount,
                                  @RequestParam(name = "sync",required = false,defaultValue = "true") Boolean sync,
                                  @RequestParam(name = "withdrawId",required = false,defaultValue = "") String withdrawId) {
        logger.info("withdraw:to={},amount={},sync={},withdrawId={}", address, amount, sync,withdrawId);
        try {
            return service.transferFromWithdrawWallet(address, amount,sync,withdrawId);
        } catch (Exception e) {
            e.printStackTrace();
            return MessageResult.error(500, "error:" + e.getMessage());
        }
    }

    /**
     * balance
     *
     * @return MessageResult
     */
    @GetMapping("balance")
    public MessageResult balance() {
        try {
            BigDecimal balance = accountService.findBalanceSum();
            MessageResult result = new MessageResult(0, "success");
            result.setData(balance);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return MessageResult.error(500, "The query failed，error:" + e.getMessage());
        }
    }


    /**
     * addressBalance
     *
     * @param address String
     * @return MessageResult
     */
    @GetMapping("balance/{address}")
    public MessageResult addressBalance(@PathVariable String address) {
        try {
            BigDecimal balance = service.getBalance(address);
            MessageResult result = new MessageResult(0, "success");
            result.setData(balance);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return MessageResult.error(500, "addressBalance error:" + e.getMessage());
        }
    }

    @GetMapping("transaction/{txid}")
    public MessageResult transaction(@PathVariable String txid) throws IOException {
        EthTransaction transaction = web3j.ethGetTransactionByHash(txid).send();
        EthGasPrice gasPrice = web3j.ethGasPrice().send();

        System.out.println(gasPrice.getGasPrice());
        System.out.println(transaction.getRawResponse());
        Map<String, Object> result = new HashMap<>();
        result.put("gasPrice", gasPrice.getGasPrice());
        result.put("transaction", transaction.getResult());
        MessageResult messageResult = new MessageResult(0, "success");
        messageResult.setData(result);
        return messageResult;
    }

    @GetMapping("gas-price")
    public MessageResult gasPrice() {
        try {
            BigInteger gasPrice = service.getGasPrice();
            MessageResult result = new MessageResult(0, "success");
            result.setData(Convert.fromWei(gasPrice.toString(), Convert.Unit.GWEI));
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return MessageResult.error(500, "gasPrice error:" + e.getMessage());
        }
    }

    @GetMapping("sync-block")
    public MessageResult manualSync(@RequestParam Long startBlock,@RequestParam Long endBlock) {
        try {
            watcher.replayBlockInit(startBlock, endBlock);
        } catch (IOException e) {
            e.printStackTrace();
            return MessageResult.error(500, "manualSync：" + e.getMessage());
        }
        return MessageResult.success();
    }
}
