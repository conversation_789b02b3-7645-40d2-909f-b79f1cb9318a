package com.icetea.lotus.core.domain.entity;

import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.PositionId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

/**
 * Domain entity cho vị thế
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Setter
public class Position {
    private PositionId id;
    private Long memberId;
    private Symbol symbol;
    private PositionDirection direction;
    private BigDecimal volume;
    private Money openPrice;
    private Money closePrice;
    private Money liquidationPrice;
    private Money maintenanceMargin;
    private Money margin;
    private Money profit;
    private Money remainingMargin;
    private MarginMode marginMode;
    private BigDecimal leverage;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String remark;
    private PositionStatus status;

    /**
     * Tính toán giá trị vị thế
     *
     * @return Gi<PERSON> trị vị thế
     */
    public Money calculateValue() {
        return openPrice.multiply(volume);
    }

    /**
     * Tính toán lợi nhuận không thực hiện
     *
     * @param currentPrice Giá hiện tại
     * @return Lợi nhuận không thực hiện
     */
    public Money calculateUnrealizedProfit(Money currentPrice) {
        // Validate input values trước khi tính toán
        BigDecimal validatedCurrentPrice = com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator
                .validateAndScale(currentPrice.getValue(), "currentPrice");
        BigDecimal validatedOpenPrice = com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator
                .validateAndScale(openPrice.getValue(), "openPrice");
        BigDecimal validatedVolume = com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator
                .validateAndScale(volume, "volume");

        BigDecimal priceDiff;
        if (direction == PositionDirection.LONG) {
            priceDiff = validatedCurrentPrice.subtract(validatedOpenPrice);
        } else {
            priceDiff = validatedOpenPrice.subtract(validatedCurrentPrice);
        }

        // Validate price difference
        priceDiff = com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator
                .validateAndScale(priceDiff, "priceDiff");

        // Calculate profit
        BigDecimal profit = priceDiff.multiply(validatedVolume);

        // Validate final profit value
        profit = com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator
                .validateAndScale(profit, "unrealizedProfit");

        return Money.of(profit);
    }

    /**
     * Tính toán tỷ lệ lợi nhuận
     *
     * @param currentPrice Giá hiện tại
     * @return Tỷ lệ lợi nhuận
     */
    public BigDecimal calculateProfitRatio(Money currentPrice) {
        Money unrealizedProfit = calculateUnrealizedProfit(currentPrice);
        return unrealizedProfit.getValue()
                .divide(margin.getValue(), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
    }

    /**
     * Kiểm tra xem vị thế có cần thanh lý không
     *
     * @param currentPrice          Giá hiện tại
     * @param maintenanceMarginRate Tỷ lệ ký quỹ bảo trì
     * @return true nếu vị thế cần thanh lý
     */
    public boolean needsLiquidation(Money currentPrice, BigDecimal maintenanceMarginRate) {
        Money unrealizedProfit = calculateUnrealizedProfit(currentPrice);
        Money availableMargin = margin.add(unrealizedProfit);
        Money requiredMaintenanceMargin = calculateValue().multiply(maintenanceMarginRate);

        return availableMargin.isLessThan(requiredMaintenanceMargin);
    }

    /**
     * Tính toán giá thanh lý theo công thức Binance
     * Binance formula:
     * For LONG: Liquidation Price = (Entry Price * Size - Initial Margin + Maintenance Margin) / Size
     * For SHORT: Liquidation Price = (Entry Price * Size + Initial Margin - Maintenance Margin) / Size
     *
     * @param maintenanceMarginRate Tỷ lệ ký quỹ bảo trì
     * @return Giá thanh lý
     */
    public Money calculateLiquidationPrice(BigDecimal maintenanceMarginRate) {
        if (volume.compareTo(BigDecimal.ZERO) == 0) {
            return Money.ZERO;
        }

        BigDecimal entryValue = openPrice.getValue().multiply(volume);
        BigDecimal maintenanceMarginAmount = entryValue.multiply(maintenanceMarginRate);
        BigDecimal liquidationPriceValue;

        if (direction == PositionDirection.LONG) {
            // LONG: Liq Price = (Entry Value - Initial Margin + Maintenance Margin) / Volume
            liquidationPriceValue = entryValue.subtract(margin.getValue()).add(maintenanceMarginAmount)
                    .divide(volume, 8, RoundingMode.HALF_UP);
        } else {
            // SHORT: Liq Price = (Entry Value + Initial Margin - Maintenance Margin) / Volume
            liquidationPriceValue = entryValue.add(margin.getValue()).subtract(maintenanceMarginAmount)
                    .divide(volume, 8, RoundingMode.HALF_UP);
        }

        return Money.of(liquidationPriceValue.max(BigDecimal.ZERO));
    }

    /**
     * Tính toán break even price (giá hòa vốn) theo công thức Binance
     * Binance formula tính phí cả mở và đóng vị thế:
     * For LONG: Break Even = Entry Price * (1 + 2 * Fee Rate)
     * For SHORT: Break Even = Entry Price * (1 - 2 * Fee Rate)
     *
     * @param feeRate Tỷ lệ phí giao dịch (cho cả mở và đóng)
     * @return Break even price
     */
    public Money calculateBreakEvenPrice(BigDecimal feeRate) {
        if (volume.compareTo(BigDecimal.ZERO) == 0) {
            return Money.ZERO;
        }

        BigDecimal breakEvenPriceValue;
        BigDecimal totalFeeRate = feeRate.multiply(BigDecimal.valueOf(2)); // Phí mở + phí đóng

        if (direction == PositionDirection.LONG) {
            // LONG: Break Even Price = Entry Price * (1 + 2 * Fee Rate)
            breakEvenPriceValue = openPrice.getValue().multiply(BigDecimal.ONE.add(totalFeeRate));
        } else {
            // SHORT: Break Even Price = Entry Price * (1 - 2 * Fee Rate)
            breakEvenPriceValue = openPrice.getValue().multiply(BigDecimal.ONE.subtract(totalFeeRate));
        }

        return Money.of(breakEvenPriceValue.max(BigDecimal.ZERO));
    }

    /**
     * Tính toán margin ratio theo công thức Binance
     * Margin Ratio = Maintenance Margin / (Wallet Balance + Unrealized PNL)
     *
     * @param currentPrice Giá hiện tại
     * @param maintenanceMarginRate Tỷ lệ ký quỹ bảo trì
     * @return Margin ratio (100% means liquidation)
     */
    public BigDecimal calculateMarginRatio(Money currentPrice, BigDecimal maintenanceMarginRate) {
        Money unrealizedPnl = calculateUnrealizedProfit(currentPrice);
        Money walletBalance = margin.add(unrealizedPnl);

        // Maintenance Margin = Position Value * Maintenance Margin Rate
        Money positionValue = Money.of(openPrice.getValue().multiply(volume));
        Money maintenanceMargin = Money.of(positionValue.getValue().multiply(maintenanceMarginRate));

        if (walletBalance.getValue().compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.valueOf(100); // 100% if no balance left
        }

        // Margin Ratio = (Maintenance Margin / Wallet Balance) * 100
        return maintenanceMargin.getValue()
                .divide(walletBalance.getValue(), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
    }

    /**
     * Get entry price (alias for open price for Binance compatibility)
     */
    public Money getEntryPrice() {
        return openPrice;
    }
}
