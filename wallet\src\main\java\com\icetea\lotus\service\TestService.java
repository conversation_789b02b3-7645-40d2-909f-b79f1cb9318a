package com.icetea.lotus.service;

import com.icetea.lotus.util.MessageResult;
import org.springframework.http.ResponseEntity;

import java.util.LinkedHashMap;

public interface TestService {

    /**
     * Retrieves the block height for a given currency unit.
     *
     * @param unit The currency unit (e.g., BTC, ETH).
     * @return The block height result.
     * @throws IllegalArgumentException if the unit is invalid or service is unavailable.
     */
    ResponseEntity<MessageResult> getBlockHeight(String unit);

    /**
     * Checks the status of RPC services for all coins.
     *
     * @return A map of RPC service check results.
     * @throws IllegalArgumentException if no RPC units are available.
     */
    LinkedHashMap<String, ResponseEntity<MessageResult>> checkRpcServices();
}