spring:

  datasource:
    url: ${DATASOURCE_URL:*******************************************************************}
    username: ${DATASOURCE_USERNAME:lotus_root}
    password: ${DATASOURCE_PASSWORD:5mxFmM53B0BnKli34RlhfyXBjVf2A4TDMzd1yRRHtVgIieJrw2IRpOnEfSLVJ3si}
    hikari:
      minimum-idle: 1
      maximum-pool-size: 2
  jpa:
    properties:
      hibernate:
        default_schema: ${DEFAULT_SCHEMA:public}
    show-sql: ${SHOW_SQL:true}
    database: postgresql

  data:
    mongodb:
      uri: ${SPRING_MONGODB_URI:****************************************************}

    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:6379}

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:8500}
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        health-check-path: "${server.servlet.context-path}/actuator/health"
        health-check-interval: 10s
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${random.value}
    stream:
      kafka:
        binder:
          headers:
            - spanId
            - spanSampled
            - spanProcessId
            - spanParentSpanId
            - spanTraceId
            - spanName
            - messageSent

  zipkin:
    base-url: http://************:9411
    enabled: true
    sender:
      type: web

cex-services:
  market-service: "market"
  future-service: "future"

#S3 Config
s3:
  access-key-id: ${S3_ACCESS_KEY_ID:}
  access-key-secret: ${S3_ACCESS_KEY_SECRET:}
  bucket-name: ${S3_BUCKET_NAME:}
  regions-name: ${S3_REGIONS_NAME:}

#Minio Config
minio:
  protocol: ${MINIO_PROTOCOL:http}
  host: ${MINIO_HOST:************}
  port: ${MINIO_PORT:9000}
  access-key: ${MINO_ACCESS_KEY:VTp9kKgOQBdn8OdCHn0V}
  secret-key: ${MINIO_SECRET-KEY:Fda7Rfd1Roafn0e9SND57T2T16MXfkIlVQaK6fJe}
  bucket-name: ${MINIO_BUCKET_NAME:icetea-software-file}
