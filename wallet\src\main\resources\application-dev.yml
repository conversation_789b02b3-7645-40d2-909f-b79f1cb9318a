spring:
  datasource:
    driver-class-name: ${DATASOURCE_DRIVER_CLASS_NAME:org.postgresql.Driver}
    url: ${DATASOURCE_URL:*******************************************************************}
    username: ${DATASOURCE_USERNAME:lotus_root}
    password: ${DATASOURCE_PASSWORD:5mxFmM53B0BnKli34RlhfyXBjVf2A4TDMzd1yRRHtVgIieJrw2IRpOnEfSLVJ3si}

  jpa:
    properties:
      hibernate:
        default_schema: ${DEFAULT_SCHEMA:public}
    show-sql: ${SHOW_SQL:true}
    database: postgresql

  data:
    redis:
      host: ************
      port: 6379
      password: 2m0881Xc30Wh
      database: ${REDIS_DATABASE:0}
      timeout: ${REDIS_TIMEOUT:5000}
    mongodb:
      uri: ${SPRING_MONGODB_URI:*****************************************************

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:9092}
    properties:
      sasl.mechanism: PLAIN

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:8500}