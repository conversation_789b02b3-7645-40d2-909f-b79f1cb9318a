package com.icetea.lotus.application.service;

import com.icetea.lotus.application.dto.AdjustLeverageCommand;
import com.icetea.lotus.application.dto.AdjustMarginCommand;
import com.icetea.lotus.application.dto.ClosePositionCommand;
import com.icetea.lotus.application.dto.PositionDto;
import com.icetea.lotus.application.mapper.PositionMapper;
import com.icetea.lotus.application.port.input.ManagePositionUseCase;
import com.icetea.lotus.application.port.output.PositionPersistencePort;
import com.icetea.lotus.core.domain.entity.Contract;
import com.icetea.lotus.core.domain.entity.MarginMode;
import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.PositionStatus;
import com.icetea.lotus.core.domain.exception.BusinessException;
import com.icetea.lotus.core.domain.exception.EntityNotFoundException;
import com.icetea.lotus.core.domain.exception.ValidationException;
import com.icetea.lotus.core.domain.repository.ContractRepository;
import com.icetea.lotus.core.domain.service.PositionOrderService;
import com.icetea.lotus.core.domain.service.PositionService;
import com.icetea.lotus.core.domain.service.PriceManagementService;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.OrderType;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.DatabaseException;
import com.icetea.lotus.infrastructure.messaging.producer.OrderCommandProducer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service cho việc quản lý vị thế
 * Triển khai use case ManagePositionUseCase
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ManagePositionService implements ManagePositionUseCase {

    private final PositionPersistencePort positionPersistencePort;
    private final ContractRepository contractRepository;
    private final PositionService positionService;
    private final PositionMapper positionMapper;
    private final PriceManagementService priceManagementService;
    private final PositionOrderService positionOrderService;
    private final OrderCommandProducer orderCommandProducer;

    /**
     * Lấy vị thế theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Thông tin vị thế
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public PositionDto getPosition(Long memberId, String symbol) {
        try {
            log.debug("Lấy vị thế theo memberId và symbol, memberId = {}, symbol = {}", memberId, symbol);

            if (memberId == null) {
                throw new ValidationException("MemberId không được để trống");
            }

            if (symbol == null || symbol.isEmpty()) {
                throw new ValidationException("Symbol không được để trống");
            }

            Optional<Position> positionOpt = positionPersistencePort.findByMemberIdAndSymbol(memberId, Symbol.of(symbol));

            if (positionOpt.isEmpty()) {
                log.debug("Không tìm thấy vị thế, memberId = {}, symbol = {}", memberId, symbol);
                return null;
            }

            Position position = positionOpt.get();

            // Lấy giá đánh dấu hiện tại
            Money currentPrice = priceManagementService.getCurrentMarkPrice(Symbol.of(symbol));

            // Lấy giá chỉ số hiện tại
            Money indexPrice = priceManagementService.getCurrentIndexPrice(Symbol.of(symbol));

            // Lấy thông tin hợp đồng để tính break even price
            Optional<Contract> contractOpt = contractRepository.findBySymbolOptional(Symbol.of(symbol));

            // Tính toán lợi nhuận không thực hiện
            Money unrealizedProfit = positionService.calculateUnrealizedProfit(position, currentPrice);

            // Tính toán tỷ lệ lợi nhuận
            BigDecimal profitRatio = positionService.calculateProfitRatio(position, currentPrice);

            // Chuyển đổi sang DTO
            PositionDto positionDto = positionMapper.toDto(position);
            positionDto.setUnrealizedProfit(unrealizedProfit.getValue());
            positionDto.setProfitRatio(profitRatio);
            positionDto.setMarkPrice(currentPrice.getValue());
            positionDto.setIndexPrice(indexPrice.getValue());

            // Tính toán break even price nếu có thông tin hợp đồng
            if (contractOpt.isPresent()) {
                Contract contract = contractOpt.get();
                Money breakEvenPrice = position.calculateBreakEvenPrice(contract.getTakerFeeRate().getValue());
                positionDto.setBreakEvenPrice(breakEvenPrice.getValue());

                // Tính toán margin ratio
                BigDecimal marginRatio = position.calculateMarginRatio(currentPrice, contract.getMaintenanceMarginRate().getValue());
                positionDto.setMarginRatio(marginRatio);
            } else {
                log.warn("Không tìm thấy hợp đồng cho symbol = {}, không thể tính break even price", symbol);
                positionDto.setBreakEvenPrice(position.getOpenPrice().getValue()); // Fallback to entry price
                positionDto.setMarginRatio(BigDecimal.ZERO);
            }

            log.debug("Đã lấy vị thế thành công, memberId = {}, symbol = {}", memberId, symbol);

            return positionDto;
        } catch (ValidationException e) {
            log.warn("Lỗi xác thực khi lấy vị thế: {}", e.getMessage());
            throw e;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lấy vị thế, memberId = {}, symbol = {}", memberId, symbol, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lấy vị thế, memberId = {}, symbol = {}", memberId, symbol, e);
            throw new DatabaseException("Lỗi khi lấy vị thế: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lấy vị thế thất bại
     * @param e Ngoại lệ
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return PositionDto
     */
    @Recover
    public PositionDto recoverGetPosition(Exception e, Long memberId, String symbol) {
        log.error("Đã thử lại lấy vị thế 3 lần nhưng thất bại, memberId = {}, symbol = {}", memberId, symbol, e);
        return null;
    }

    /**
     * Lấy tất cả các vị thế của một thành viên
     * @param memberId ID của thành viên
     * @return Danh sách các vị thế
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<PositionDto> getAllPositions(Long memberId) {
        try {
            log.debug("Lấy tất cả các vị thế của thành viên, memberId = {}", memberId);

            if (memberId == null) {
                throw new ValidationException("MemberId không được để trống");
            }

            List<Position> positions = positionPersistencePort.findAllByMemberId(memberId);

            if (positions.isEmpty()) {
                log.debug("Không tìm thấy vị thế nào, memberId = {}", memberId);
                return List.of();
            }

            // Lấy giá hiện tại cho tất cả các symbol
            Map<String, Money> currentPrices = new HashMap<>();
            Map<String, Money> indexPrices = new HashMap<>();
            Map<String, Contract> contracts = new HashMap<>();

            for (Position position : positions) {
                String symbol = position.getSymbol().getValue();
                if (!currentPrices.containsKey(symbol)) {
                    currentPrices.put(symbol, priceManagementService.getCurrentMarkPrice(Symbol.of(symbol)));
                    indexPrices.put(symbol, priceManagementService.getCurrentIndexPrice(Symbol.of(symbol)));

                    // Lấy thông tin hợp đồng
                    Optional<Contract> contractOpt = contractRepository.findBySymbolOptional(Symbol.of(symbol));
                    if (contractOpt.isPresent()) {
                        contracts.put(symbol, contractOpt.get());
                    }
                }
            }

            // Chuyển đổi sang DTO
            List<PositionDto> result = positions.stream()
                    .map(position -> {
                        String symbol = position.getSymbol().getValue();
                        Money currentPrice = currentPrices.get(symbol);
                        Money indexPrice = indexPrices.get(symbol);
                        Contract contract = contracts.get(symbol);

                        Money unrealizedProfit = positionService.calculateUnrealizedProfit(position, currentPrice);
                        BigDecimal profitRatio = positionService.calculateProfitRatio(position, currentPrice);

                        PositionDto positionDto = positionMapper.toDto(position);
                        positionDto.setUnrealizedProfit(unrealizedProfit.getValue());
                        positionDto.setProfitRatio(profitRatio);
                        positionDto.setMarkPrice(currentPrice.getValue());
                        positionDto.setIndexPrice(indexPrice.getValue());

                        // Tính toán break even price và margin ratio nếu có hợp đồng
                        if (contract != null) {
                            Money breakEvenPrice = position.calculateBreakEvenPrice(contract.getTakerFeeRate().getValue());
                            positionDto.setBreakEvenPrice(breakEvenPrice.getValue());

                            BigDecimal marginRatio = position.calculateMarginRatio(currentPrice, contract.getMaintenanceMarginRate().getValue());
                            positionDto.setMarginRatio(marginRatio);
                        } else {
                            log.warn("Không tìm thấy hợp đồng cho symbol = {}, không thể tính break even price", symbol);
                            positionDto.setBreakEvenPrice(position.getOpenPrice().getValue()); // Fallback to entry price
                            positionDto.setMarginRatio(BigDecimal.ZERO);
                        }

                        return positionDto;
                    })
                    .toList();

            log.debug("Đã lấy {} vị thế thành công, memberId = {}", result.size(), memberId);

            return result;
        } catch (ValidationException e) {
            log.warn("Lỗi xác thực khi lấy tất cả các vị thế: {}", e.getMessage());
            throw e;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lấy tất cả các vị thế, memberId = {}", memberId, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lấy tất cả các vị thế, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi lấy tất cả các vị thế: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi lấy tất cả các vị thế thất bại
     * @param e Ngoại lệ
     * @param memberId ID của thành viên
     * @return List<PositionDto>
     */
    @Recover
    public List<PositionDto> recoverGetAllPositions(Exception e, Long memberId) {
        log.error("Đã thử lại lấy tất cả các vị thế 3 lần nhưng thất bại, memberId = {}", memberId, e);
        return List.of();
    }

    /**
     * Đóng vị thế
     * @param command Command chứa thông tin đóng vị thế
     * @return Thông tin vị thế sau khi đóng
     */
    @Override
    @Transactional
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public PositionDto closePosition(ClosePositionCommand command) {
        try {
            log.debug("Đóng vị thế, memberId = {}, symbol = {}, volume = {}, price = {}",
                    command.getMemberId(), command.getSymbol(), command.getVolume(), command.getPrice());

            // Command không thể null ở đây vì đã được sử dụng ở trên

            if (command.getMemberId() == null) {
                throw new ValidationException("MemberId không được để trống");
            }

            if (command.getSymbol() == null || command.getSymbol().isEmpty()) {
                throw new ValidationException("Symbol không được để trống");
            }

            if (command.getVolume() == null || command.getVolume().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ValidationException("Volume phải lớn hơn 0");
            }

            if (command.getPrice() == null || command.getPrice().compareTo(BigDecimal.ZERO) < 0) {
                throw new ValidationException("Price phải lớn hơn 0");
            }

            Optional<Position> positionOpt = positionPersistencePort.findByMemberIdAndSymbol(command.getMemberId(), Symbol.of(command.getSymbol()));

            if (positionOpt.isEmpty()) {
                throw new EntityNotFoundException("Không tìm thấy vị thế");
            }

            Position position = positionOpt.get();

            // Kiểm tra khối lượng đóng
            if (command.getVolume().compareTo(position.getVolume()) > 0) {
                throw new ValidationException("Khối lượng đóng không thể lớn hơn khối lượng vị thế");
            }

            // Bước 1: Tạo lệnh đóng vị thế
            log.debug("Bước 1: Tạo lệnh đóng vị thế");
            OrderType orderType = OrderType.LIMIT; // Mặc định là lệnh giới hạn
            Order order;

            // Xác định loại lệnh (MARKET hoặc LIMIT)
            if (command.getPrice().compareTo(BigDecimal.ZERO) == 0) {
                orderType = OrderType.MARKET;
                // Lấy giá đánh dấu hiện tại cho lệnh thị trường
                Money currentPrice = priceManagementService.getCurrentMarkPrice(position.getSymbol());
                order = positionOrderService.createClosePositionOrder(position, orderType, currentPrice);
            } else {
                order = positionOrderService.createClosePositionOrder(position, orderType, Money.of(command.getPrice()));
            }

            log.debug("Đã tạo lệnh đóng vị thế, orderId = {}", order.getOrderId().getValue());

            // Bước 2: Gửi lệnh đến hệ thống khớp lệnh thông qua Kafka
            log.debug("Bước 2: Gửi lệnh đến hệ thống khớp lệnh");
            orderCommandProducer.sendPlaceOrderCommand(order);
            log.debug("Đã gửi lệnh đến hệ thống khớp lệnh");

            // Bước 3: Cập nhật vị thế trong cơ sở dữ liệu
            log.debug("Bước 3: Cập nhật vị thế trong cơ sở dữ liệu");
            Position closedPosition = positionService.closePosition(position, Money.of(command.getPrice()), command.getVolume());
//            Position savedPosition = positionPersistencePort.save(closedPosition);


            log.debug("Đã cập nhật vị thế trong cơ sở dữ liệu");

            log.info("Đã đóng vị thế thành công, memberId = {}, symbol = {}, volume = {}",
                    command.getMemberId(), command.getSymbol(), command.getVolume());

            // Chuyển đổi sang DTO
            return positionMapper.toDto(closedPosition);
        } catch (ValidationException | EntityNotFoundException e) {
            log.warn("Lỗi xác thực khi đóng vị thế: {}", e.getMessage());
            throw e;
        } catch (BusinessException e) {
            log.error("Lỗi nghiệp vụ khi đóng vị thế: {}", e.getMessage());
            throw e;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi đóng vị thế, memberId = {}, symbol = {}", command.getMemberId(), command.getSymbol(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi đóng vị thế, memberId = {}, symbol = {}", command.getMemberId(), command.getSymbol(), e);
            throw new DatabaseException("Lỗi khi đóng vị thế: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi đóng vị thế thất bại
     * @param e Ngoại lệ
     * @param command Command chứa thông tin đóng vị thế
     * @return PositionDto
     */
    @Recover
    public PositionDto recoverClosePosition(Exception e, ClosePositionCommand command) {
        log.error("Đã thử lại đóng vị thế 3 lần nhưng thất bại, memberId = {}, symbol = {}", command.getMemberId(), command.getSymbol(), e);
        throw new DatabaseException("Không thể đóng vị thế sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Đóng tất cả các vị thế của một thành viên
     * @param memberId ID của thành viên
     * @return Số lượng vị thế đã đóng
     */
    @Override
    @Transactional
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public int closeAllPositions(Long memberId) {
        try {
            log.debug("Đóng tất cả các vị thế của thành viên, memberId = {}", memberId);

            if (memberId == null) {
                throw new ValidationException("MemberId không được để trống");
            }

            List<Position> positions = positionPersistencePort.findAllByMemberId(memberId);

            if (positions.isEmpty()) {
                log.debug("Không tìm thấy vị thế nào để đóng, memberId = {}", memberId);
                return 0;
            }

            int closedCount = 0;

            for (Position position : positions) {
                try {
                    // Lấy giá đánh dấu hiện tại
                    Money currentPrice = priceManagementService.getCurrentMarkPrice(position.getSymbol());

                    // Bước 1: Tạo lệnh đóng vị thế
                    log.debug("Bước 1: Tạo lệnh đóng vị thế cho position {}", position.getId().getValue());
                    Order order = positionOrderService.createClosePositionOrder(position, OrderType.MARKET, currentPrice);
                    log.debug("Đã tạo lệnh đóng vị thế, orderId = {}", order.getOrderId().getValue());

                    // Bước 2: Gửi lệnh đến hệ thống khớp lệnh thông qua Kafka
                    log.debug("Bước 2: Gửi lệnh đến hệ thống khớp lệnh");
                    orderCommandProducer.sendPlaceOrderCommand(order);
                    log.debug("Đã gửi lệnh đến hệ thống khớp lệnh");

                    // Bước 3: Cập nhật vị thế trong cơ sở dữ liệu
                    log.debug("Bước 3: Cập nhật vị thế trong cơ sở dữ liệu");
                    Position closedPosition = positionService.closePosition(position, currentPrice, position.getVolume());
                    positionPersistencePort.save(closedPosition);
                    log.debug("Đã cập nhật vị thế trong cơ sở dữ liệu");

                    closedCount++;

                    log.debug("Đã đóng vị thế thành công, memberId = {}, symbol = {}", memberId, position.getSymbol().getValue());
                } catch (Exception e) {
                    log.error("Lỗi khi đóng vị thế, memberId = {}, symbol = {}", memberId, position.getSymbol().getValue(), e);
                    // Tiếp tục đóng các vị thế khác
                }
            }

            log.info("Đã đóng {} vị thế thành công, memberId = {}", closedCount, memberId);

            return closedCount;
        } catch (ValidationException e) {
            log.warn("Lỗi xác thực khi đóng tất cả các vị thế: {}", e.getMessage());
            throw e;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi đóng tất cả các vị thế, memberId = {}", memberId, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi đóng tất cả các vị thế, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi đóng tất cả các vị thế: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi đóng tất cả các vị thế thất bại
     * @param e Ngoại lệ
     * @param memberId ID của thành viên
     * @return int
     */
    @Recover
    public int recoverCloseAllPositions(Exception e, Long memberId) {
        log.error("Đã thử lại đóng tất cả các vị thế 3 lần nhưng thất bại, memberId = {}", memberId, e);
        throw new DatabaseException("Không thể đóng tất cả các vị thế sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Điều chỉnh đòn bẩy cho vị thế
     * @param command Command chứa thông tin điều chỉnh đòn bẩy
     * @return Thông tin vị thế sau khi điều chỉnh
     */
    @Override
    @Transactional
    public PositionDto adjustLeverage(AdjustLeverageCommand command) {
        Optional<Position> positionOpt = positionPersistencePort.findByMemberIdAndSymbol(command.getMemberId(), Symbol.of(command.getSymbol()));

        if (positionOpt.isEmpty()) {
            throw new IllegalArgumentException("Position not found");
        }

        Position position = positionOpt.get();

        // Kiểm tra đòn bẩy
        if (command.getLeverage().compareTo(BigDecimal.ONE) < 0) {
            throw new IllegalArgumentException("Leverage must be greater than or equal to 1");
        }

        // Lấy hợp đồng
        Optional<Contract> contractOpt = contractRepository.findBySymbolOptional(position.getSymbol());

        if (contractOpt.isEmpty()) {
            throw new IllegalArgumentException("Contract not found");
        }

        Contract contract = contractOpt.get();

        // Kiểm tra đòn bẩy tối đa
        if (command.getLeverage().compareTo(BigDecimal.valueOf(contract.getLeverageMax())) > 0) {
            throw new IllegalArgumentException("Leverage cannot be greater than maximum leverage");
        }

        // Tính toán ký quỹ mới
        Money newMargin = Money.of(position.getOpenPrice().getValue().multiply(position.getVolume()).divide(command.getLeverage(), RoundingMode.HALF_UP));

        // Tạo vị thế mới => update
        position.setMargin(newMargin);
        position.setLeverage(command.getLeverage());
        position.setUpdateTime(LocalDateTime.now());

        // Lưu vị thế
        Position savedPosition = positionPersistencePort.save(position);

        // Chuyển đổi sang DTO
        return positionMapper.toDto(savedPosition);
    }

    /**
     * Điều chỉnh ký quỹ cho vị thế
     * @param command Command chứa thông tin điều chỉnh ký quỹ
     * @return Thông tin vị thế sau khi điều chỉnh
     */
    @Override
    @Transactional
    public PositionDto adjustMargin(AdjustMarginCommand command) {
        Optional<Position> positionOpt = positionPersistencePort.findByMemberIdAndSymbol(command.getMemberId(), Symbol.of(command.getSymbol()));

        if (positionOpt.isEmpty()) {
            throw new IllegalArgumentException("Position not found");
        }

        Position position = positionOpt.get();

        // Tính toán ký quỹ mới
        Money newMargin;
        if (command.isAdd()) {
            newMargin = position.getMargin().add(Money.of(command.getMargin()));
        } else {
            if (command.getMargin().compareTo(position.getMargin().getValue()) > 0) {
                throw new IllegalArgumentException("Cannot withdraw more than current margin");
            }
            newMargin = position.getMargin().subtract(Money.of(command.getMargin()));
        }

        // Tính toán đòn bẩy mới
        BigDecimal newLeverage = position.getOpenPrice().getValue().multiply(position.getVolume()).divide(newMargin.getValue(), RoundingMode.HALF_UP);

        // Tạo vị thế mới
        Position newPosition = Position.builder()
                .id(position.getId())
                .memberId(position.getMemberId())
                .symbol(position.getSymbol())
                .direction(position.getDirection())
                .volume(position.getVolume())
                .openPrice(position.getOpenPrice())
                .closePrice(position.getClosePrice())
                .liquidationPrice(position.getLiquidationPrice())
                .maintenanceMargin(position.getMaintenanceMargin())
                .margin(newMargin)
                .profit(position.getProfit())
                .marginMode(position.getMarginMode())
                .leverage(newLeverage)
                .createTime(position.getCreateTime())
                .updateTime(LocalDateTime.now())
                .remark(position.getRemark())
                .build();

        // Lưu vị thế
        Position savedPosition = positionPersistencePort.save(newPosition);

        // Chuyển đổi sang DTO
        return positionMapper.toDto(savedPosition);
    }

    /**
     * Thay đổi chế độ ký quỹ cho vị thế
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param marginMode Chế độ ký quỹ mới
     * @return Thông tin vị thế sau khi thay đổi
     */
    @Override
    @Transactional
    public PositionDto changeMarginMode(Long memberId, String symbol, MarginMode marginMode) {
        Optional<Position> positionOpt = positionPersistencePort.findByMemberIdAndSymbol(memberId, Symbol.of(symbol));

        if (positionOpt.isEmpty()) {
            throw new IllegalArgumentException("Position not found");
        }

        Position position = positionOpt.get();

        // Tạo vị thế mới
        Position newPosition = Position.builder()
                .id(position.getId())
                .memberId(position.getMemberId())
                .symbol(position.getSymbol())
                .direction(position.getDirection())
                .volume(position.getVolume())
                .openPrice(position.getOpenPrice())
                .closePrice(position.getClosePrice())
                .liquidationPrice(position.getLiquidationPrice())
                .maintenanceMargin(position.getMaintenanceMargin())
                .margin(position.getMargin())
                .profit(position.getProfit())
                .marginMode(marginMode)
                .leverage(position.getLeverage())
                .createTime(position.getCreateTime())
                .updateTime(LocalDateTime.now())
                .remark(position.getRemark())
                .build();

        // Lưu vị thế
        Position savedPosition = positionPersistencePort.save(newPosition);

        // Chuyển đổi sang DTO
        return positionMapper.toDto(savedPosition);
    }

    /**
     * Tính toán lợi nhuận không thực hiện cho vị thế
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Lợi nhuận không thực hiện
     */
    @Override
    public BigDecimal calculateUnrealizedProfit(Long memberId, String symbol) {
        Optional<Position> positionOpt = positionPersistencePort.findByMemberIdAndSymbol(memberId, Symbol.of(symbol));

        if (positionOpt.isEmpty()) {
            return BigDecimal.ZERO;
        }

        Position position = positionOpt.get();

        // Lấy giá đánh dấu hiện tại
        Money currentPrice = priceManagementService.getCurrentMarkPrice(Symbol.of(symbol));

        // Tính toán lợi nhuận không thực hiện
        Money unrealizedProfit = positionService.calculateUnrealizedProfit(position, currentPrice);

        return unrealizedProfit.getValue();
    }

    /**
     * Tính toán tổng lợi nhuận không thực hiện cho tất cả các vị thế của một thành viên
     * @param memberId ID của thành viên
     * @return Tổng lợi nhuận không thực hiện
     */
    @Override
    public BigDecimal calculateTotalUnrealizedProfit(Long memberId) {
        List<Position> positions = positionPersistencePort.findAllByMemberId(memberId);

        // Lấy giá hiện tại cho tất cả các symbol
        Map<Symbol, Money> currentPrices = new HashMap<>();
        for (Position position : positions) {
            Symbol symbol = position.getSymbol();
            if (!currentPrices.containsKey(symbol)) {
                currentPrices.put(symbol, priceManagementService.getCurrentMarkPrice(symbol));
            }
        }

        // Tính toán tổng lợi nhuận không thực hiện
        Money totalUnrealizedProfit = positionService.calculateTotalUnrealizedProfit(positions, currentPrices);

        return totalUnrealizedProfit.getValue();
    }

    /**
     * Tìm kiếm vị thế theo các tiêu chí
     * @param memberId ID của thành viên
     * @param status Trạng thái của vị thế
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @param page Số trang
     * @param size Kích thước trang
     * @return Danh sách các vị thế phân trang
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public com.icetea.lotus.core.domain.valueobject.Page<PositionDto> findPositions(Long memberId, PositionStatus status,
            LocalDateTime startTime, LocalDateTime endTime, int page, int size) {
        try {
            log.debug("Tìm kiếm vị thế, memberId = {}, status = {}, startTime = {}, endTime = {}, page = {}, size = {}",
                    memberId, status, startTime, endTime, page, size);

            if (memberId == null) {
                throw new ValidationException("MemberId không được để trống");
            }

            // Tìm kiếm vị thế theo các tiêu chí
            Page<Position> positionsPage = positionPersistencePort.findByMemberIdAndStatusAndTimeRange(
                    memberId, status, startTime, endTime, page, size);

            if (positionsPage.isEmpty()) {
                log.debug("Không tìm thấy vị thế nào, memberId = {}", memberId);
                return com.icetea.lotus.core.domain.valueobject.Page.empty();
            }

            // Lấy giá hiện tại cho tất cả các symbol
            Map<String, Money> currentPrices = new HashMap<>();
            Map<String, Money> indexPrices = new HashMap<>();
            Map<String, Contract> contracts = new HashMap<>();

            for (Position position : positionsPage.getContent()) {
                String symbol = position.getSymbol().getValue();
                if (!currentPrices.containsKey(symbol)) {
                    currentPrices.put(symbol, priceManagementService.getCurrentMarkPrice(Symbol.of(symbol)));
                    indexPrices.put(symbol, priceManagementService.getCurrentIndexPrice(Symbol.of(symbol)));

                    // Lấy thông tin hợp đồng
                    Optional<Contract> contractOpt = contractRepository.findBySymbolOptional(Symbol.of(symbol));
                    if (contractOpt.isPresent()) {
                        contracts.put(symbol, contractOpt.get());
                    }
                }
            }

            // Chuyển đổi sang DTO
            List<PositionDto> positionDtos = positionsPage.getContent().stream()
                    .map(position -> {
                        String symbol = position.getSymbol().getValue();
                        Money currentPrice = currentPrices.get(symbol);
                        Money indexPrice = indexPrices.get(symbol);
                        Contract contract = contracts.get(symbol);

                        Money unrealizedProfit = positionService.calculateUnrealizedProfit(position, currentPrice);
                        BigDecimal profitRatio = positionService.calculateProfitRatio(position, currentPrice);

                        PositionDto positionDto = positionMapper.toDto(position);
                        positionDto.setUnrealizedProfit(unrealizedProfit.getValue());
                        positionDto.setProfitRatio(profitRatio);
                        positionDto.setMarkPrice(currentPrice.getValue());
                        positionDto.setIndexPrice(indexPrice.getValue());

                        // Tính toán break even price và margin ratio nếu có hợp đồng
                        if (contract != null) {
                            Money breakEvenPrice = position.calculateBreakEvenPrice(contract.getTakerFeeRate().getValue());
                            positionDto.setBreakEvenPrice(breakEvenPrice.getValue());

                            BigDecimal marginRatio = position.calculateMarginRatio(currentPrice, contract.getMaintenanceMarginRate().getValue());
                            positionDto.setMarginRatio(marginRatio);
                        } else {
                            log.warn("Không tìm thấy hợp đồng cho symbol = {}, không thể tính break even price", symbol);
                            positionDto.setBreakEvenPrice(position.getOpenPrice().getValue()); // Fallback to entry price
                            positionDto.setMarginRatio(BigDecimal.ZERO);
                        }

                        return positionDto;
                    })
                    .toList();

            // Tạo Page mới với các DTO sử dụng lớp Page tùy chỉnh thay vì PageImpl
            com.icetea.lotus.core.domain.valueobject.Page<PositionDto> result =
                com.icetea.lotus.core.domain.valueobject.Page.<PositionDto>builder()
                    .content(new ArrayList<>(positionDtos))
                    .totalElements(positionsPage.getTotalElements())
                    .totalPages(positionsPage.getTotalPages())
                    .number(positionsPage.getNumber())
                    .size(positionsPage.getSize())
                    .build();

            log.debug("Đã tìm thấy {} vị thế, tổng số: {}, memberId = {}",
                    result.getContent().size(), result.getTotalElements(), memberId);

            return result;
        } catch (ValidationException e) {
            log.warn("Lỗi xác thực khi tìm kiếm vị thế: {}", e.getMessage());
            throw e;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm kiếm vị thế, memberId = {}", memberId, e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm kiếm vị thế, memberId = {}", memberId, e);
            throw new DatabaseException("Lỗi khi tìm kiếm vị thế: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tìm kiếm vị thế thất bại
     * @param e Ngoại lệ
     * @param memberId ID của thành viên
     * @param status Trạng thái của vị thế
     * @param startTime Thời gian bắt đầu
     * @param endTime Thời gian kết thúc
     * @param page Số trang
     * @param size Kích thước trang
     * @return Page<PositionDto>
     */
    @Recover
    public com.icetea.lotus.core.domain.valueobject.Page<PositionDto> recoverFindPositions(Exception e, Long memberId, PositionStatus status,
            LocalDateTime startTime, LocalDateTime endTime, int page, int size) {
        log.error("Đã thử lại tìm kiếm vị thế 3 lần nhưng thất bại, memberId = {}", memberId, e);
        return com.icetea.lotus.core.domain.valueobject.Page.empty();
    }
}
