package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.MarginMode;
import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.PositionId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.persistence.entity.PositionJpaEntity;
import com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * Mapper cho Position và PositionJpaEntity
 * Chuyển đổi giữa domain entity và JPA entity
 */
@Component
public class PositionPersistenceMapper {

    /**
     * Chuyển đổi từ JPA entity sang domain entity
     * @param entity JPA entity
     * @return Domain entity
     */
    public Position entityToDomain(PositionJpaEntity entity) {
        if (entity == null) {
            return null;
        }

        return Position.builder()
                .id(PositionId.of(entity.getId()))
                .memberId(entity.getMemberId())
                .symbol(Symbol.of(entity.getSymbol()))
                .direction(entity.getDirection())
                .volume(entity.getVolume())
                .openPrice(Money.of(entity.getOpenPrice()))
                .closePrice(entity.getClosePrice() != null ? Money.of(entity.getClosePrice()) : null)
                .liquidationPrice(entity.getLiquidationPrice() != null ? Money.of(entity.getLiquidationPrice()) : null)
                .maintenanceMargin(entity.getMaintenanceMargin() != null ? Money.of(entity.getMaintenanceMargin()) : null)
                .margin(entity.getMargin() != null ? Money.of(entity.getMargin()) : null)
                .profit(entity.getProfit() != null ? Money.of(entity.getProfit()) : null)
                .marginMode(entity.getMarginMode())
                .leverage(entity.getLeverage())
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .remark(entity.getRemark())
                .status(entity.getStatus())
                .build();
    }

    /**
     * Chuyển đổi từ domain entity sang JPA entity
     * @param domain Domain entity
     * @return JPA entity
     */
    public PositionJpaEntity domainToEntity(Position domain) {
        if (domain == null) {
            return null;
        }

        return PositionJpaEntity.builder()
                .id(domain.getId() != null ? domain.getId().getValue() : null)
                .memberId(domain.getMemberId())
                .symbol(domain.getSymbol().getValue())
                .direction(domain.getDirection())
                .volume(BigDecimalValidator.validateAndScale(domain.getVolume(), "volume"))
                .openPrice(BigDecimalValidator.validateAndScale(domain.getOpenPrice().getValue(), "openPrice"))
                .closePrice(domain.getClosePrice() != null ? BigDecimalValidator.validateAndScale(domain.getClosePrice().getValue(), "closePrice") : null)
                .liquidationPrice(domain.getLiquidationPrice() != null ? BigDecimalValidator.validateAndScale(domain.getLiquidationPrice().getValue(), "liquidationPrice") : null)
                .maintenanceMargin(domain.getMaintenanceMargin() != null ? BigDecimalValidator.validateAndScale(domain.getMaintenanceMargin().getValue(), "maintenanceMargin") : BigDecimal.ZERO)
                .margin(domain.getMargin() != null ? BigDecimalValidator.validateAndScale(domain.getMargin().getValue(), "margin") : null)
                .profit(domain.getProfit() != null ? BigDecimalValidator.validateAndScale(domain.getProfit().getValue(), "profit") : null)
                .marginMode(domain.getMarginMode() != null ? domain.getMarginMode() : MarginMode.UNKNOWN)
                .leverage(domain.getLeverage() != null ? domain.getLeverage() : BigDecimal.ZERO)
                .createTime(domain.getCreateTime())
                .updateTime(domain.getUpdateTime())
                .remark(domain.getRemark())
                .status(domain.getStatus())
                .build();
    }
}
