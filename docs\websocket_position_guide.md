# Hướng Dẫn Sử Dụng WebSocket Position

## 1. Tổng Quan

WebSocket Position cung cấp real-time updates về trạng thái vị thế của người dùng, bao gồm:
- C<PERSON><PERSON> nhật vị thế mới
- Thay đổi vị thế hiện tại
- <PERSON><PERSON><PERSON> vị thế
- <PERSON>h lý vị thế
- C<PERSON><PERSON> nhật PnL (Profit and Loss)

## 2. Kết Nối WebSocket

### 2.1. Endpoint
```
ws://localhost:8080/ws
```

### 2.2. Authentication
Sử dụng JWT token trong header hoặc query parameter:
```javascript
const socket = new WebSocket('ws://localhost:8080/ws?token=YOUR_JWT_TOKEN');
```

### 2.3. STOMP Protocol
Sử dụng STOMP protocol để subscribe và gửi messages:
```javascript
const stompClient = new StompJs.Client({
    brokerURL: 'ws://localhost:8080/ws',
    connectHeaders: {
        'Authorization': 'Bearer YOUR_JWT_TOKEN'
    }
});
```

## 3. Subscription Topics

### 3.1. Position Updates cho User Cụ Thể
```javascript
// Subscribe to all position updates for current user
stompClient.subscribe('/user/position/updates', function(message) {
    const positionUpdate = JSON.parse(message.body);
    console.log('Position Update:', positionUpdate);
});
```

### 3.2. Position Updates cho Symbol Cụ Thể
```javascript
// Subscribe to position updates for specific symbol
stompClient.subscribe('/user/position/updates/BTC/USDT', function(message) {
    const positionUpdate = JSON.parse(message.body);
    console.log('BTC/USDT Position Update:', positionUpdate);
});
```

### 3.3. Position Closed Events
```javascript
// Subscribe to position closed events
stompClient.subscribe('/user/position/closed/BTC/USDT', function(message) {
    const closedPosition = JSON.parse(message.body);
    console.log('Position Closed:', closedPosition);
});
```

### 3.4. Position Liquidated Events
```javascript
// Subscribe to position liquidated events
stompClient.subscribe('/user/position/liquidated/BTC/USDT', function(message) {
    const liquidatedPosition = JSON.parse(message.body);
    console.log('Position Liquidated:', liquidatedPosition);
});
```

### 3.5. Public Position Topics (Optional)
```javascript
// Subscribe to public position events for specific symbol and user
stompClient.subscribe('/topic/position/updates/BTC/USDT/USER_ID', function(message) {
    const positionUpdate = JSON.parse(message.body);
    console.log('Public Position Update:', positionUpdate);
});
```

## 4. Message Formats

### 4.1. Position Update Message
```json
{
    "id": "123456789",
    "memberId": 601152,
    "symbol": "BTC/USDT",
    "direction": "LONG",
    "volume": "1.5",
    "openPrice": "45000.00",
    "closePrice": null,
    "liquidationPrice": "40500.00",
    "maintenanceMargin": "450.00",
    "margin": "9000.00",
    "unrealizedPnl": "1500.00",
    "realizedPnl": "0.00",
    "marginMode": "ISOLATED",
    "leverage": "5.0",
    "status": "OPEN",
    "createTime": "2025-01-27T10:30:00",
    "updateTime": "2025-01-27T10:35:00",
    "eventType": "POSITION_UPDATED",
    "timestamp": 1706347500000
}
```

### 4.2. Position Closed Message
```json
{
    "id": "123456789",
    "memberId": 601152,
    "symbol": "BTC/USDT",
    "direction": "LONG",
    "volume": "0.0",
    "openPrice": "45000.00",
    "closePrice": "47000.00",
    "liquidationPrice": "0.00",
    "maintenanceMargin": "0.00",
    "margin": "0.00",
    "unrealizedPnl": "0.00",
    "realizedPnl": "3000.00",
    "marginMode": "ISOLATED",
    "leverage": "5.0",
    "status": "CLOSED",
    "createTime": "2025-01-27T10:30:00",
    "updateTime": "2025-01-27T11:00:00",
    "eventType": "POSITION_CLOSED",
    "timestamp": 1706349600000,
    "closeReason": "USER_CLOSE"
}
```

### 4.3. Position Liquidated Message
```json
{
    "id": "123456789",
    "memberId": 601152,
    "symbol": "BTC/USDT",
    "direction": "LONG",
    "volume": "0.0",
    "openPrice": "45000.00",
    "closePrice": "40500.00",
    "liquidationPrice": "40500.00",
    "maintenanceMargin": "0.00",
    "margin": "0.00",
    "unrealizedPnl": "0.00",
    "realizedPnl": "-6750.00",
    "remainingMargin": "-750.00",
    "marginMode": "ISOLATED",
    "leverage": "5.0",
    "status": "LIQUIDATED",
    "createTime": "2025-01-27T10:30:00",
    "updateTime": "2025-01-27T11:15:00",
    "eventType": "POSITION_LIQUIDATED",
    "timestamp": 1706350500000,
    "liquidationType": "INSURANCE_FUND",
    "liquidationReason": "Margin ratio below maintenance margin"
}
```

## 5. JavaScript Client Implementation

### 5.1. Complete Client Setup
```javascript
class PositionWebSocketClient {
    constructor(token, userId) {
        this.token = token;
        this.userId = userId;
        this.stompClient = null;
        this.subscriptions = new Map();
    }

    connect() {
        this.stompClient = new StompJs.Client({
            brokerURL: 'ws://localhost:8080/ws',
            connectHeaders: {
                'Authorization': `Bearer ${this.token}`
            },
            debug: function (str) {
                console.log('STOMP Debug:', str);
            },
            reconnectDelay: 5000,
            heartbeatIncoming: 4000,
            heartbeatOutgoing: 4000
        });

        this.stompClient.onConnect = (frame) => {
            console.log('Connected to Position WebSocket:', frame);
            this.subscribeToPositionUpdates();
        };

        this.stompClient.onStompError = (frame) => {
            console.error('STOMP Error:', frame);
        };

        this.stompClient.onWebSocketError = (error) => {
            console.error('WebSocket Error:', error);
        };

        this.stompClient.activate();
    }

    subscribeToPositionUpdates() {
        // Subscribe to all position updates
        const allPositionsSubscription = this.stompClient.subscribe('/user/position/updates',
            (message) => this.handlePositionUpdate(JSON.parse(message.body))
        );
        this.subscriptions.set('all-positions', allPositionsSubscription);

        // Subscribe to position closed events
        const closedPositionsSubscription = this.stompClient.subscribe('/user/position/closed',
            (message) => this.handlePositionClosed(JSON.parse(message.body))
        );
        this.subscriptions.set('closed-positions', closedPositionsSubscription);

        // Subscribe to position liquidated events
        const liquidatedPositionsSubscription = this.stompClient.subscribe('/user/position/liquidated',
            (message) => this.handlePositionLiquidated(JSON.parse(message.body))
        );
        this.subscriptions.set('liquidated-positions', liquidatedPositionsSubscription);
    }

    subscribeToSymbol(symbol) {
        const topic = `/user/position/updates/${symbol}`;
        if (!this.subscriptions.has(symbol)) {
            const subscription = this.stompClient.subscribe(topic,
                (message) => this.handleSymbolPositionUpdate(symbol, JSON.parse(message.body))
            );
            this.subscriptions.set(symbol, subscription);
            console.log(`Subscribed to position updates for ${symbol}`);
        }
    }

    unsubscribeFromSymbol(symbol) {
        if (this.subscriptions.has(symbol)) {
            this.subscriptions.get(symbol).unsubscribe();
            this.subscriptions.delete(symbol);
            console.log(`Unsubscribed from position updates for ${symbol}`);
        }
    }

    handlePositionUpdate(positionData) {
        console.log('Position Update Received:', positionData);

        // Update UI with new position data
        this.updatePositionInUI(positionData);

        // Trigger custom events
        this.dispatchEvent('positionUpdate', positionData);
    }

    handlePositionClosed(positionData) {
        console.log('Position Closed:', positionData);

        // Remove position from UI
        this.removePositionFromUI(positionData.id);

        // Show notification
        this.showNotification(`Position ${positionData.symbol} closed with PnL: ${positionData.realizedPnl}`);

        // Trigger custom events
        this.dispatchEvent('positionClosed', positionData);
    }

    handlePositionLiquidated(positionData) {
        console.log('Position Liquidated:', positionData);

        // Remove position from UI
        this.removePositionFromUI(positionData.id);

        // Show liquidation notification
        this.showNotification(`Position ${positionData.symbol} liquidated! Loss: ${positionData.realizedPnl}`, 'error');

        // Trigger custom events
        this.dispatchEvent('positionLiquidated', positionData);
    }

    handleSymbolPositionUpdate(symbol, positionData) {
        console.log(`${symbol} Position Update:`, positionData);
        this.updateSymbolPositionInUI(symbol, positionData);
    }

    updatePositionInUI(positionData) {
        // Implementation depends on your UI framework
        const positionElement = document.getElementById(`position-${positionData.id}`);
        if (positionElement) {
            // Update existing position
            this.updatePositionElement(positionElement, positionData);
        } else {
            // Create new position element
            this.createPositionElement(positionData);
        }
    }

    removePositionFromUI(positionId) {
        const positionElement = document.getElementById(`position-${positionId}`);
        if (positionElement) {
            positionElement.remove();
        }
    }

    showNotification(message, type = 'info') {
        // Implementation depends on your notification system
        console.log(`[${type.toUpperCase()}] ${message}`);
    }

    dispatchEvent(eventType, data) {
        const event = new CustomEvent(`position${eventType}`, { detail: data });
        document.dispatchEvent(event);
    }

    disconnect() {
        if (this.stompClient) {
            this.stompClient.deactivate();
        }
    }
}
```

### 5.2. Usage Example
```javascript
// Initialize client
const positionClient = new PositionWebSocketClient('your-jwt-token', 'user-id');

// Connect to WebSocket
positionClient.connect();

// Subscribe to specific symbols
positionClient.subscribeToSymbol('BTC/USDT');
positionClient.subscribeToSymbol('ETH/USDT');

// Listen to custom events
document.addEventListener('positionpositionUpdate', (event) => {
    console.log('Custom Position Update Event:', event.detail);
});

document.addEventListener('positionpositionClosed', (event) => {
    console.log('Custom Position Closed Event:', event.detail);
});

document.addEventListener('positionpositionLiquidated', (event) => {
    console.log('Custom Position Liquidated Event:', event.detail);
    // Handle liquidation logic
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    positionClient.disconnect();
});
```

## 6. Error Handling

### 6.1. Connection Errors
```javascript
this.stompClient.onWebSocketError = (error) => {
    console.error('WebSocket connection error:', error);
    // Implement retry logic
    setTimeout(() => {
        this.connect();
    }, 5000);
};
```

### 6.2. Authentication Errors
```javascript
this.stompClient.onStompError = (frame) => {
    console.error('STOMP protocol error:', frame);
    if (frame.headers.message.includes('Authentication')) {
        // Refresh token and reconnect
        this.refreshTokenAndReconnect();
    }
};
```

## 7. Best Practices

### 7.1. Performance Optimization
- Chỉ subscribe các symbol đang quan tâm
- Unsubscribe khi không cần thiết
- Sử dụng throttling cho UI updates
- Implement connection pooling cho multiple symbols

### 7.2. Error Recovery
- Implement automatic reconnection
- Handle token expiration
- Graceful degradation khi WebSocket unavailable
- Fallback to REST API polling

### 7.3. Security
- Validate tất cả incoming messages
- Sanitize data trước khi hiển thị
- Implement rate limiting
- Use secure WebSocket (WSS) trong production

## 8. Testing

### 8.1. Unit Testing
```javascript
// Test WebSocket client functionality
describe('PositionWebSocketClient', () => {
    it('should connect and subscribe to position updates', () => {
        // Test implementation
    });

    it('should handle position update messages correctly', () => {
        // Test implementation
    });
});
```

### 8.2. Integration Testing
- Test với real WebSocket server
- Verify message formats
- Test error scenarios
- Performance testing với high message volume

## 9. Advanced Features

### 9.1. Position Aggregation
```javascript
class PositionAggregator {
    constructor() {
        this.positions = new Map();
        this.totalPnl = 0;
        this.totalMargin = 0;
    }

    updatePosition(positionData) {
        const key = `${positionData.symbol}_${positionData.direction}`;
        this.positions.set(key, positionData);
        this.recalculateAggregates();
    }

    removePosition(positionId) {
        for (let [key, position] of this.positions) {
            if (position.id === positionId) {
                this.positions.delete(key);
                break;
            }
        }
        this.recalculateAggregates();
    }

    recalculateAggregates() {
        this.totalPnl = 0;
        this.totalMargin = 0;

        for (let position of this.positions.values()) {
            this.totalPnl += parseFloat(position.unrealizedPnl || 0);
            this.totalMargin += parseFloat(position.margin || 0);
        }
    }

    getPositionsBySymbol(symbol) {
        return Array.from(this.positions.values())
            .filter(p => p.symbol === symbol);
    }

    getAllPositions() {
        return Array.from(this.positions.values());
    }
}
```

### 9.2. Real-time PnL Calculation
```javascript
class PnLCalculator {
    constructor(priceService) {
        this.priceService = priceService;
        this.positions = new Map();
    }

    updatePosition(positionData) {
        this.positions.set(positionData.id, positionData);
    }

    async calculateRealTimePnL() {
        const results = new Map();

        for (let [id, position] of this.positions) {
            if (position.status === 'OPEN') {
                const currentPrice = await this.priceService.getCurrentPrice(position.symbol);
                const unrealizedPnl = this.calculateUnrealizedPnL(position, currentPrice);

                results.set(id, {
                    ...position,
                    currentPrice: currentPrice,
                    unrealizedPnl: unrealizedPnl,
                    lastUpdated: new Date().toISOString()
                });
            }
        }

        return results;
    }

    calculateUnrealizedPnL(position, currentPrice) {
        const openPrice = parseFloat(position.openPrice);
        const volume = parseFloat(position.volume);
        const leverage = parseFloat(position.leverage);

        let priceDiff;
        if (position.direction === 'LONG') {
            priceDiff = currentPrice - openPrice;
        } else {
            priceDiff = openPrice - currentPrice;
        }

        return priceDiff * volume * leverage;
    }
}
```

### 9.3. Position Risk Management
```javascript
class PositionRiskManager {
    constructor(riskLimits) {
        this.riskLimits = riskLimits;
        this.alerts = [];
    }

    checkPositionRisk(positionData) {
        const risks = [];

        // Check margin ratio
        const marginRatio = this.calculateMarginRatio(positionData);
        if (marginRatio < this.riskLimits.minMarginRatio) {
            risks.push({
                type: 'LOW_MARGIN',
                severity: 'HIGH',
                message: `Margin ratio ${marginRatio}% below minimum ${this.riskLimits.minMarginRatio}%`,
                position: positionData
            });
        }

        // Check position size
        const positionValue = parseFloat(positionData.volume) * parseFloat(positionData.openPrice);
        if (positionValue > this.riskLimits.maxPositionValue) {
            risks.push({
                type: 'LARGE_POSITION',
                severity: 'MEDIUM',
                message: `Position value ${positionValue} exceeds limit ${this.riskLimits.maxPositionValue}`,
                position: positionData
            });
        }

        // Check unrealized loss
        const unrealizedPnl = parseFloat(positionData.unrealizedPnl || 0);
        if (unrealizedPnl < -this.riskLimits.maxUnrealizedLoss) {
            risks.push({
                type: 'HIGH_LOSS',
                severity: 'HIGH',
                message: `Unrealized loss ${unrealizedPnl} exceeds limit ${this.riskLimits.maxUnrealizedLoss}`,
                position: positionData
            });
        }

        return risks;
    }

    calculateMarginRatio(positionData) {
        const margin = parseFloat(positionData.margin);
        const unrealizedPnl = parseFloat(positionData.unrealizedPnl || 0);
        const positionValue = parseFloat(positionData.volume) * parseFloat(positionData.openPrice);

        return ((margin + unrealizedPnl) / positionValue) * 100;
    }
}
```

## 10. Configuration Examples

### 10.1. Environment Configuration
```javascript
const config = {
    development: {
        wsUrl: 'ws://localhost:8080/ws',
        reconnectDelay: 5000,
        heartbeatInterval: 4000,
        debug: true
    },
    production: {
        wsUrl: 'wss://api.yourexchange.com/ws',
        reconnectDelay: 10000,
        heartbeatInterval: 30000,
        debug: false
    }
};
```

### 10.2. Risk Management Configuration
```javascript
const riskLimits = {
    minMarginRatio: 10, // 10%
    maxPositionValue: 100000, // $100,000
    maxUnrealizedLoss: 5000, // $5,000
    maxPositionsPerSymbol: 5,
    maxTotalExposure: 500000 // $500,000
};
```

## 11. Troubleshooting

### 11.1. Common Issues

#### Connection Problems
```javascript
// Check WebSocket support
if (!window.WebSocket) {
    console.error('WebSocket not supported');
    // Fallback to polling
}

// Check network connectivity
navigator.onLine ? console.log('Online') : console.log('Offline');
```

#### Message Parsing Errors
```javascript
try {
    const data = JSON.parse(message.body);
    this.handlePositionUpdate(data);
} catch (error) {
    console.error('Failed to parse message:', error);
    console.error('Raw message:', message.body);
}
```

#### Authentication Issues
```javascript
// Token refresh logic
async refreshToken() {
    try {
        const response = await fetch('/api/auth/refresh', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.refreshToken}`
            }
        });
        const data = await response.json();
        this.token = data.accessToken;
        return true;
    } catch (error) {
        console.error('Token refresh failed:', error);
        return false;
    }
}
```

### 11.2. Debug Tools
```javascript
// Enable debug logging
const debugLogger = {
    log: (message, data) => {
        if (config.debug) {
            console.log(`[Position WS] ${message}`, data);
        }
    },
    error: (message, error) => {
        console.error(`[Position WS Error] ${message}`, error);
    }
};
```

## 12. Performance Monitoring

### 12.1. Metrics Collection
```javascript
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            messagesReceived: 0,
            messagesPerSecond: 0,
            connectionUptime: 0,
            reconnections: 0,
            lastMessageTime: null
        };
        this.startTime = Date.now();
    }

    recordMessage() {
        this.metrics.messagesReceived++;
        this.metrics.lastMessageTime = Date.now();
        this.calculateMessagesPerSecond();
    }

    calculateMessagesPerSecond() {
        const uptime = (Date.now() - this.startTime) / 1000;
        this.metrics.messagesPerSecond = this.metrics.messagesReceived / uptime;
    }

    recordReconnection() {
        this.metrics.reconnections++;
    }

    getMetrics() {
        this.metrics.connectionUptime = Date.now() - this.startTime;
        return { ...this.metrics };
    }
}
```
