server.port=7002
spring.application.name=service-rpc-usdt
#kafka
# \u00E6\u008C\u0087\u00E5\u00AE\u009Akafka \u00E4\u00BB\u00A3\u00E7\u0090\u0086\u00E5\u009C\u00B0\u00E5\u009D\u0080\u00EF\u00BC\u008C\u00E5\u008F\u00AF\u00E4\u00BB\u00A5\u00E5\u00A4\u009A\u00E4\u00B8\u00AA
spring.kafka.bootstrap-servers=***************:9092
# \u00E6\u008C\u0087\u00E5\u00AE\u009A\u00E9\u00BB\u0098\u00E8\u00AE\u00A4\u00E6\u00B6\u0088\u00E8\u00B4\u00B9\u00E8\u0080\u0085group id
spring.kafka.consumer.group-id=default-group
# \u00E6\u008C\u0087\u00E5\u00AE\u009A\u00E9\u00BB\u0098\u00E8\u00AE\u00A4topic id
spring.kafka.template.default-topic= test
# \u00E6\u008C\u0087\u00E5\u00AE\u009Alistener \u00E5\u00AE\u00B9\u00E5\u0099\u00A8\u00E4\u00B8\u00AD\u00E7\u009A\u0084\u00E7\u00BA\u00BF\u00E7\u00A8\u008B\u00E6\u0095\u00B0\u00EF\u00BC\u008C\u00E7\u0094\u00A8\u00E4\u00BA\u008E\u00E6\u008F\u0090\u00E9\u00AB\u0098\u00E5\u00B9\u00B6\u00E5\u008F\u0091\u00E9\u0087\u008F
spring.kafka.listener.concurrency= 3
# \u00E6\u00AF\u008F\u00E6\u00AC\u00A1\u00E6\u0089\u00B9\u00E9\u0087\u008F\u00E5\u008F\u0091\u00E9\u0080\u0081\u00E6\u00B6\u0088\u00E6\u0081\u00AF\u00E7\u009A\u0084\u00E6\u0095\u00B0\u00E9\u0087\u008F
spring.kafka.producer.batch-size= 1000
# mongodb
spring.data.mongodb.uri=**********************************************************

eureka.client.serviceUrl.defaultZone=http://***************:7000/eureka/
# \u00E6\u00B3\u00A8\u00E5\u0086\u008C\u00E6\u0097\u00B6\u00E4\u00BD\u00BF\u00E7\u0094\u00A8ip\u00E8\u0080\u008C\u00E4\u00B8\u008D\u00E6\u0098\u00AF\u00E4\u00B8\u00BB\u00E6\u009C\u00BA\u00E5\u0090\u008D
eureka.instance.prefer-ip-address=true

coin.rpc=************************************/
coin.name=USDT
coin.unit=USDT
coin.password=123456
coin.step=10
coin.init-block-height=592417
coin.withdraw-address=*********************************
#\u00E8\u00BD\u00AC\u00E8\u00B4\u00A6\u00E6\u0097\u00B6\u00E9\u00BB\u0098\u00E8\u00AE\u00A4\u00E6\u0094\u00AF\u00E4\u00BB\u0098\u00E7\u009A\u0084btc\u00E6\u0089\u008B\u00E7\u00BB\u00AD\u00E8\u00B4\u00B9
coin.default-miner-fee=0.0001
#\u00E6\u00AF\u008F\u00E6\u00AC\u00A1\u00E7\u00BB\u0099USDT\u00E5\u009C\u00B0\u00E5\u009D\u0080\u00E5\u0085\u0085\u00E5\u0080\u00BC\u00E7\u009A\u0084btc\u00E6\u0095\u00B0\u00E9\u0087\u008F
coin.recharge-miner-fee=0.0001
coin.min-collect-amount=1
