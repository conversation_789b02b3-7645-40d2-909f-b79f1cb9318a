package com.icetea.lotus.repository;

import com.icetea.lotus.entity.Coin;
import com.icetea.lotus.entity.WalletFuture;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface WalletFutureRepository extends JpaRepository<WalletFuture, Long> {

    @Query("SELECT w FROM WalletFuture w WHERE w.coin = :coin AND w.memberId = :id")
    WalletFuture findByCoinAndMemberId(@Param("coin") String coin, @Param("id") long id);
}
