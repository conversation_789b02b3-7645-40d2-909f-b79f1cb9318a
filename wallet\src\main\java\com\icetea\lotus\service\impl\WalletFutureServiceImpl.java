package com.icetea.lotus.service.impl;

import com.icetea.lotus.constants.TransferEnum;
import com.icetea.lotus.dao.CoinDao;
import com.icetea.lotus.dao.MemberWalletDao;
import com.icetea.lotus.dto.WalletDto;
import com.icetea.lotus.dto.request.TransferRequestDto;
import com.icetea.lotus.entity.Coin;
import com.icetea.lotus.entity.MemberWallet;
import com.icetea.lotus.entity.WalletFuture;
import com.icetea.lotus.entity.transform.AuthMember;
import com.icetea.lotus.repository.WalletFutureRepository;
import com.icetea.lotus.service.WalletFutureService;
import com.icetea.lotus.utils.mapper.WalletDtoMapper;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Domain service implementation cho Wallet
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WalletFutureServiceImpl implements WalletFutureService {
    private final MemberWalletDao memberWalletDao;
    private final WalletFutureRepository walletFutureRepository;
    private final WalletDtoMapper walletDtoMapper;
    private final CoinDao coinDao;

    @Override
    @Transactional(rollbackOn = Exception.class)
    public WalletDto transferWalletFuture(TransferRequestDto request) {
        Coin coin = coinDao.findByUnit(request.getCoin());
        if(coin == null){
            throw new IllegalArgumentException("Coin unit not found");
        }
        MemberWallet spotWallet = memberWalletDao.findByCoinAndMemberId(coin, request.getMemberId());
        if(spotWallet.getIsLock().isIs()){
            throw new IllegalArgumentException("Member wallet is locked");
        }
        if(spotWallet == null ){
            throw new IllegalArgumentException("Member wallet not found");
        }
        if(TransferEnum.SPOT_TO_FUTURE.equals(TransferEnum.getTransferEnumByType(request.getTransferType()))){
            if(spotWallet.getBalance().compareTo(request.getAmount()) < 0){
                throw new IllegalArgumentException("Số dư không đủ");
            }
            memberWalletDao.decreaseBalance(spotWallet.getId(), request.getAmount());
        }else{
            memberWalletDao.increaseBalance(spotWallet.getId(), request.getAmount());
        }
        return buildResponse(spotWallet);
    }

    private WalletDto buildResponse(MemberWallet spotWallet) {
        return WalletDto.builder()
                .id(spotWallet.getId())
                .isLock(spotWallet.getIsLock().isIs())
                .availableBalance(spotWallet.getBalance())
                .balance(spotWallet.getBalance())
                .coin(spotWallet.getCoin().getUnit())
                .frozenBalance(spotWallet.getFrozenBalance())
                .memberId(spotWallet.getMemberId())
                .build();
    }


}
