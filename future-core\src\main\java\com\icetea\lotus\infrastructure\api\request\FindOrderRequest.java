package com.icetea.lotus.infrastructure.api.request;

import com.icetea.lotus.core.domain.entity.OrderStatus;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Builder
@Setter
@Getter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class FindOrderRequest {
    @NotBlank(message = "Symbol không để trống")
    private String symbol;
    private Integer pageNo;
    private Integer pageSize;
    private List<OrderStatus> status;

    public void sanitize() {
        if (pageNo == null) {
            pageNo = 0;
        }
        if (pageSize == null) {
            pageSize = 20;
        }
    }

}
