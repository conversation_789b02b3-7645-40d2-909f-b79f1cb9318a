package com.icetea.lotus.controller;

import com.icetea.lotus.service.TestService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.LinkedHashMap;

@Slf4j
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
public class TestController extends BaseController {

    private final TestService testService;

    /**
     * Retrieve the current block height for the specified currency unit.
     *
     * @param unit Currency unit, e.g., BTC, ETH, etc.
     * @return MessageResult with the result.
     */
    @GetMapping("/height/{unit}")
    public MessageResult getBlockHeight(@PathVariable("unit") String unit) {
        log.info("Retrieving block height for unit: {}", unit);
        ResponseEntity<MessageResult> result = testService.getBlockHeight(unit);
        return success(result);
    }

    /**
     * Check whether the RPC services for all coins are functioning properly.
     *
     * @return MessageResult with the result.
     */
    @GetMapping("/rpc")
    public MessageResult checkRpcServices() {
        log.info("Checking RPC services for all coins");
        LinkedHashMap<String, ResponseEntity<MessageResult>> result = testService.checkRpcServices();
        return success(result);
    }
}