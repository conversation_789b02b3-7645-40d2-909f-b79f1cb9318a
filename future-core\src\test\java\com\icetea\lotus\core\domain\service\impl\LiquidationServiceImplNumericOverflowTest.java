package com.icetea.lotus.core.domain.service.impl;

import com.icetea.lotus.core.domain.entity.Contract;
import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.PositionDirection;
import com.icetea.lotus.core.domain.entity.PositionStatus;
import com.icetea.lotus.core.domain.repository.FinancialRecordRepository;
import com.icetea.lotus.core.domain.service.AccountService;
import com.icetea.lotus.core.domain.service.PositionService;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.PositionId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.external.LiquidationEventProducer;
import com.icetea.lotus.infrastructure.util.SnowflakeIdGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Test cho LiquidationServiceImpl với focus vào numeric overflow issues
 */
@ExtendWith(MockitoExtension.class)
class LiquidationServiceImplNumericOverflowTest {

    @Mock
    private PositionService positionService;

    @Mock
    private AccountService accountService;

    @Mock
    private FinancialRecordRepository financialRecordRepository;

    @Mock
    private LiquidationEventProducer liquidationEventProducer;

    @Mock
    private SnowflakeIdGenerator snowflakeIdGenerator;

    private LiquidationServiceImpl liquidationService;

    @BeforeEach
    void setUp() {
        liquidationService = new LiquidationServiceImpl(
                positionService,
                accountService,
                financialRecordRepository,
                liquidationEventProducer,
                snowflakeIdGenerator
        );
    }

    @Test
    @DisplayName("Test liquidation với giá trị lớn không gây numeric overflow")
    void testLiquidationWithLargeValuesNoOverflow() {
        // Arrange - Tạo position với giá trị lớn
        Position position = createLargeValuePosition();
        Money liquidationPrice = Money.of(new BigDecimal("100000.********")); // Giá thanh lý cao
        Contract contract = createContract();

        // Mock dependencies
        when(snowflakeIdGenerator.nextIdWithPrefix("FR")).thenReturn("FR123456789");
        when(positionService.calculateUnrealizedProfit(any(), any()))
                .thenReturn(Money.of(new BigDecimal("5********0.********"))); // Profit lớn
        when(financialRecordRepository.save(any())).thenReturn(null);

        // Act & Assert - Không throw exception
        assertDoesNotThrow(() -> {
            Position result = liquidationService.liquidatePosition(position, liquidationPrice, contract);
            assertNotNull(result);
            assertEquals(PositionStatus.LIQUIDATED, result.getStatus());
        });

        // Verify financial record được tạo
        verify(financialRecordRepository, times(1)).save(any());
    }

    @Test
    @DisplayName("Test tính toán phí thanh lý với giá trị lớn")
    void testCalculateLiquidationFeeWithLargeValues() {
        // Arrange
        Position position = createLargeValuePosition();
        Money liquidationPrice = Money.of(new BigDecimal("100000.********"));
        Contract contract = createContract();

        // Act
        Money liquidationFee = liquidationService.calculateLiquidationFee(position, liquidationPrice, contract);

        // Assert - Fee được tính toán và không overflow
        assertNotNull(liquidationFee);
        assertTrue(liquidationFee.getValue().compareTo(BigDecimal.ZERO) >= 0);
        // Fee không được vượt quá giới hạn NUMERIC(18,8)
        assertTrue(liquidationFee.getValue().compareTo(new BigDecimal("9999999999.99999999")) <= 0);
    }

    @Test
    @DisplayName("Test tính toán unrealized profit với giá trị lớn")
    void testCalculateUnrealizedProfitWithLargeValues() {
        // Arrange
        Position position = createLargeValuePosition();
        Money currentPrice = Money.of(new BigDecimal("150000.********")); // Giá cao hơn open price

        // Act
        Money unrealizedProfit = position.calculateUnrealizedProfit(currentPrice);

        // Assert
        assertNotNull(unrealizedProfit);
        // Profit không được vượt quá giới hạn NUMERIC(18,8)
        assertTrue(unrealizedProfit.getValue().compareTo(new BigDecimal("9999999999.99999999")) <= 0);
        assertTrue(unrealizedProfit.getValue().compareTo(new BigDecimal("-9999999999.99999999")) >= 0);
    }

    @Test
    @DisplayName("Test liquidation với profit âm lớn")
    void testLiquidationWithLargeNegativeProfit() {
        // Arrange
        Position position = createLargeValuePosition();
        Money liquidationPrice = Money.of(new BigDecimal("10000.********")); // Giá thấp hơn open price nhiều
        Contract contract = createContract();

        // Mock large negative profit
        when(positionService.calculateUnrealizedProfit(any(), any()))
                .thenReturn(Money.of(new BigDecimal("-8********0.********")));
        when(snowflakeIdGenerator.nextIdWithPrefix("FR")).thenReturn("FR123456789");
        when(financialRecordRepository.save(any())).thenReturn(null);

        // Act & Assert
        assertDoesNotThrow(() -> {
            Position result = liquidationService.liquidatePosition(position, liquidationPrice, contract);
            assertNotNull(result);
            assertEquals(PositionStatus.LIQUIDATED, result.getStatus());
        });
    }

    @Test
    @DisplayName("Test validation của financial amounts")
    void testFinancialAmountValidation() {
        // Arrange
        Position position = createLargeValuePosition();
        Money liquidationPrice = Money.of(new BigDecimal("50000.********"));
        Contract contract = createContract();

        // Mock extreme values
        when(positionService.calculateUnrealizedProfit(any(), any()))
                .thenReturn(Money.of(new BigDecimal("99999999999.99999999"))); // Vượt quá giới hạn
        when(snowflakeIdGenerator.nextIdWithPrefix("FR")).thenReturn("FR123456789");
        when(financialRecordRepository.save(any())).thenReturn(null);

        // Act & Assert - Không throw exception, values được cap
        assertDoesNotThrow(() -> {
            liquidationService.liquidatePosition(position, liquidationPrice, contract);
        });

        // Verify financial record vẫn được tạo với values đã được cap
        verify(financialRecordRepository, times(1)).save(any());
    }

    @Test
    @DisplayName("Test edge case với volume rất lớn")
    void testLiquidationWithVeryLargeVolume() {
        // Arrange
        Position position = Position.builder()
                .id(PositionId.of(486L))
                .memberId(601152L)
                .symbol(Symbol.of("BTC/USDT"))
                .status(PositionStatus.OPEN)
                .direction(PositionDirection.LONG)
                .volume(new BigDecimal("999999.99999999")) // Volume rất lớn
                .openPrice(Money.of(new BigDecimal("45000.********")))
                .margin(Money.of(new BigDecimal("9000000.********"))) // Margin lớn
                .leverage(new BigDecimal("5"))
                .createTime(LocalDateTime.now().minusHours(1))
                .updateTime(LocalDateTime.now())
                .build();

        Money liquidationPrice = Money.of(new BigDecimal("50000.********"));
        Contract contract = createContract();

        when(snowflakeIdGenerator.nextIdWithPrefix("FR")).thenReturn("FR123456789");
        when(positionService.calculateUnrealizedProfit(any(), any()))
                .thenReturn(Money.of(new BigDecimal("1000000.********")));
        when(financialRecordRepository.save(any())).thenReturn(null);

        // Act & Assert
        assertDoesNotThrow(() -> {
            Position result = liquidationService.liquidatePosition(position, liquidationPrice, contract);
            assertNotNull(result);
        });
    }

    /**
     * Tạo position với giá trị lớn để test
     */
    private Position createLargeValuePosition() {
        return Position.builder()
                .id(PositionId.of(486L))
                .memberId(601152L)
                .symbol(Symbol.of("BTC/USDT"))
                .status(PositionStatus.OPEN)
                .direction(PositionDirection.LONG)
                .volume(new BigDecimal("100000.********")) // Volume lớn
                .openPrice(Money.of(new BigDecimal("45000.********")))
                .margin(Money.of(new BigDecimal("900000.********"))) // Margin lớn
                .leverage(new BigDecimal("5"))
                .createTime(LocalDateTime.now().minusHours(1))
                .updateTime(LocalDateTime.now())
                .build();
    }

    /**
     * Tạo contract để test
     */
    private Contract createContract() {
        return Contract.builder()
                .symbol(Symbol.of("BTC/USDT"))
                .baseCoin("BTC")
                .quoteCoin("USDT")
                .enable(1)
                .visible(1)
                .sort(1)
                .enableOpenSell(1)
                .enableOpenBuy(1)
                .enableMarketSell(1)
                .enableMarketBuy(1)
                .maxTradingTime(24)
                .maxTradingUnit(new BigDecimal("1000"))
                .minTradingUnit(new BigDecimal("0.001"))
                .liquidationFeeRate(Money.of(new BigDecimal("0.005"))) // 0.5%
                .maintenanceMarginRatio(new BigDecimal("0.05")) // 5%
                .build();
    }
}
