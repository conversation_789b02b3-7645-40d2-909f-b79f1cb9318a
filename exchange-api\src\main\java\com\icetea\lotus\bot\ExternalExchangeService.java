package com.icetea.lotus.bot;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Service for fetching order book data from external crypto exchanges
 */
@Service
@Slf4j
public class ExternalExchangeService {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper = new ObjectMapper();

    public ExternalExchangeService(@Qualifier("externalRestTemplate") RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Value("${external-exchange.binance.api-base-url:https://api.binance.com}")
    private String binanceApiBaseUrl;

    @Value("${external-exchange.binance.order-book-endpoint:/api/v3/depth}")
    private String binanceOrderBookEndpoint;

    @Value("${external-exchange.binance.connect-timeout:5000}")
    private int connectTimeout;

    @Value("${external-exchange.binance.read-timeout:5000}")
    private int readTimeout;

    @Value("${external-exchange.binance.enabled:true}")
    private boolean binanceEnabled;

    @Value("${external-exchange.huobi.api-base-url:https://api.huobi.pro}")
    private String huobiApiBaseUrl;

    @Value("${external-exchange.huobi.order-book-endpoint:/market/depth}")
    private String huobiOrderBookEndpoint;

    @Value("${external-exchange.huobi.connect-timeout:5000}")
    private int huobiConnectTimeout;

    @Value("${external-exchange.huobi.read-timeout:5000}")
    private int huobiReadTimeout;

    @Value("${external-exchange.huobi.enabled:true}")
    private boolean huobiEnabled;

    @PostConstruct
    public void init() {
        // Configure timeouts for RestTemplate
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(connectTimeout);
        requestFactory.setReadTimeout(readTimeout);

        // Apply the configuration to the RestTemplate
        ((SimpleClientHttpRequestFactory) restTemplate.getRequestFactory()).setConnectTimeout(connectTimeout);
        ((SimpleClientHttpRequestFactory) restTemplate.getRequestFactory()).setReadTimeout(readTimeout);

        log.info("ExternalExchangeService initialized with Binance API URL: {}", binanceApiBaseUrl);
        log.info("ExternalExchangeService initialized with Huobi API URL: {}", huobiApiBaseUrl);
    }

    /**
     * Fetch detailed order book data from Binance for a given symbol
     * 
     * @param symbol The trading pair symbol (e.g., "BTC/USDT")
     * @param limit The number of bids/asks to retrieve (default: 5)
     * @return OrderBook object containing multiple levels of bids and asks
     */
    public OrderBook fetchBinanceOrderBookDetailed(String symbol, int limit) {
        OrderBook orderBook = new OrderBook();
        orderBook.setSymbol(symbol);

        // Check if Binance API integration is enabled
        if (!binanceEnabled) {
            log.warn("Binance API integration is disabled. Returning empty order book for symbol: {}", symbol);
            return orderBook;
        }

        try {
            // Convert a symbol format from "BTC/USDT" to "BTCUSDT"
            String binanceSymbol = symbol.replace("/", "");

            // Check for known invalid symbols that Binance doesn't support
            List<String> knownInvalidSymbols = Arrays.asList("ETH1", "BZB");

            for (String invalidSymbol : knownInvalidSymbols) {
                if (binanceSymbol.contains(invalidSymbol)) {
                    log.warn("Symbol {} (converted to {}) contains '{}' which is not supported by Binance. Returning empty order book.", 
                            symbol, binanceSymbol, invalidSymbol);
                    return orderBook;
                }
            }

            // Construct the URL for Binance order book API
            String url = binanceApiBaseUrl + binanceOrderBookEndpoint + "?symbol=" + binanceSymbol + "&limit=" + limit;

            log.info("Fetching detailed order book from Binance for symbol: {}", symbol);

            // Make the API request
            String response = restTemplate.getForObject(url, String.class);

            // Parse the JSON response
            JsonNode root = objectMapper.readTree(response);

            // Extract all bids and asks
            JsonNode bidsNode = root.get("bids");
            JsonNode asksNode = root.get("asks");

            // Process bids (buy orders)
            if (bidsNode != null && bidsNode.isArray()) {
                for (JsonNode bid : bidsNode) {
                    if (bid.isArray() && bid.size() >= 2) {
                        BigDecimal price = new BigDecimal(bid.get(0).asText());
                        BigDecimal quantity = new BigDecimal(bid.get(1).asText());
                        orderBook.getBids().add(new OrderBook.OrderBookEntry(price, quantity));
                    }
                }
            }

            // Process asks (sell orders)
            if (asksNode != null && asksNode.isArray()) {
                for (JsonNode ask : asksNode) {
                    if (ask.isArray() && ask.size() >= 2) {
                        BigDecimal price = new BigDecimal(ask.get(0).asText());
                        BigDecimal quantity = new BigDecimal(ask.get(1).asText());
                        orderBook.getAsks().add(new OrderBook.OrderBookEntry(price, quantity));
                    }
                }
            }

            log.info("Successfully fetched detailed order book for {}: {} bid levels, {} ask levels", 
                    symbol, orderBook.getBids().size(), orderBook.getAsks().size());
            return orderBook;
        } catch (Exception e) {
            String errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.contains("Invalid symbol")) {
                log.warn("Binance rejected symbol {} as invalid. This symbol is not supported by Binance. Error: {}", 
                        symbol, errorMessage);
            } else {
                log.error("Error fetching order book from Binance for symbol {}: {}", symbol, errorMessage);
            }
            return orderBook;
        }
    }

    /**
     * Fetch order book data from Binance for a given symbol
     * 
     * @param symbol The trading pair symbol (e.g., "BTCUSDT")
     * @param limit The number of bids/asks to retrieve (default: 5)
     * @return Map containing best bid and ask prices and volumes
     */
    public Map<String, BigDecimal> fetchBinanceOrderBook(String symbol, int limit) {
        // Use the detailed method and convert to legacy format for backward compatibility
        OrderBook orderBook = fetchBinanceOrderBookDetailed(symbol, limit);
        return orderBook.toLegacyMap();
    }

    /**
     * Convert internal symbol format to exchange-specific format
     * 
     * @param symbol The internal symbol format (e.g., "BTC/USDT")
     * @param exchange The exchange name (e.g., "binance")
     * @return The exchange-specific symbol format
     */
    public String convertSymbolFormat(String symbol, String exchange) {
        if ("binance".equalsIgnoreCase(exchange)) {
            return symbol.replace("/", "");
        } else if ("huobi".equalsIgnoreCase(exchange)) {
            // Huobi uses lowercase symbols without the slash
            return symbol.replace("/", "").toLowerCase();
        }
        // Add more exchanges as needed
        return symbol;
    }

    /**
     * Fetch detailed order book data from Huobi Pro for a given symbol
     * 
     * @param symbol The trading pair symbol (e.g., "BTC/USDT")
     * @param depth The depth of the order book to retrieve (default: 20)
     * @return OrderBook object containing multiple levels of bids and asks
     */
    public OrderBook fetchHuobiOrderBookDetailed(String symbol, int depth) {
        OrderBook orderBook = new OrderBook();
        orderBook.setSymbol(symbol);

        // Check if Huobi API integration is enabled
        if (!huobiEnabled) {
            log.warn("Huobi API integration is disabled. Returning empty order book for symbol: {}", symbol);
            return orderBook;
        }

        try {
            // Convert a symbol format from "BTC/USDT" to "btcusdt"
            String huobiSymbol = convertSymbolFormat(symbol, "huobi");

            // Construct the URL for Huobi order book API
            String url = huobiApiBaseUrl + huobiOrderBookEndpoint + "?symbol=" + huobiSymbol + "&depth=" + depth + "&type=step0";

            log.info("Fetching detailed order book from Huobi for symbol: {}", symbol);

            // Make the API request
            String response = restTemplate.getForObject(url, String.class);

            // Parse the JSON response
            JsonNode root = objectMapper.readTree(response);

            // Check if the response status is ok
            if (!"ok".equals(root.path("status").asText())) {
                log.warn("Huobi API returned non-ok status: {} for symbol: {}", root.path("status").asText(), symbol);
                return orderBook;
            }

            // Extract the tick node which contains bids and asks
            JsonNode tickNode = root.path("tick");
            if (tickNode.isMissingNode()) {
                log.warn("Huobi API response missing 'tick' node for symbol: {}", symbol);
                return orderBook;
            }

            // Extract all bids and asks
            JsonNode bidsNode = tickNode.path("bids");
            JsonNode asksNode = tickNode.path("asks");

            // Process bids (buy orders)
            if (bidsNode.isArray()) {
                for (JsonNode bid : bidsNode) {
                    if (bid.isArray() && bid.size() >= 2) {
                        BigDecimal price = new BigDecimal(bid.get(0).asText());
                        BigDecimal quantity = new BigDecimal(bid.get(1).asText());
                        orderBook.getBids().add(new OrderBook.OrderBookEntry(price, quantity));
                    }
                }
            }

            // Process asks (sell orders)
            if (asksNode.isArray()) {
                for (JsonNode ask : asksNode) {
                    if (ask.isArray() && ask.size() >= 2) {
                        BigDecimal price = new BigDecimal(ask.get(0).asText());
                        BigDecimal quantity = new BigDecimal(ask.get(1).asText());
                        orderBook.getAsks().add(new OrderBook.OrderBookEntry(price, quantity));
                    }
                }
            }

            log.info("Successfully fetched detailed order book from Huobi for {}: {} bid levels, {} ask levels", 
                    symbol, orderBook.getBids().size(), orderBook.getAsks().size());
            return orderBook;
        } catch (Exception e) {
            log.error("Error fetching order book from Huobi for symbol {}: {}", symbol, e.getMessage());
            return orderBook;
        }
    }

    /**
     * Fetch order book data from Huobi Pro for a given symbol
     * 
     * @param symbol The trading pair symbol (e.g., "BTC/USDT")
     * @param depth The depth of the order book to retrieve (default: 20)
     * @return Map containing best bid and ask prices and volumes
     */
    public Map<String, BigDecimal> fetchHuobiOrderBook(String symbol, int depth) {
        // Use the detailed method and convert to legacy format for backward compatibility
        OrderBook orderBook = fetchHuobiOrderBookDetailed(symbol, depth);
        return orderBook.toLegacyMap();
    }

    /**
     * Fetch detailed order book data from the preferred exchange for a given symbol
     * 
     * @param symbol The trading pair symbol (e.g., "BTC/USDT")
     * @param limit The number of bids/asks to retrieve
     * @return OrderBook object containing multiple levels of bids and asks
     */
    public OrderBook fetchOrderBookDetailed(String symbol, int limit) {
        // Try Huobi first if enabled
        if (huobiEnabled) {
            try {
                OrderBook orderBook = fetchHuobiOrderBookDetailed(symbol, limit);
                if (!orderBook.getBids().isEmpty() || !orderBook.getAsks().isEmpty()) {
                    log.info("Successfully fetched order book from Huobi for symbol: {}", symbol);
                    return orderBook;
                }
            } catch (Exception e) {
                log.warn("Failed to fetch order book from Huobi for symbol: {}. Error: {}", symbol, e.getMessage());
            }
        }

        // Fall back to Binance if Huobi failed or is disabled
/*
        if (binanceEnabled) {
            try {
                OrderBook orderBook = fetchBinanceOrderBookDetailed(symbol, limit);
                if (!orderBook.getBids().isEmpty() || !orderBook.getAsks().isEmpty()) {
                    log.info("Successfully fetched order book from Binance for symbol: {}", symbol);
                    return orderBook;
                }
            } catch (Exception e) {
                log.warn("Failed to fetch order book from Binance for symbol: {}. Error: {}", symbol, e.getMessage());
            }
        }
*/

        // If both exchanges failed or are disabled, return an empty order book
        log.warn("Failed to fetch order book from any exchange for symbol: {}", symbol);
        OrderBook emptyOrderBook = new OrderBook();
        emptyOrderBook.setSymbol(symbol);
        return emptyOrderBook;
    }

    /**
     * Fetch order book data from the preferred exchange for a given symbol
     * 
     * @param symbol The trading pair symbol (e.g., "BTC/USDT")
     * @param limit The number of bids/asks to retrieve
     * @return Map containing best bid and ask prices and volumes
     */
    public Map<String, BigDecimal> fetchOrderBook(String symbol, int limit) {
        // Use the detailed method and convert to legacy format for backward compatibility
        OrderBook orderBook = fetchOrderBookDetailed(symbol, limit);
        return orderBook.toLegacyMap();
    }
}
