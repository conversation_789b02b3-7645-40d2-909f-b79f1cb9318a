package com.icetea.lotus.controller;

import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.util.MessageResult;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("exchange-coin")
public class ExchangeCoinController extends BaseController {

    private final ExchangeCoinService service;

    // Get a base symbol
    @RequestMapping("base-symbol")
    public MessageResult baseSymbol() {
        List<String> baseSymbol = service.getBaseSymbol();
        if (baseSymbol != null && !baseSymbol.isEmpty()) {
            return success(baseSymbol);
        }
        return error("baseSymbol null");

    }

}
