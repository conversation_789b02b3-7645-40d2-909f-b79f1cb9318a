package com.icetea.lotus.consumer;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.constant.ActivityRewardType;
import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.constant.RewardRecordType;
import com.icetea.lotus.constant.TransactionType;
import com.icetea.lotus.entity.Coin;
import com.icetea.lotus.entity.Member;
import com.icetea.lotus.entity.MemberTransaction;
import com.icetea.lotus.entity.MemberWallet;
import com.icetea.lotus.entity.RewardActivitySetting;
import com.icetea.lotus.entity.RewardRecord;
import com.icetea.lotus.es.ESUtils;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.MemberTransactionService;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.service.RewardActivitySettingService;
import com.icetea.lotus.service.RewardRecordService;
import com.icetea.lotus.util.BigDecimalUtils;
import com.icetea.lotus.util.GeneratorUtil;
import com.icetea.lotus.util.MessageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Component
public class MemberConsumer {
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private CoinService coinService;
    @Autowired
    private MemberWalletService memberWalletService;
    @Autowired
    private RewardActivitySettingService rewardActivitySettingService;
    @Autowired
    private MemberService memberService;
    @Autowired
    private RewardRecordService rewardRecordService;
    @Autowired
    private MemberTransactionService memberTransactionService;
    @Autowired
    private ESUtils esUtils;
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Reset user wallet address
     * <p>
     * Extracts the user ID from the provided Kafka record JSON, retrieves the corresponding wallet,
     * generates a unique account string, then invokes the remote RPC endpoint to obtain a new address.
     * The address is updated in the wallet which is then persisted.
     *
     * @param record Kafka record with the coin unit as key and a JSON value containing the user ID.
     */
    @KafkaListener(topics = {"reset-member-address"})
    public void resetAddress(ConsumerRecord<String, String> record) {
        String content = record.value();
        try {
            JsonNode json = objectMapper.readTree(content);
            Coin coin = coinService.findByUnit(record.key());
            Assert.notNull(coin, "coin null");
            if (coin.getEnableRpc() == BooleanEnum.IS_TRUE) {
                MemberWallet memberWallet = memberWalletService.findByCoinUnitAndMemberId(record.key(), json.get("uid").asLong());
                Assert.notNull(memberWallet, "wallet null");
                String account = "U" + json.get("uid").asLong() + GeneratorUtil.getNonceString(4);
                //Remote RPC service URL, suffix is currency unit
                String serviceName = "SERVICE-RPC-" + coin.getUnit();
                try {
                    String url = "http://" + serviceName + "/rpc/address/{account}";
                    ResponseEntity<MessageResult> result = restTemplate.getForEntity(url, MessageResult.class, account);
                    log.info("remote call:service={},result={}", serviceName, result);
                    if (result.getStatusCode().value() == 200) {
                        MessageResult mr = result.getBody();
                        if (mr.getCode() == 0) {
                            String address = mr.getData().toString();
                            memberWallet.setAddress(address);
                        }
                    }
                } catch (Exception e) {
                    log.error("call {} failed,error={}", serviceName, e.getMessage());
                }
                memberWalletService.save(memberWallet);
            }
        } catch (Exception e) {
            log.error("resetAddress failed, error={}", e.getMessage());
        }

    }


    /**
     * Customer registration message
     * <p>
     * Parses the provided JSON content to extract the customer ID, initializes wallets for all supported
     * currencies, and registers any applicable activity rewards by updating wallet balances, saving reward
     * records, and logging transactions.
     *
     * @param content JSON string containing customer registration details
     */
    @KafkaListener(topics = {"member-register"})
    public void handle(String content) {
        log.info("handle member-register,data={}", content);
        if (StringUtils.isEmpty(content)) {
            return;
        }
        try {
            JsonNode json = objectMapper.readTree(content);
            if (json == null) {
                return;
            }
            //Retrieve all supported currencies
            List<Coin> coins = coinService.findAll();
            for (Coin coin : coins) {
                log.info("memberId:{},unit:{}", json.get("uid").asLong(), coin.getUnit());
                MemberWallet wallet = new MemberWallet();
                wallet.setCoin(coin);
                wallet.setMemberId(json.get("uid").asLong());
                wallet.setBalance(new BigDecimal(0));
                wallet.setFrozenBalance(new BigDecimal(0));
                wallet.setAddress("");

/** The address retrieval here is commented out, and all cryptocurrency addresses are only generated when the user actively requests them  **/
//            if(coin.getEnableRpc() == BooleanEnum.IS_TRUE) {
//                String account = "U" + json.getLong("uid");
//                //Remote RPC service URL, with the currency unit as the suffix
//                String serviceName = "SERVICE-RPC-" + coin.getUnit();
//                try{
//                    String url = "http://" + serviceName + "/rpc/address/{account}";
//                    ResponseEntity<MessageResult> result = restTemplate.getForEntity(url, MessageResult.class, account);
//                    log.info("remote call:service={},result={}", serviceName, result);
//                    if (result.getStatusCode().value() == 200) {
//                        MessageResult mr = result.getBody();
//                        log.info("mr={}", mr);
//                        if (mr.getCode() == 0) {
//                            //Address retrieval successful, proceed with persistence
//                            String address = (String) mr.getData();
//                            wallet.setAddress(address);
//                        }
//                    }
//                }
//                catch (Exception e){
//                    log.error("call {} failed,error={}",serviceName,e.getMessage());
//                    wallet.setAddress("");
//                }
//            }
//            else{
//                wallet.setAddress("");
//            }
                //Save wallet
                memberWalletService.save(wallet);
            }
            //Register activity rewards
            RewardActivitySetting rewardActivitySetting = rewardActivitySettingService.findByType(ActivityRewardType.REGISTER);
            if (rewardActivitySetting != null) {
                MemberWallet memberWallet = memberWalletService.findByCoinAndMemberId(rewardActivitySetting.getCoin(), json.get("uid").asLong());
                if (memberWallet == null) {
                    return;
                }
                // Reward currency
                BigDecimal amount3 = new BigDecimal(objectMapper.readTree(rewardActivitySetting.getInfo()).get("amount").asText());
                memberWallet.setBalance(BigDecimalUtils.add(memberWallet.getBalance(), amount3));
                memberWalletService.save(memberWallet);
                // Save reward record
                Member member = memberService.findOne(json.get("uid").asLong());
                RewardRecord rewardRecord3 = new RewardRecord();
                rewardRecord3.setAmount(amount3);
                rewardRecord3.setCoin(rewardActivitySetting.getCoin());
                rewardRecord3.setMember(member);
                rewardRecord3.setRemark(rewardActivitySetting.getType().getName());
                rewardRecord3.setType(RewardRecordType.ACTIVITY);
                rewardRecordService.save(rewardRecord3);
                // Save asset transaction record
                MemberTransaction memberTransaction = new MemberTransaction();
                memberTransaction.setFee(BigDecimal.ZERO);
                memberTransaction.setAmount(amount3);
                memberTransaction.setSymbol(rewardActivitySetting.getCoin().getUnit());
                memberTransaction.setType(TransactionType.ACTIVITY_AWARD);
                memberTransaction.setMemberId(member.getId());
                memberTransaction.setDiscountFee("0");
                memberTransaction.setRealFee("0");
                memberTransactionService.save(memberTransaction);
            }
        } catch (Exception e) {
            log.error("handle member-register failed, error={}", e.getMessage());
        }

    }
}
