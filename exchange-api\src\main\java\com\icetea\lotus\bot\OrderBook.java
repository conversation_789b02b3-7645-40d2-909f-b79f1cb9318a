package com.icetea.lotus.bot;

import com.icetea.lotus.entity.ExchangeOrderDirection;
import com.icetea.lotus.entity.ExchangeOrderType;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Represents an order book from an external exchange with multiple levels of bids and asks
 */
@Data
public class OrderBook {
    private String symbol;
    private List<OrderBookEntry> bids = new ArrayList<>();
    private List<OrderBookEntry> asks = new ArrayList<>();

    /**
     * Represents a single entry in the order book (price and quantity)
     */
    @Data
    public static class OrderBookEntry {
        private BigDecimal price;
        private BigDecimal quantity;

        public OrderBookEntry(BigDecimal price, BigDecimal quantity) {
            this.price = price;
            this.quantity = quantity;
        }
    }

    /**
     * Get the best bid price (highest buy price)
     * @return The best bid price or null if no bids
     */
    public BigDecimal getBestBidPrice() {
        return !bids.isEmpty() ? bids.get(0).getPrice() : null;
    }

    /**
     * Get the best bid quantity
     * @return The best bid quantity or null if no bids
     */
    public BigDecimal getBestBidQuantity() {
        return !bids.isEmpty() ? bids.get(0).getQuantity() : null;
    }

    /**
     * Get the best ask price (lowest sell price)
     * @return The best ask price or null if no asks
     */
    public BigDecimal getBestAskPrice() {
        return !asks.isEmpty() ? asks.get(0).getPrice() : null;
    }

    /**
     * Get the best ask quantity
     * @return The best ask quantity or null if no asks
     */
    public BigDecimal getBestAskQuantity() {
        return !asks.isEmpty() ? asks.get(0).getQuantity() : null;
    }

    /**
     * Convert the order book to the legacy Map format for backward compatibility
     * @return Map containing best bid and ask prices and volumes
     */
    public Map<String, BigDecimal> toLegacyMap() {
        Map<String, BigDecimal> result = new HashMap<>();

        if (!bids.isEmpty()) {
            result.put("bestBidPrice", bids.get(0).getPrice());
            result.put("bestBidVolume", bids.get(0).getQuantity());
        }

        if (!asks.isEmpty()) {
            result.put("bestAskPrice", asks.get(0).getPrice());
            result.put("bestAskVolume", asks.get(0).getQuantity());
        }

        return result;
    }

    /**
     * Calculate the mid price (average of best bid and best ask)
     * @return The mid price or null if either bid or ask is missing
     */
    public BigDecimal getMidPrice() {
        BigDecimal bestBid = getBestBidPrice();
        BigDecimal bestAsk = getBestAskPrice();

        if (bestBid != null && bestAsk != null) {
            return bestBid.add(bestAsk).divide(BigDecimal.valueOf(2), 8, BigDecimal.ROUND_HALF_UP);
        }

        return null;
    }

    /**
     * Calculate the spread (difference between best ask and best bid)
     * @return The spread or null if either bid or ask is missing
     */
    public BigDecimal getSpread() {
        BigDecimal bestBid = getBestBidPrice();
        BigDecimal bestAsk = getBestAskPrice();

        if (bestBid != null && bestAsk != null) {
            return bestAsk.subtract(bestBid);
        }

        return null;
    }

    /**
     * Calculate the spread percentage
     * @return The spread percentage or null if either bid or ask is missing
     */
    public BigDecimal getSpreadPercentage() {
        BigDecimal bestBid = getBestBidPrice();
        BigDecimal bestAsk = getBestAskPrice();

        if (bestBid != null && bestAsk != null && bestBid.compareTo(BigDecimal.ZERO) > 0) {
            return bestAsk.subtract(bestBid).divide(bestBid, 8, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
        }

        return null;
    }

    /**
     * Determine the appropriate order type based on market conditions
     * 
     * @return The recommended order type (LIMIT_PRICE or MARKET_PRICE)
     */
    public ExchangeOrderType determineOrderType() {
        // If we have valid bid and ask prices, use LIMIT_PRICE
        if (getBestBidPrice() != null && getBestAskPrice() != null) {
            return ExchangeOrderType.LIMIT_PRICE;
        }

        // If we don't have enough market data, default to LIMIT_PRICE
        // This is safer than MARKET_PRICE which could execute at unexpected prices
        return ExchangeOrderType.LIMIT_PRICE;
    }

    /**
     * Determine the appropriate order direction based on the order book
     * 
     * @return The recommended order direction (BUY for bids, SELL for asks)
     */
    public ExchangeOrderDirection determineOrderDirection() {
        // If we don't have valid market data, we can't make a recommendation
        if (getBestBidPrice() == null && getBestAskPrice() == null) {
            return null;
        }

        // If we have bids, return BUY
        if (!bids.isEmpty()) {
            return ExchangeOrderDirection.BUY;
        }

        // If we have asks, return SELL
        if (!asks.isEmpty()) {
            return ExchangeOrderDirection.SELL;
        }

        // If we can't make a clear determination, return null
        return null;
    }

    /**
     * Calculate the total volume on the bid side
     * 
     * @return The total bid volume
     */
    public BigDecimal getTotalBidVolume() {
        BigDecimal total = BigDecimal.ZERO;
        for (OrderBookEntry bid : bids) {
            total = total.add(bid.getQuantity());
        }
        return total;
    }

    /**
     * Calculate the total volume on the ask side
     * 
     * @return The total ask volume
     */
    public BigDecimal getTotalAskVolume() {
        BigDecimal total = BigDecimal.ZERO;
        for (OrderBookEntry ask : asks) {
            total = total.add(ask.getQuantity());
        }
        return total;
    }
}
