package com.icetea.lotus.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

/**
 * Component to track the initialization status of traders.
 * This is used to ensure that ExchangeOrderConsumer only starts processing
 * messages after CoinTraderEvent has completed initializing all traders.
 */
@Component
@Getter
@Setter
public class TraderInitializationStatus {
    
    private volatile boolean initialized = false;
    
}