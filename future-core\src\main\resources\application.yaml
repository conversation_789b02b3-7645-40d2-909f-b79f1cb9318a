server:
  port: 6060
  servlet:
    context-path: /future
  # Netty Configuration
  netty:
    connection-timeout: 3000
    max-connections: 1000
    max-idle-time: 10000
  # Database Configuration

# Spring Configuration
spring:
  application:
    name: futures-core
  profiles:
    active: dev,no-cache
    include: optimized-db

  datasource:
    url: **********************************************
    username: future_user
    password: eAP8efPstBJJWOkL3ybGD4AvlEs7BaiVMbzqElj7CGN6pRcOgprxXaLg0vqjaxHs
    driver-class-name: org.postgresql.Driver
    hikari:
      # Core pool settings - optimized for high throughput
      minimum-idle: ${HIKARI_MINIMUM_IDLE:15}
      maximum-pool-size: ${HIKARI_MAXIMUM_POOL_SIZE:50}
      pool-name: HikariCP
      schema: public

      # Connection lifecycle settings
      idle-timeout: ${HIKARI_IDLE_TIMEOUT:180000} # 3 minutes
      max-lifetime: ${HIKARI_MAX_LIFETIME:600000} # 10 minutes

      # Connection acquisition settings
      connection-timeout: ${HIKARI_CONNECTION_TIMEOUT:30000} # 30 seconds
      initialization-fail-timeout: 2000 # 2 seconds

      # Connection validation settings
      validation-timeout: ${HIKARI_VALIDATION_TIMEOUT:3000} # 3 seconds
      keepalive-time: 60000 # 1 minute

      # Connection quality settings
      auto-commit: false
      transaction-isolation: TRANSACTION_READ_COMMITTED

      # Monitoring and debugging
      leak-detection-threshold: ${HIKARI_LEAK_DETECTION_THRESHOLD:30000} # 30 seconds
      register-mbeans: true

      # Connection testing
      connection-test-query: SELECT 1
      test-while-idle: true
      test-on-borrow: true

      # Connection initialization
      connection-init-sql: SET statement_timeout = 30000; SET lock_timeout = 10000;

      # PostgreSQL specific optimizations
      data-source-properties:
        # Statement caching
        cachePrepStmts: true
        prepStmtCacheSize: 500
        prepStmtCacheSqlLimit: 4096

        # Server-side statement preparation
        useServerPrepStmts: true

        # Batch processing
        rewriteBatchedStatements: true

        # Metadata caching
        cacheResultSetMetadata: true
        cacheServerConfiguration: true

        # Connection management
        useLocalSessionState: true
        elideSetAutoCommits: true
        maintainTimeStats: false

        # Network settings
        tcpKeepAlive: true
        socketTimeout: 30
        connectTimeout: 10

        # Application name for monitoring
        ApplicationName: futures-core

  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: none
    database: postgresql
    show-sql: ${JPA_SHOW_SQL:false}
    open-in-view: false
    properties:
      hibernate:
        # SQL formatting
        format_sql: ${JPA_FORMAT_SQL:false}

        # JDBC settings
        jdbc:
          lob:
            non_contextual_creation: true
          batch_size: ${HIBERNATE_BATCH_SIZE:100}
          batch_versioned_data: true
          time_zone: UTC
          # Statement timeout (in milliseconds)
          statement_timeout: 30000

        # Dialect settings
        dialect: org.hibernate.dialect.PostgreSQLDialect
        legacy_limit_handler: true
        default_schema: public

        # Batch processing
        order_inserts: true
        order_updates: true
        default_batch_fetch_size: ${HIBERNATE_DEFAULT_BATCH_FETCH_SIZE:200}

        # Query optimization
        query:
          in_clause_parameter_padding: true
          fail_on_pagination_over_collection_fetch: true
          plan_cache_max_size: 4096
          plan_parameter_metadata_max_size: 256

        # Connection management - optimized for stability
        connection:
          provider_disables_autocommit: false
          handling_mode: DELAYED_ACQUISITION_AND_RELEASE_AFTER_TRANSACTION
          autoReconnect: true
          autoReconnectForPools: true
          is-connection-validation-required: true
          # Aggressive timeout checking
          checkout_timeout: 10000
          # Retry failed connections
          retry_attempts: 3
          retry_delay: 1000

        # Performance settings
        generate_statistics: false

        # Session monitoring
        session:
          events:
            log:
              LOG_QUERIES_SLOWER_THAN_MS: 1000

        # Second-level cache
        cache:
          use_second_level_cache: true
          use_query_cache: true
          region:
            factory_class: org.hibernate.cache.jcache.internal.JCacheRegionFactory

        # Physical naming strategy
        physical_naming_strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

        # Fetch optimization
        max_fetch_depth: 3

        # Statement batching
        jdbc.batch_versioned_data: true
  main:
    allow-bean-definition-overriding: true
  # Redis Configuration
  data:
    redis:
      host: ************
      port: 6379
      password: 2m0881Xc30Wh
      database: ${REDIS_DATABASE:0}
      timeout: ${REDIS_TIMEOUT:5000}
    mongodb:
      uri: **********************************************
      database: contract

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:9092}
    properties:
      sasl.mechanism: PLAIN
    listener:
      type: single
      concurrency: 9
    producer:
      retries: ${KAFKA_PRODUCER_RETRIES:3}
      batch-size: ${KAFKA_PRODUCER_BATCH_SIZE:256}
      linger: ${KAFKA_PRODUCER_LINGER:1}
      buffer-memory: ${KAFKA_PRODUCER_BUFFER_MEMORY:1048576}
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      group-id: ${KAFKA_CONSUMER_GROUP_ID:default-group}
      enable-auto-commit: ${KAFKA_CONSUMER_ENABLE_AUTO_COMMIT:true}
      session-timeout: ${KAFKA_CONSUMER_SESSION_TIMEOUT:15000}
      auto-commit-interval: ${KAFKA_CONSUMER_AUTO_COMMIT_INTERVAL:1000}
      auto-offset-reset: ${KAFKA_CONSUMER_AUTO_OFFSET_RESET:earliest}
      max-poll-records: 50
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer

  # Consul Configuration
  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30500}
      discovery:
        enabled: true
        service-name: future
        health-check-path: ${server.servlet.context-path:/future}/actuator/health
        health-check-interval: 60s
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${random.uuid}
  cache:
    jcache:
      config: classpath:ehcache.xml

  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: http://************:8082/realms/cex-lotus
      client:
        registration:
          keycloak:
            client-id: internal-service
            client-secret: X9EopoZ44E0gmdBrVpia8zZI9xDsR37e
            authorization-grant-type: client_credentials
            scope: openid
        provider:
          keycloak:
            issuer-uri: http://************:8082/realms/cex-lotus
            token-uri: http://************:8082/realms/cex-lotus/protocol/openid-connect/token

# Kafka Topics
topic-kafka:
  contract:
    order-new: contract-order-new
    order-cancel: contract-order-cancel
    order-completed: contract-order-completed
    trade: contract-trade
    trade-plate: contract-trade-plate
    position: contract-position
    mark-price: contract-mark-price
    index-price: contract-index-price
    funding-rate: contract-funding-rate
    liquidation: contract-liquidation
    order-commands: order-commands
    order-events: order-events
    last-price: contract-last-price
  minus:
    wallet-spot: minus-balance-wallet-spot

# Contract Pricing Configuration
contract:
  pricing:
    cache-update-interval: 500
    slow-cache-update-interval: 5000
    realtime-update-enabled: true
    realtime-update-interval: 5000
    price-fluctuation-range: 0.05
    price-change-probability: 30
    cache-update-enabled: true
    index-price-update-interval: 1000
    mark-price-update-interval: 1000
    funding-rate-update-interval: 60000
    market-data-update-cron: 0 0 * * * *
  kline:
    realtime-update-enabled: true
    realtime-update-interval: 20
    # Danh sách các khung thời gian được cập nhật theo thời gian thực
    realtime-update-periods: 1min,5min,15min,30min,60min,4hour,1day,1week,1mon
  special-order:
    trigger-process-interval: 1000
    time-process-interval: 5000
  liquidation:
    check-interval: 5000
    max-positions-per-batch: 100
    enabled: true
  last-price:
    # Cấu hình đồng bộ giá cuối cùng giữa các instance
    sync-enabled: true
    # Thời gian cache Redis (giờ)
    redis-cache-ttl: 24
    # Khoảng thời gian lưu định kỳ vào database (phút)
    periodic-save-interval: 5
    # Bật/tắt tự động khôi phục giá cuối cùng khi khởi động
    auto-recovery-enabled: true
    # Timeout cho Redis operations (ms)
    redis-timeout: 3000

# Thread Pool Configuration
thread-pool:
  core-size: 10
  max-size: 20
  queue-capacity: 100
  keep-alive-seconds: 60

# Circuit Breaker Configuration
circuit-breaker:
  enabled: true
  failure-threshold: 5
  reset-timeout-seconds: 60

# Snowflake ID Generator Configuration
snowflake:
  worker-id: ${SNOWFLAKE_WORKER_ID:-1} # -1 means auto-generate from MAC address

# Query Analyzer Configuration
query:
  analyzer:
    enabled: true
    n-plus-one-threshold: 10
    slow-query-threshold: 1000
  limiter:
    enabled: true
    default-limit: 1000
    max-limit: 10000

# CORS Configuration đã được vô hiệu hóa để xử lý qua Nginx
# cors:
#   allowed-origins:
#     - http://localhost:3000
#     - http://localhost:8080

# WebClient Configuration
webclient:
  timeout:
    connect: 5000
    read: 5000
    write: 5000
    response: 10000
  max-in-memory-size: 10485760
  max-connections: 500
  max-idle-time: 30
  acquire-timeout: 45

# Retry Configuration
retry:
  max-attempts: 3
  wait-duration: 1000
  max-wait-duration: 5000
  wait-duration-multiplier: 1.5

# Logging Configuration
logging:
  level:
    root: INFO
    com.icetea.lotus: INFO
    org.springframework.kafka: INFO
    org.apache.kafka: INFO
    org.hibernate: INFO
  file:
    name: logs/contract-perpetual-futures-core.log
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"

# Actuator Configuration
management:
  tracing:
    sampling:
      probability: 1.0
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics
  metrics:
    distribution:
      percentiles-histogram:
        http:
          server:
            request: true
    tags:
      application: ${spring.application.name}
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
    livenessState:
      enabled: true
    readinessState:
      enabled: true
  prometheus:
    metrics:
      export:
        enabled: true

# Resilience4j Circuit Breaker Configuration
resilience4j:
  circuitbreaker:
    instances:
      orderService:
        registerHealthIndicator: true
        slidingWindowSize: 100
        minimumNumberOfCalls: 10
        permittedNumberOfCallsInHalfOpenState: 5
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 5s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10
  ratelimiter:
    instances:
      orderService:
        registerHealthIndicator: true
        limitForPeriod: 100
        limitRefreshPeriod: 1s
        timeoutDuration: 0s
        eventConsumerBufferSize: 10
  retry:
    instances:
      orderService:
        maxAttempts: 3
        waitDuration: 1s
        enableExponentialBackoff: true
        exponentialBackoffMultiplier: 2
        retryExceptions: org.springframework.web.client.HttpServerErrorException
        ignoreExceptions: org.springframework.web.client.HttpClientErrorException
        eventConsumerBufferSize: 10

# Market Service Configuration
market:
  service:
    name: market
  websocket:
    path: /market/market-ws

# Spot Market Configuration
spot:
  market:
    tracked-symbols: BTC/USDT

# CEX Security Configuration
cex-security:
  resource-server-enabled: true
  default-principal-name: "internal-service"
  permit-all-endpoints:
    - "/contract-ws/**"
    - "/future/contract-ws/**"

# Keycloak Configuration
keycloak:
  auth-server-url: http://************:8082
  realm: cex-lotus
  resource: cex-future
  credentials:
    secret: T63reTvMuOAr1JDVO7Jn1yRvGr0aKhOl

HOSTNAME: 1
