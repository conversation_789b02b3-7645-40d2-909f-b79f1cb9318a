package com.icetea.lotus.dto;

import com.icetea.lotus.entity.ExchangeOrderDirection;
import com.icetea.lotus.entity.ExchangeOrderType;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Request object for adding a new order
 */
@Data
@Builder
public class AddOrderRequest {
    
    @NotNull(message = "Direction cannot be null")
    private ExchangeOrderDirection direction;
    
    @NotBlank(message = "Symbol cannot be blank")
    private String symbol;
    
    private BigDecimal price;
    
    @Positive(message = "Amount must be greater than zero")
    private BigDecimal amount;
    
    @NotNull(message = "Type cannot be null")
    private ExchangeOrderType type;
    
    /**
     * Custom validation for price based on order type
     * Price must be positive for LIMIT_PRICE orders
     */
    public boolean isValidPrice() {
        return type != ExchangeOrderType.LIMIT_PRICE || 
               (price != null && price.compareTo(BigDecimal.ZERO) > 0);
    }
}