# Phase 1: Extract Futures Market Data Services từ Future-Core sang Market Service

## 1. Tổng quan

Phase 1 tập trung vào việc di chuyển các service liên quan đến futures market data từ future-core service sang market service để tạo ra unified market data management cho cả spot và futures trading.

**Lưu ý**: Đ<PERSON><PERSON> là migration giữa 2 microservices đã tồn tại, không phải tạo module mới.

## 2. Current State Analysis

### 2.1. **Market Service hiện tại** (đã có)
```
market/ (MICROSERVICE)
├── MarketApplication.java ✅
├── service/
│   ├── MarketDataService ✅ (spot trading)
│   ├── KLineService ✅ (spot K-lines)
│   └── ExchangePushJob ✅ (real-time streaming)
├── websocket/
│   └── WebsocketMarketHandler ✅ (spot market data)
├── integration/
│   └── SpotMarketIntegration ✅
└── storage/
    └── KLineRepository ✅ (spot K-lines)
```

### 2.2. **Future-Core Services cần di chuyển**
```
future-core → market service:
├── LastPriceService (futures-specific)
├── IndexPriceService (futures index prices)
├── MarkPriceService (futures mark prices)
├── KLineManagementService (futures contracts)
├── MarketDataServiceImpl (futures market data)
└── WebSocketMarketHandler (futures market streaming)
```

### 2.3. **Related Components cần di chuyển**
```
future-core → market service:
├── PriceCache (futures prices)
├── KLineCache (futures K-lines)
├── LastPriceConsumer (Kafka consumer)
├── PriceProducer (Kafka producer)
├── FuturesKLineRepository
└── FuturesPriceRepository
```

## 3. Kế hoạch thực hiện chi tiết

### Step 1: Enhance Market Service Structure

#### 1.1. Cập nhật cấu trúc market service để support futures
```
market/ (EXISTING MICROSERVICE)
├── MarketApplication.java ✅
├── src/main/java/com/icetea/lotus/
│   ├── service/
│   │   ├── spot/ ✅ (existing)
│   │   │   ├── MarketDataService.java
│   │   │   ├── KLineService.java
│   │   │   └── ExchangePushJob.java
│   │   └── futures/ 🆕 (new package)
│   │       ├── FuturesMarketDataService.java
│   │       ├── FuturesLastPriceService.java
│   │       ├── FuturesIndexPriceService.java
│   │       ├── FuturesMarkPriceService.java
│   │       └── FuturesKLineService.java
│   ├── websocket/
│   │   ├── spot/ ✅ (existing)
│   │   │   └── WebsocketMarketHandler.java
│   │   └── futures/ 🆕 (new package)
│   │       └── FuturesWebSocketHandler.java
│   ├── cache/
│   │   ├── spot/ ✅ (existing)
│   │   └── futures/ 🆕 (new package)
│   │       ├── FuturesPriceCache.java
│   │       └── FuturesKLineCache.java
│   ├── messaging/
│   │   ├── producer/
│   │   │   └── FuturesPriceProducer.java 🆕
│   │   └── consumer/
│   │       └── FuturesLastPriceConsumer.java 🆕
│   ├── repository/
│   │   ├── spot/ ✅ (existing)
│   │   └── futures/ 🆕 (new package)
│   │       ├── FuturesKLineRepository.java
│   │       └── FuturesPriceRepository.java
│   ├── controller/
│   │   ├── spot/ ✅ (existing)
│   │   └── futures/ 🆕 (new package)
│   │       ├── FuturesMarketDataController.java
│   │       └── FuturesKLineController.java
│   └── integration/
│       └── FuturesMarketIntegration.java 🆕
└── src/main/resources/
    └── application.yaml ✅ (update existing)
```

#### 1.2. Cập nhật dependencies cho market service
```xml
<!-- Market service đã có sẵn dependencies cần thiết -->
<dependencies>
    <!-- Core dependencies ✅ -->
    <dependency>
        <groupId>com.icetea.lotus</groupId>
        <artifactId>core</artifactId>
        <version>${project.version}</version>
    </dependency>

    <!-- Spring Boot dependencies ✅ -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.kafka</groupId>
        <artifactId>spring-kafka</artifactId>
    </dependency>

    <!-- WebSocket support 🆕 (cần thêm cho futures) -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-websocket</artifactId>
    </dependency>

    <!-- Cache dependencies ✅ -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>

    <!-- MongoDB for K-line storage ✅ -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-mongodb</artifactId>
    </dependency>

    <!-- Service discovery ✅ -->
    <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-consul-discovery</artifactId>
    </dependency>
</dependencies>
```

### Step 2: Di chuyển Market Data Services

#### 2.1. Tạo MarketDataService interface trong market module
```java
// market/src/main/java/com/icetea/lotus/service/market/MarketDataService.java
package com.icetea.lotus.service.market;

import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import java.math.BigDecimal;

public interface MarketDataService {
    Money getLastPrice(Symbol symbol);
    Money getMarkPrice(Symbol symbol);
    Money getIndexPrice(Symbol symbol);
    BigDecimal get24hVolume(Symbol symbol);
    BigDecimal get24hChange(Symbol symbol);
    void updateLastPrice(Symbol symbol, Money price);
    void updateMarkPrice(Symbol symbol, Money price);
    void updateIndexPrice(Symbol symbol, Money price);
}
```

#### 2.2. Di chuyển implementation
```java
// Copy từ future-core và adapt
// future-core/src/main/java/com/icetea/lotus/core/domain/service/impl/MarketDataServiceImpl.java
// → market/src/main/java/com/icetea/lotus/service/market/MarketDataServiceImpl.java
```

#### 2.3. Tạo REST API cho Market Data
```java
// market/src/main/java/com/icetea/lotus/controller/MarketDataController.java
@RestController
@RequestMapping("/api/market")
public class MarketDataController {

    @Autowired
    private MarketDataService marketDataService;

    @GetMapping("/price/{symbol}")
    public ResponseEntity<PriceResponse> getPrice(@PathVariable String symbol) {
        // Implementation
    }

    @GetMapping("/kline/{symbol}")
    public ResponseEntity<List<KLineResponse>> getKLine(
            @PathVariable String symbol,
            @RequestParam String period,
            @RequestParam(required = false) Long from,
            @RequestParam(required = false) Long to) {
        // Implementation
    }
}
```

### Step 3: Cập nhật Future-Core

#### 3.1. Tạo Market Data Client trong future-core
```java
// future-core/src/main/java/com/icetea/lotus/infrastructure/client/MarketDataClient.java
@Component
public class MarketDataClient {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${market.service.url}")
    private String marketServiceUrl;

    public Money getLastPrice(Symbol symbol) {
        String url = marketServiceUrl + "/api/market/price/" + symbol.getValue();
        PriceResponse response = restTemplate.getForObject(url, PriceResponse.class);
        return Money.of(response.getLastPrice());
    }

    public Money getMarkPrice(Symbol symbol) {
        String url = marketServiceUrl + "/api/market/price/" + symbol.getValue();
        PriceResponse response = restTemplate.getForObject(url, PriceResponse.class);
        return Money.of(response.getMarkPrice());
    }
}
```

#### 3.2. Cập nhật các service trong future-core sử dụng MarketDataClient
```java
// future-core/src/main/java/com/icetea/lotus/core/domain/service/impl/OrderMatchingEngineServiceImpl.java
@Service
public class OrderMatchingEngineServiceImpl implements OrderMatchingEngineService {

    @Autowired
    private MarketDataClient marketDataClient; // Thay thế MarketDataService

    @Override
    public Money getLastPrice(Symbol symbol) {
        return marketDataClient.getLastPrice(symbol);
    }

    @Override
    public Money getMarkPrice(Symbol symbol) {
        return marketDataClient.getMarkPrice(symbol);
    }
}
```

### Step 4: Configuration và Integration

#### 4.1. Cập nhật application.yaml trong future-core
```yaml
# future-core/src/main/resources/application.yaml
market:
  service:
    url: http://market-service:8080
    timeout: 5000
    retry:
      max-attempts: 3
      delay: 1000

# Circuit breaker configuration
resilience4j:
  circuitbreaker:
    instances:
      market-service:
        registerHealthIndicator: true
        slidingWindowSize: 10
        minimumNumberOfCalls: 5
        permittedNumberOfCallsInHalfOpenState: 3
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 5s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10
```

#### 4.2. Tạo configuration cho Market Service integration
```java
// future-core/src/main/java/com/icetea/lotus/infrastructure/config/MarketServiceConfig.java
@Configuration
public class MarketServiceConfig {

    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    @Bean
    public CircuitBreaker marketServiceCircuitBreaker() {
        return CircuitBreaker.ofDefaults("market-service");
    }
}
```

### Step 5: Database Migration

#### 5.1. Tạo migration script cho market module
```sql
-- market/src/main/resources/sql/market_data_migration.sql
-- Di chuyển dữ liệu từ future database sang market database

-- Copy K-line data
INSERT INTO market.kline
SELECT * FROM future.contract_kline;

-- Copy last price data
INSERT INTO market.last_price
SELECT * FROM future.contract_last_price;

-- Copy price history data
INSERT INTO market.price_history
SELECT * FROM future.contract_price_history;
```

#### 5.2. Cập nhật database configuration
```yaml
# market/src/main/resources/application-market.yaml
spring:
  datasource:
    url: ******************************************
    username: ${DB_USERNAME:market_user}
    password: ${DB_PASSWORD:market_pass}

  data:
    mongodb:
      uri: mongodb://localhost:27017/market_kline

  jpa:
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
```

### Step 6: Testing Strategy

#### 6.1. Unit Tests
```java
// market/src/test/java/com/icetea/lotus/service/market/MarketDataServiceTest.java
@ExtendWith(MockitoExtension.class)
class MarketDataServiceTest {

    @Mock
    private LastPriceRepository lastPriceRepository;

    @Mock
    private PriceCache priceCache;

    @InjectMocks
    private MarketDataServiceImpl marketDataService;

    @Test
    void testGetLastPrice() {
        // Test implementation
    }
}
```

#### 6.2. Integration Tests
```java
// future-core/src/test/java/com/icetea/lotus/integration/MarketDataIntegrationTest.java
@SpringBootTest
@TestPropertySource(properties = {
    "market.service.url=http://localhost:8081"
})
class MarketDataIntegrationTest {

    @Autowired
    private MarketDataClient marketDataClient;

    @Test
    void testMarketDataIntegration() {
        // Test market data client integration
    }
}
```

### Step 7: Deployment Strategy

#### 7.1. Blue-Green Deployment
1. **Deploy market module** với feature flag tắt
2. **Migrate data** từ future database sang market database
3. **Enable feature flag** để route traffic sang market module
4. **Monitor performance** và error rates
5. **Rollback** nếu có issues

#### 7.2. Monitoring và Alerting
```yaml
# monitoring/prometheus/market-service-alerts.yaml
groups:
  - name: market-service
    rules:
      - alert: MarketServiceDown
        expr: up{job="market-service"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Market service is down"

      - alert: HighLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="market-service"}[5m])) > 0.5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High latency in market service"
```

## 4. Success Criteria

### 4.1. **Functional Requirements**
- ✅ Market data APIs hoạt động bình thường
- ✅ K-line data được generate và serve chính xác
- ✅ Price updates được propagate real-time
- ✅ Future-core integration hoạt động seamlessly

### 4.2. **Non-Functional Requirements**
- ✅ Response time < 100ms cho price queries
- ✅ 99.9% uptime cho market data service
- ✅ Zero data loss trong migration
- ✅ Backward compatibility maintained

### 4.3. **Rollback Plan**
- Feature flag để switch back về old implementation
- Database rollback scripts
- Configuration rollback procedures
- Monitoring dashboards để track migration progress

Phase 1 hoàn thành sẽ tạo ra foundation vững chắc cho các phase tiếp theo và demonstrate feasibility của microservices architecture.
