server:
  port: ${SERVER_PORT:6003}
  servlet:
    context-path: /exchange-api

spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  application:
    name: exchange-api
  session:
    store-type: ${SPRING_SESSION_STORE_TYPE:none}

  datasource:
    driver-class-name: org.postgresql.Driver
    hikari:
      minimum-idle: 2
      maximum-pool-size: 2
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      auto-commit: true
      leak-detection-threshold: 2000
    #    filters: stat,wall,log4j
    tomcat:
      initial-size: 5
      min-idle: 5
      max-active: 200
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
    dbcp2:
      pool-prepared-statements: true

  redis:
    pool:
      max-active: 300
      max-wait: 60000
      max-idle: 100

      #     min-idle: 20
      #    timeout: 30000
  kafka:
    producer:
      retries: ${KAFKA_PRODUCER_RETRIES:0}
      #      linger: ${KAFKA_PRODUCER_LINGER:1}
      buffer-memory: ${KAFKA_PRODUCER_BUFFER_MEMORY:1048576}
      batch-size: ${KAFKA_PRODUCER_BATCH_SIZE:256}
    consumer:
      enable-auto-commit: ${KAFKA_CONSUMER_ENABLE_AUTO_COMMIT:false}
      #      session-timeout: ${KAFKA_CONSUMER_SESSION_TIMEOUT:15000}
      auto-commit-interval: ${KAFKA_CONSUMER_AUTO_COMMIT_INTERVAL:1000}
      auto-offset-reset: ${KAFKA_CONSUMER_AUTO_OFFSET_RESET:earliest}
      #      concurrency: 9
      #      maxPollRecordsConfig: 50

  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:465}
    properties:
      mail.smtp:
        socketFactory.class: javax.net.ssl.SSLSocketFactory
        auth: true
        starttls:
          enable: true
          required: true
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:erlzeziorjuccpvx}

  data:
    mongodb:
      uri: ${SPRING_MONGODB_URI:*****************************************************
    jpa:
      repositories:
        enabled: true

  devtools:
    restart:
      enabled: true

  jpa:
    properties:
      hibernate:
        default_schema: ${DEFAULT_SCHEMA:public}
        dialect: org.hibernate.dialect.PostgreSQLDialect
    show-sql: false
  cloud:
    consul:
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        health-check-path: "${server.servlet.context-path}/actuator/health"
        health-check-interval: 10s
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${random.value}
    stream:
      kafka:
        binder:
          headers:
            - spanId
            - spanSampled
            - spanProcessId
            - spanParentSpanId
            - spanTraceId
            - spanName
            - messageSent


management:
  security:
    enabled: false
  health:
    elasticsearch:
      enabled: false
    mail:
      enabled: false
    redis:
      enabled: false # TODO need to remove later
  context-path: /actuator

endpoints:
  health:
    sensitive: false
    enabled: true
  info:
    sensitive: false
  metrics:
    sensitive: false

spark:
  system:
    work-id: 1
    data-center-id: 1
    host: "@spark.system.host@"
    name: "@spark.system.name@"
    admins: "@spark.system.admins@"
    admin-phones: "@spark.system.admin-phones@"

aliyun:
  accessKeyId: ${ALYUN_ACCESS_KEY_ID:LTAI532zG6W9gFErUw}
  accessKeySecret: ${ALYUN_ACCESS_KEY_SECRET:D0RFaFT55JSUoXc3D32324aqln1TTGuWGFP}
  ossEndpoint: ${ALYUN_OSS_ENDPOINT:oss-cn-shenzhen.aliyuncs.com}
  ossBucketName: ${ALLUN_OSS_BUCKET_NAME:fdaf2323efa}
  mail-sms:
    region: ap-southeast-1
    access-key-id: LTAI5tSqZs8e1sMSBPzt3Dkm
    access-secret: ******************************
    from-address: <EMAIL>
    from-alias: BIZZAN
    sms-sign: BIZZAN
    sms-template: SMS_199285259
    email-tag: BIZZAN

es:
  username: ${ES_USERNAME:}
  password: ${ES_PASSWORD:}
  mine:
    index: ${ES_MINE_INDEX:}
    type: ${ES_MINE_TYPE:}
  public:
    ip: ${ES_PUBLIC_IP:}
  private:
    ip: ${ES_PRIVATE_IP:#}
  port: ${ES_PORT:9200}

openapi:
  service:
    api-docs: exchange-api
    server: http://localhost:8080
    title: API Exchange API Service
    version: 1.0.0

# External exchange API configuration
external-exchange:
  binance:
    api-base-url: https://api.binance.com
    order-book-endpoint: /api/v3/depth
    connect-timeout: 5000
    read-timeout: 5000
    enabled: false
  huobi:
    api-base-url: https://api.huobi.pro
    order-book-endpoint: /market/depth
    connect-timeout: 5000
    read-timeout: 5000
    enabled: true
