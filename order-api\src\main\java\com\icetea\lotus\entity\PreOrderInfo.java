package com.icetea.lotus.entity;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

import com.icetea.lotus.constant.AdvertiseType;
import com.icetea.lotus.constant.BooleanEnum;

@Builder
@Data
public class PreOrderInfo {
    private String username;
    private BooleanEnum emailVerified;
    private BooleanEnum phoneVerified;
    private BooleanEnum idCardVerified;
    private int transactions;
    private long otcCoinId;
    private String unit;
    private BigDecimal price;
    private BigDecimal number;
    private String payMode;
    private BigDecimal minLimit;
    private BigDecimal maxLimit;
    private int timeLimit;
    private String country;
    private String currency;
    private AdvertiseType advertiseType;
    private String remark;

    //Maximum tradable quantity
    private BigDecimal maxTradableAmount;
}
