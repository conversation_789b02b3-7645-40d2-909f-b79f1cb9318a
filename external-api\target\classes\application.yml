server:
  port: ${SERVER_PORT:6001}
  servlet:
    context-path: /uc

spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  application:
    name: uc
  datasource:
    driver-class-name: ${DATASOURCE_DRIVER_CLASS_NAME:org.postgresql.Driver}
    url: ${DATASOURCE_URL:*****************************************************************}
    username: ${DATASOURCE_USERNAME:lotus_root}
    password: ${DATASOURCE_PASSWORD:Lotus12345!}
    hikari:
      minimum-idle: 1
      maximum-pool-size: 2
  data:
    jpa:
      repositories:
        enabled: ${JPA_REPOSITORIES_ENABLED:true}
    redis:
      timeout: ${REDIS_TIMEOUT:30000}
      password: ${REDIS_PASSWORD:2m0881Xc30Wh}
      pool:
        max-active: ${REDIS_MAX_ACTIVE:300}
        max-wait: ${REDIS_MAX_WAIT:60000}
        max-idle: ${REDIS_MAX_IDLE:100}
        min-idle: ${REDIS_MIN_IDLE:20}

  jpa:
    show-sql: ${JPA_SHOW_SQL:true}
    hibernate:
      ddl-auto: ${JPA_HIBERNATE_AUTO:none}
    properties:
      hibernate:
        format_sql: true
        default_schema: ${DEFAULT_SCHEMA:bizzan}

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:************:9092}
    producer:
      retries: ${KAFKA_PRODUCER_RETRIES:0}
      linger: ${KAFKA_PRODUCER_LINGER:1}
      batch-size: ${KAFKA_PRODUCER_BATCH_SIZE:256}
      buffer-memory: ${KAFKA_PRODUCER_BUFFER_MEMORY:1048576}
    consumer:
      session-timeout: ${KAFKA_CONSUMER_SESSION_TIMEOUT:15000}
      concurrency: ${KAFKA_CONSUMER_CONCURRENCY:9}
      maxPollRecordsConfig: ${KAFKA_CONSUMER_MAX_POLL_RECORDS:50}
      enable-auto-commit: ${KAFKA_CONSUMER_ENABLE_AUTO_COMMIT:false}
      auto-commit-interval: ${KAFKA_CONSUMER_AUTO_COMMIT_INTERVAL:100}
      group-id: ${KAFKA_CONSUMER_GROUP_ID:default-group}
      auto-offset-reset: ${KAFKA_CONSUMER_AUTO_OFFSET_RESET:earliest}

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30500}
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        health-check-path: "${server.servlet.context-path}/actuator/health"
        health-check-interval: 10s
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${random.value}
    stream:
      kafka:
        binder:
          headers:
            - spanId
            - spanSampled
            - spanProcessId
            - spanParentSpanId
            - spanTraceId
            - spanName
            - messageSent

  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:465}
    properties:
      mail.smtp:
        socketFactory.class: javax.net.ssl.SSLSocketFactory
        auth: true
        starttls:
          enable: true
          required: true
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:guyp jllk gpum srpo}

  http:
    multipart:
      maxFileSize: ${MULTIPART_MAX_FILE_SIZE:10MB}
      maxRequestSize: ${MULTIPART_MAX_REQUEST_SIZE:10MB}

  devtools:
    restart:
      enabled: ${SPRING_DEVTOOLS_RESTART_ENABLED:false}

  freemarker:
    cache: ${FREMARKER_CACHE:false}
    template-loader-path: ${FREMARKER_TEMPLATE_LOADER_PATH:classpath:/templates}

  jackson:
    serialization:
      indent_output: ${JACKSON_SERIALIZATION:true}
    date-format: ${JACKSON_DATE_FORMAT:yyyy-MM-dd HH:mm:ss}
    time-zone: ${JACKSON_TIME_ZONE:GMT+8}

  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: http://************:8082/realms/cex-lotus
      client:
        registration:
          keycloak:
            client-id: internal-service
            client-secret: X9EopoZ44E0gmdBrVpia8zZI9xDsR37e
            authorization-grant-type: client_credentials
            scope: openid
        provider:
          keycloak:
            issuer-uri: http://************:8082/realms/cex-lotus
            token-uri: http://************:8082/realms/cex-lotus/protocol/openid-connect/token

# keycloak
keycloak:
  auth-server-url: http://************:8082
  realm: cex-lotus
  resource: cex-external-api-client
  credentials:
    secret: xkZbe85jveKyrsh65X6tWKTbkIgWWOGZ
cex-security:
  resource-server-enabled: true
  default-principal-name: internal-service
  permit-all-endpoints:
    - "/auth/login"
    - "/login"
    - "/reg/email/code"
    - "/register/email"
    - "/reset/login/password"

management:
  health:
    elasticsearch:
      enabled: false
    mail:
      enabled: false
  security:
    enabled: false
  context-path: /actuator


endpoints:
  health:
    enabled: true
    sensitive: false
  info:
    sensitive: false
  metrics:
    sensitive: false

logging:
  level:
    "*": ${LOGGING_LEVEL:debug}

security:
  management:
    context-path: ${MANAGEMENT_CONTEXT_PATH:/monitor}
  user:
    name: ${SECURITY_USER_NAME:test2}
    password: ${SECURITY_USER_PASSWORD:test12**3828**@#&@}

sms:
  driver: ${SMS_DRIVER:twilio}
  gateway: ${SMS_GATEWAY:}
  username: ${SMS_USERNAME:**********}
  password: ${SMS_PASSWORD:4901B0E56BD8CB679D8C822F8}
  sign: ${SMS_SIGN:Icetea-Software}
  internationalGateway: ${SMS_INTERNATIONAL_GATEWAY:}
  internationalUsername: ${SMS_INTERNATIONAL_USERNAME:}
  internationalPassword: ${SMS_INTERNATIONAL_PASSWORD:}

twilio:
  account_sid: **********************************
  auth_token: 9d6fe9aa6af7e56b72397b219b817e5f
  trial_number: +***********

spark:
  system:
    work-id: ${SPARK_WORK_ID:1}
    data-center-id: ${SPARK_DATA_CENTRE_ID:1}
    host: ${SPARK_HOST:smtp.126.com}
    name: ${SPARK_NAME:BIZZAN}
    admins: ${SPARK_ADMINS:<EMAIL>}
    admin-phones: ${SPARK_ADMIN_PHONES:**********}

commission:
  need:
    real-name: ${COMMISSION_NEED_REAL_NAME:1}
  promotion:
    second-level: ${COMMISSION_PROMOTION_SECOND_LEVEL:1}

person:
  promote:
    prefix: ${PEER_PREFIX:https://www.bizzan.com/reg?code=}

transfer:
  url: ${TRANSFER_URL:}
  key: ${TRANSFER_KEY:}
  smac: ${TRANSFER_SMAC:}

geetest:
  captchaId: ${GEETEST_CAPTCHA_ID:ebd15c83cb7daecc963bc87b2c9cd76}
  privateKey: ${GEETEST_PRIVATE_KEY:26e11b2e8ed2ccbcf060756f2b845fd}
  newFailback: ${GEETEST_NEW_FAILBACK:0}

es:
  username: ${ES_USERNAME:}
  password: ${ES_PASSWORD:}
  mine:
    index: ${ES_MINE_INDEX:}
    type: ${ES_MINE_TYPE:}
  public:
    ip: ${ES_PUBLIC_IP:}
  private:
    ip: ${ES_PRIVATE_IP:#}
  port: ${ES_PORT:9200}

openapi:
  service:
    api-docs: external-api
    server: http://localhost:8080
    title: API Enternal Service
    version: 1.0.0

aliyun:
  accessKeyId: ${ALYUN_ACCESS_KEY_ID:LTAI532zG6W9gFErUw}
  accessKeySecret: ${ALYUN_ACCESS_KEY_SECRET:D0RFaFT55JSUoXc3D32324aqln1TTGuWGFP}
  ossEndpoint: ${ALYUN_OSS_ENDPOINT:oss-cn-shenzhen.aliyuncs.com}
  ossBucketName: ${ALLUN_OSS_BUCKET_NAME:fdaf2323efa}
  mail-sms:
    region: ap-southeast-1
    access-key-id: LTAI5tSqZs8e1sMSBPzt3Dkm
    access-secret: ******************************
    from-address: <EMAIL>
    from-alias: BIZZAN
    sms-sign: BIZZAN
    sms-template: SMS_199285259
    email-tag: BIZZAN

access:
  key:
    id: ${ACCESS_KEY_ID:}
    secret: ${ACCESS_KEY_SECRET:}

water:
  proof:
    app:
      id: ${PROOF_APP_ID:203187463}
      secret:
        key: ${PROOF_APP_SECRET_KEY:0qatdl6QlhK5cAtEAfE2MA**}

google:
  host: BIZZAN.PRO

oss:
  name: minio

email:
  driver: java
