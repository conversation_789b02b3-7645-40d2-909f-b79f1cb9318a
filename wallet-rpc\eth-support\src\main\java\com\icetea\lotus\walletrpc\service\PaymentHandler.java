package com.icetea.lotus.walletrpc.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.icetea.lotus.walletrpc.entity.Coin;
import com.icetea.lotus.walletrpc.entity.Contract;
import com.icetea.lotus.walletrpc.entity.Payment;
import com.icetea.lotus.walletrpc.util.EthConvert;
import com.icetea.lotus.walletrpc.util.MessageResult;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Function;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.crypto.Credentials;
import org.web3j.crypto.RawTransaction;
import org.web3j.crypto.TransactionEncoder;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.request.Transaction;
import org.web3j.protocol.core.methods.response.EthGetTransactionCount;
import org.web3j.protocol.core.methods.response.EthSendTransaction;
import org.web3j.protocol.core.methods.response.NetVersion;
import org.web3j.utils.Convert;
import org.web3j.utils.Numeric;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.Objects;


/**
 * The ETH and Token payment module supports synchronous tasks and asynchronous tasks, and uses asynchronous tiers when continuous payments may occur at a single address
 */
@Component
public class PaymentHandler {

    private final Logger logger = LoggerFactory.getLogger(PaymentHandler.class);

    @Autowired
    private Web3j web3j;

    @Autowired
    private EthService ethService;

    @Autowired(required = false)
    private Contract contract;

    @Autowired
    private Coin coin;

    @Autowired
    private KafkaTemplate<String,String> kafkaTemplate;

    @Autowired(required = false)
    private EtherscanApi etherscanApi;

    private Payment current;

    private final LinkedList<Payment> tasks = new LinkedList<>();

    private int checkTimes = 0;

    private int maxCheckTimes = 100;

    public void transferTokenAsync(Credentials credentials, String to, BigDecimal amount,String withdrawId){
        Payment payment = Payment.builder()
                .credentials(credentials)
                .amount(amount)
                .to(to)
                .txBizNumber(withdrawId)
                .unit(coin.getUnit())
                .build();
        synchronized (tasks) {
            tasks.addLast(payment);
        }
    }

    public void notify(Payment payment,int status){
        JSONObject json = new JSONObject();
        json.put("withdrawId",payment.getTxBizNumber());
        json.put("txid",payment.getTxid());
        json.put("status",status);
        kafkaTemplate.send("withdraw-notify",coin.getName(), JSON.toJSONString(json));
    }

    public void transferEthAsync(Credentials credentials, String to, BigDecimal amount,String withdrawId){
        Payment payment = Payment.builder()
                .credentials(credentials)
                .amount(amount)
                .to(to)
                .txBizNumber(withdrawId)
                .unit("ETH")
                .build();
        synchronized (tasks) {
            tasks.addLast(payment);
        }
    }

    public MessageResult transferEth(Credentials credentials, String to, BigDecimal amount) {
        Payment payment = Payment.builder()
                .credentials(credentials)
                .amount(amount)
                .to(to)
                .unit("ETH")
                .build();
        return transferEth(payment);
    }

    public MessageResult transferEth(Payment payment) {
        try {
            EthGetTransactionCount ethGetTransactionCount = web3j.ethGetTransactionCount(payment.getCredentials().getAddress(), DefaultBlockParameterName.LATEST)
                    .sendAsync()
                    .get();

            BigInteger nonce = ethGetTransactionCount.getTransactionCount();
            BigInteger gasPrice = ethService.getGasPrice();
            BigInteger value = Convert.toWei(payment.getAmount(), Convert.Unit.ETHER).toBigInteger();
            BigInteger maxGas = coin.getGasLimit();
            String toAddress = payment.getTo();
            logger.info("value={},gasPrice={},gasLimit={},nonce={},address={}", value, gasPrice, maxGas, nonce, payment.getTo());
            RawTransaction rawTransaction = RawTransaction.createEtherTransaction(
                    nonce,
                    gasPrice,
                    maxGas,
                    toAddress,
                    value
            );

            long chainId = getChainId();
            byte[] signedMessage = TransactionEncoder.signMessage(rawTransaction, chainId, payment.getCredentials());
            String hexValue = Numeric.toHexString(signedMessage);
            EthSendTransaction ethSendTransaction = web3j.ethSendRawTransaction(hexValue).send();
            logger.info("ethSendTransaction: {}", JSON.toJSONString(ethSendTransaction));
            if (ethSendTransaction.hasError()) {
                return new MessageResult(500, "transferEth error: " + ethSendTransaction.getError().getMessage());
            }
            String txHash = ethSendTransaction.getTransactionHash();
            logger.info("txid = {}", txHash);
            if (StringUtils.isEmpty(txHash)) {
                return new MessageResult(500, "transferEth error: empty txHash");
            } else {
                if (etherscanApi != null) {
                    logger.info("=====Send an Etherscan broadcast transaction======");
                    etherscanApi.sendRawTransaction(hexValue);
                }
                MessageResult mr = new MessageResult(0, "success");
                mr.setData(txHash);
                return mr;
            }
//            Transaction transaction = Transaction.createEtherTransaction(payment.getCredentials().getAddress(), nonce, gasPrice, maxGas, payment.getTo(), value);
//
//            return web3j.ethSendTransaction(transaction).sendAsync().thenApply(ethSendTransaction -> {
//                logger.info("ethSendTransaction: {}", JSON.toJSONString(ethSendTransaction));
//                if (Objects.nonNull(ethSendTransaction.getError())) {
//                    return new MessageResult(500, "transferEth error: " + ethSendTransaction.getError().toString());
//                }
//                String transactionHash = ethSendTransaction.getTransactionHash();
//                byte[] signedTransaction = transactionHash.getBytes();
//                String hexValue1 = Numeric.toHexString(signedTransaction);
//                web3j.ethSendRawTransaction(hexValue1).sendAsync();
//                logger.info("txid = {}", transactionHash);
//                if (StringUtils.isEmpty(transactionHash)) {
//                    return new MessageResult(500, "transferEth error");
//                }
//                else {
//                    if(etherscanApi != null){
//                        logger.info("=====Send an Etherscan broadcast transaction======");
//                        etherscanApi.sendRawTransaction(hexValue1);
//                    }
//                    MessageResult mr = new MessageResult(0, "success");
//                    mr.setData(transactionHash);
//                    return mr;
//                }
//
//            }).get();
//            byte[] signedTransaction = ethSendTrans.getTransactionHash().getBytes();
//            String hexValue1 = Numeric.toHexString(signedTransaction);
//            EthSendTransaction ethSendTransaction1 = web3j.ethSendRawTransaction(hexValue1).sendAsync().get();
//            String transactionHash1 = ethSendTransaction1.getTransactionHash();
//
//
//            RawTransaction rawTransaction = RawTransaction.createEtherTransaction(
//                    nonce, gasPrice, maxGas, payment.getTo(), value);

//            byte[] signedMessage = TransactionEncoder.signMessage(rawTransaction, payment.getCredentials());
//            String hexValue = Numeric.toHexString(signedMessage);
//            EthSendTransaction ethSendTransaction = web3j.ethSendRawTransaction(hexValue).sendAsync().get();
//            String transactionHash = ethSendTransaction.getTransactionHash();
//            logger.info("txid = {}", ethSendTransaction1.getTransactionHash());
//            if (StringUtils.isEmpty(ethSendTransaction1.getTransactionHash())) {
//                return new MessageResult(500, "transferEth error");
//            }
//            else {
//                if(etherscanApi != null){
//                    logger.info("=====Send an Etherscan broadcast transaction======");
//                    etherscanApi.sendRawTransaction(hexValue1);
//                }
//                MessageResult mr = new MessageResult(0, "success");
//                mr.setData(ethSendTransaction1.getTransactionHash());
//                return mr;
//            }
        } catch (Exception e) {
            e.printStackTrace();
            return new MessageResult(500, "The transaction failed,error:" + e.getMessage());
        }
    }

    private long getChainId() throws IOException {
        NetVersion netVersion = web3j.netVersion().send();

        String chainIdStr = netVersion.getNetVersion(); // Ví dụ: "1", "5", "11155111"
        long chainId = Long.parseLong(chainIdStr);

        System.out.println("Chain ID = " + chainId);
        return chainId;
    }

    public MessageResult transferToken(Payment payment){
        try {
            EthGetTransactionCount ethGetTransactionCount = web3j.ethGetTransactionCount(payment.getCredentials().getAddress(), DefaultBlockParameterName.LATEST)
                    .sendAsync()
                    .get();
            BigInteger nonce = ethGetTransactionCount.getTransactionCount();
            BigInteger gasPrice = ethService.getGasPrice();
            BigInteger value = EthConvert.toWei(payment.getAmount(), contract.getUnit()).toBigInteger();
            Function fn = new Function("transfer", Arrays.asList(new Address(payment.getTo()), new Uint256(value)), Collections.<TypeReference<?>> emptyList());
            String data = FunctionEncoder.encode(fn);
            BigInteger maxGas = contract.getGasLimit();
            logger.info("from={},value={},gasPrice={},gasLimit={},nonce={},address={}",payment.getCredentials().getAddress(), value, gasPrice, maxGas, nonce,payment.getTo());
            RawTransaction rawTransaction = RawTransaction.createTransaction(
                    nonce, gasPrice, maxGas, contract.getAddress(), data);
            byte[] signedMessage = TransactionEncoder.signMessage(rawTransaction, payment.getCredentials());
            String hexValue = Numeric.toHexString(signedMessage);
            logger.info("hexRawValue={}",hexValue);
            EthSendTransaction ethSendTransaction = web3j.ethSendRawTransaction(hexValue).sendAsync().get();
            String transactionHash = ethSendTransaction.getTransactionHash();
            logger.info("txid:{}", transactionHash);
            if (StringUtils.isEmpty(transactionHash)) {
                return new MessageResult(500, "transferToken ERROR");
            }
            else {
                if(etherscanApi != null){
                    logger.info("=====Etherscan======");
                    etherscanApi.sendRawTransaction(hexValue);
                }
                payment.setTxid(transactionHash);
                MessageResult mr = new MessageResult(0, "success");
                mr.setData(transactionHash);
                return mr;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new MessageResult(500, "transferToken  error:" + e.getMessage());
        }
    }

    public MessageResult transferToken(Credentials credentials, String to, BigDecimal amount) {
        Payment payment = Payment.builder()
                .credentials(credentials)
                .amount(amount)
                .to(to)
                .unit(coin.getUnit())
                .build();
        return transferToken(payment);
    }

    /**
     * Check whether the current task is paid for
     */
    @Scheduled(cron = "0/30 * * * * *")
    public synchronized void checkJob(){
        logger.info("checkJob");
//        && StringUtils.isNotEmpty(current.getTxid())
        if (current != null ) {
            synchronized (current) {
                try {
                    checkTimes ++;
                    if (ethService.isTransactionSuccess(current.getTxid())) {
                        logger.info("checkJob {} checkTimes:{}", JSON.toJSON(current),checkTimes);
                        notify(current,1);
                        current = null;
                    }
                    else{
                        logger.info("checkJob {} checkTimes:{}", JSON.toJSON(current),checkTimes);
                        if(checkTimes > maxCheckTimes){
                            //The timeout was unsuccessful
                            notify(current,0);
                            current = null;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        else{
            logger.info("checkJob end");
        }
    }

    public MessageResult transfer(Payment payment){
        if(payment.getUnit().equalsIgnoreCase("ETH")){
            return transferEth(payment);
        }
        else{
            return transferToken(payment);
        }
    }

    @Scheduled(cron = "0/30 * * * * *")
    public synchronized void doJob(){
        synchronized (tasks) {
            logger.info("Payment handler doJob {}",tasks.size());
            if (current == null && !tasks.isEmpty()) {
                logger.info("Payment handler doJob ---{}", JSONObject.toJSONString(current));
                Payment payment = tasks.getFirst();
                MessageResult result = transfer(payment);
                if (result.getCode() == 0) {
                    logger.info("------txID:{}", result.getData().toString());
                    payment.setTxid(result.getData().toString());
                    tasks.removeFirst();
                    current = payment;
                    checkTimes = 0;
                }
            }
        }
    }
}
