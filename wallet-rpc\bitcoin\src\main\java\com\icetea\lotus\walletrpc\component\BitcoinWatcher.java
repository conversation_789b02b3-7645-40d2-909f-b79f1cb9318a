package com.icetea.lotus.walletrpc.component;

import com.icetea.lotus.walletrpc.bitcoinrpc.BitcoinRpcClient;
import com.icetea.lotus.walletrpc.bitcoinrpc.BitcoinRpcException;
import com.icetea.lotus.walletrpc.entity.Deposit;
import com.icetea.lotus.walletrpc.service.AccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class BitcoinWatcher extends Watcher {

    private final BitcoinRpcClient rpcClient;
    private final AccountService accountService;

    @Override
    public List<Deposit> replayBlock(Long startBlockNumber, Long endBlockNumber) {
        List<Deposit> deposits = new ArrayList<>();
        try {
            for (Long blockHeight = startBlockNumber; blockHeight <= endBlockNumber; blockHeight++) {
                String blockHash;
                try {
                    blockHash = rpcClient.getBlockHash(blockHeight.intValue());
                } catch (BitcoinRpcException e) {
                    log.warn("Failed to get block hash for height {}: {}", blockHeight, e.getMessage());
                    continue;
                }

                // Get the block
                BitcoinRpcClient.Block block = rpcClient.getBlock(blockHash);
                List<String> txIds = block.tx();
                log.info("Block {} contains {} transactions", blockHeight, txIds.size());

                // Traverse the transactions in the block
                for (String txId : txIds) {
                    BitcoinRpcClient.RawTransaction transaction = rpcClient.getRawTransaction(txId);
                    List<BitcoinRpcClient.RawTransaction.Vout> outs = transaction.vOut();

                    if (outs != null) {
                        for (BitcoinRpcClient.RawTransaction.Vout out : outs) {
                            if (out.scriptPubKey() != null) {
                                List<String> addresses = out.scriptPubKey().addresses();

                                if (addresses != null && !addresses.isEmpty()) {
                                    String address = addresses.get(0);
                                    BigDecimal amount = out.value();

                                    if (accountService.isAddressExist(address)) {
                                        log.info("Discovered deposit to address {} with amount {} BTC", address, amount);

                                        Deposit deposit = new Deposit();
                                        deposit.setTxid(transaction.txId());
                                        deposit.setBlockHeight((long) block.height());
                                        deposit.setBlockHash(transaction.blockHash());
                                        deposit.setAmount(amount);
                                        deposit.setAddress(address);
                                        deposit.setTime(new java.util.Date(transaction.time() * 1000)); // Convert Unix timestamp to Date
                                        deposits.add(deposit);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (BitcoinRpcException e) {
            log.error("Error replaying blocks: {}", e.getMessage(), e);
            return null;
        }

        return deposits;
    }

    @Override
    public Long getNetworkBlockHeight() {
        try {
            return (long) rpcClient.getBlockCount();
        } catch (BitcoinRpcException e) {
            log.error("Error getting network block height: {}", e.getMessage(), e);
            return 0L;
        }
    }
}
