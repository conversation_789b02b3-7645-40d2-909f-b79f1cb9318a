package com.icetea.lotus.walletrpc.service;

import com.icetea.lotus.walletrpc.entity.Account;
import com.icetea.lotus.walletrpc.entity.Coin;
import com.icetea.lotus.walletrpc.entity.Contract;
import com.icetea.lotus.walletrpc.util.EthConvert;
import com.icetea.lotus.walletrpc.util.MessageResult;
import com.googlecode.jsonrpc4j.JsonRpcHttpClient;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Function;
import org.web3j.crypto.*;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.response.*;
import org.web3j.utils.Convert;
import org.web3j.utils.Numeric;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.security.InvalidAlgorithmParameterException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.util.*;


@Component
public class EthService {
    private Logger logger = LoggerFactory.getLogger(EthService.class);
    @Autowired
    private Coin coin;
    @Autowired
    private Web3j web3j;
    @Autowired
    private PaymentHandler paymentHandler;
    @Autowired
    private AccountService accountService;
    @Autowired
    private JsonRpcHttpClient jsonrpcClient;
    @Autowired(required = false)
    private Contract contract;

    public String createNewWallet(String account, String password) throws NoSuchAlgorithmException, NoSuchProviderException, InvalidAlgorithmParameterException, CipherException, IOException, CipherException {
        logger.info("====>  Generate new wallet file for ETH.");
        File keystoreDir = new File(coin.getKeystorePath());
        if (!keystoreDir.exists()) {
            boolean created = keystoreDir.mkdirs();
            if (!created) {
                throw new RuntimeException("Không thể tạo thư mục keystore: " + coin.getKeystorePath());
            }
        }
        String fileName = WalletUtils.generateNewWalletFile(password, new File(coin.getKeystorePath()), true);
        Credentials credentials = WalletUtils.loadCredentials(password, coin.getKeystorePath() + "/" + fileName);
        String address = credentials.getAddress();
        accountService.saveOne(account, fileName, address);
        return address;
    }


    /**
     * Synchronize balances
     *
     * @param address address
     * @throws IOException error
     */
    public void syncAddressBalance(String address) throws IOException {
        BigDecimal balance = getBalance(address);
        accountService.updateBalance(address, balance);
    }


    public MessageResult transferFromWithdrawWallet(String toAddress, BigDecimal amount, boolean sync, String withdrawId) {
        return transfer(coin.getKeystorePath() + "/" + coin.getWithdrawWallet(), coin.getWithdrawWalletPassword(), toAddress, amount, sync, withdrawId);
    }

    public MessageResult transfer(String walletFile, String password, String toAddress, BigDecimal amount, boolean sync, String withdrawId) {
        Credentials credentials;
        try {
            credentials = WalletUtils.loadCredentials(password, walletFile);
        } catch (IOException e) {
            e.printStackTrace();
            return new MessageResult(500, "The wallet file does not exist");
        } catch (CipherException e) {
            e.printStackTrace();
            return new MessageResult(500, "Decryption failed and the password was incorrect");
        }
        if (sync) {
            return paymentHandler.transferEth(credentials, toAddress, amount);
        } else {
            paymentHandler.transferEthAsync(credentials, toAddress, amount, withdrawId);
            return new MessageResult(0, "The submission was successful");
        }
    }

    public BigDecimal getBalance(String address) throws IOException {
        EthGetBalance getBalance = web3j.ethGetBalance(address, DefaultBlockParameterName.LATEST).send();
        return Convert.fromWei(getBalance.getBalance().toString(), Convert.Unit.ETHER);
    }

    public BigInteger getGasPrice() throws IOException {
        EthGasPrice gasPrice = web3j.ethGasPrice().send();
        BigInteger baseGasPrice = gasPrice.getGasPrice();
        return new BigDecimal(baseGasPrice).multiply(coin.getGasSpeedUp()).toBigInteger();
    }

    public MessageResult transferFromWallet(String address, BigDecimal amount, BigDecimal fee, BigDecimal minAmount) {
        logger.info("transferFromWallet method");
        List<Account> accounts = accountService.findByBalance(minAmount);
        if (accounts == null || accounts.isEmpty()) {
            MessageResult messageResult = new MessageResult(500, "There is no transfer account that meets the conditions (greater than 0.1)!");
            logger.info(messageResult.toString());
            return messageResult;
        }
        BigDecimal transferredAmount = BigDecimal.ZERO;
        for (Account account : accounts) {
            BigDecimal realAmount = account.getBalance().subtract(fee);
            if (realAmount.compareTo(amount.subtract(transferredAmount)) > 0) {
                realAmount = amount.subtract(transferredAmount);
            }
            MessageResult result = transfer(coin.getKeystorePath() + "/" + account.getWalletFile(), "", address, realAmount, true, "");
            if (result.getCode() == 0 && result.getData() != null) {
                logger.info("transfer address={},amount={},txid={}", account.getAddress(), realAmount, result.getData());
                transferredAmount = transferredAmount.add(realAmount);
                try {
                    syncAddressBalance(account.getAddress());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (transferredAmount.compareTo(amount) >= 0) {
                break;
            }
        }
        MessageResult result = new MessageResult(0, "success");
        result.setData(transferredAmount);
        return result;
    }

    public MessageResult transferToken(String fromAddress, String toAddress, BigDecimal amount, boolean sync) {
        Account account = accountService.findByAddress(fromAddress);
        Credentials credentials;
        try {
            credentials = WalletUtils.loadCredentials("", coin.getKeystorePath() + "/" + account.getWalletFile());
        } catch (IOException e) {
            e.printStackTrace();
            return new MessageResult(500, "The private key file does not exist");
        } catch (CipherException e) {
            e.printStackTrace();
            return new MessageResult(500, "Decryption failed and the password was incorrect");
        }
        if (sync) {
            return paymentHandler.transferToken(credentials, toAddress, amount);
        } else {
            paymentHandler.transferTokenAsync(credentials, toAddress, amount, "");
            return new MessageResult(0, "The submission was successful");
        }
    }

    public MessageResult transferTokenFromWithdrawWallet(String toAddress, BigDecimal amount, boolean sync, String withdrawId) {
        Credentials credentials;
        try {
            //Unlock the withdrawal wallet
            credentials = WalletUtils.loadCredentials(coin.getWithdrawWalletPassword(), coin.getKeystorePath() + "/" + coin.getWithdrawWallet());
        } catch (IOException e) {
            e.printStackTrace();
            return new MessageResult(500, "The private key file does not exist");
        } catch (CipherException e) {
            e.printStackTrace();
            return new MessageResult(500, "Decryption failed and the password was incorrect");
        }
        if (sync) {
            return paymentHandler.transferToken(credentials, toAddress, amount);
        } else {
            paymentHandler.transferTokenAsync(credentials, toAddress, amount, withdrawId);
            return new MessageResult(0, "The submission was successful");
        }
    }


    public BigDecimal getTokenBalance(String address) throws IOException {
        BigInteger balance = BigInteger.ZERO;
        Function fn = new Function("balanceOf", Collections.singletonList(new Address(address)), Collections.<TypeReference<?>>emptyList());
        String data = FunctionEncoder.encode(fn);
        Map<String, String> map = new HashMap<String, String>();
        map.put("to", contract.getAddress());
        map.put("data", data);
        try {
            String methodName = "eth_call";
            Object[] params = new Object[]{map, "latest"};
            String result = jsonrpcClient.invoke(methodName, params, Object.class).toString();
            if (StringUtils.isNotEmpty(result)) {
                if ("0x".equalsIgnoreCase(result) || result.length() == 2) {
                    result = "0x0";
                }
                balance = Numeric.decodeQuantity(result);
            }
        } catch (Throwable e) {
            e.printStackTrace();
            logger.info("Query interface ERROR");
        }
        return EthConvert.fromWei(new BigDecimal(balance), contract.getUnit());
    }

    public BigDecimal getMinerFee(BigInteger gasLimit) throws IOException {
        BigDecimal fee = new BigDecimal(getGasPrice().multiply(gasLimit));
        return Convert.fromWei(fee, Convert.Unit.ETHER);
    }

    public Boolean isTransactionSuccess(String txid) throws IOException {
        Optional<Transaction> transactionOpt = web3j.ethGetTransactionByHash(txid)
                .send()
                .getTransaction();

        if (!transactionOpt.isPresent()) {
            return false;
        }

        Transaction transaction = transactionOpt.get();

        // Kiểm tra nếu block hash là 0x0...0 => giao dịch chưa được xác nhận
        if (!"0x0000000000000000000000000000000000000000000000000000000000000000"
                .equalsIgnoreCase(transaction.getBlockHash())) {
            return false;
        }

        try {
            Optional<TransactionReceipt> receiptOpt = web3j.ethGetTransactionReceipt(txid)
                    .send()
                    .getTransactionReceipt();
            return receiptOpt.map(receipt -> "0x1".equalsIgnoreCase(receipt.getStatus()))
                    .orElse(false);
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

}
