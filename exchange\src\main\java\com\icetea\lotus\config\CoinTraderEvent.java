package com.icetea.lotus.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.Trader.CoinTrader;
import com.icetea.lotus.Trader.CoinTraderFactory;
import com.icetea.lotus.entity.ExchangeOrder;
import com.icetea.lotus.entity.ExchangeOrderDetail;
import com.icetea.lotus.service.ExchangeOrderDetailService;
import com.icetea.lotus.service.ExchangeOrderService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class CoinTraderEvent implements ApplicationListener<ContextRefreshedEvent> {
    private final Logger log = LoggerFactory.getLogger(CoinTraderEvent.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    private final CoinTraderFactory coinTraderFactory;

    private final ExchangeOrderService exchangeOrderService;

    private final ExchangeOrderDetailService exchangeOrderDetailService;

    private final KafkaTemplate<String, String> kafkaTemplate;

    private final TraderInitializationStatus initializationStatus;

    @Override
    public void onApplicationEvent(@NotNull ContextRefreshedEvent contextRefreshedEvent) {
        log.info("======initialize coinTrader======");
        // coinTraderFactory.getTraderMap();
        Map<String, CoinTrader> traders = coinTraderFactory.getTraderMap();
        traders.forEach((symbol, trader) -> {
            log.info("======CoinTrader Process: {}======", symbol);
            List<ExchangeOrder> orders = exchangeOrderService.findAllTradingOrderBySymbol(symbol);
            log.info("Initialize: find all trading orders, total count( {} )", orders.size());
            List<ExchangeOrder> tradingOrders = new ArrayList<>();
            List<ExchangeOrder> completedOrders = new ArrayList<>();
            orders.forEach(order -> {
                BigDecimal tradedAmount = BigDecimal.ZERO;
                BigDecimal turnover = BigDecimal.ZERO;
                List<ExchangeOrderDetail> details = exchangeOrderDetailService.findAllByOrderId(order.getOrderId());

                for (ExchangeOrderDetail trade : details) {
                    tradedAmount = tradedAmount.add(trade.getAmount());
                    turnover = turnover.add(trade.getAmount().multiply(trade.getPrice()));
                }
                order.setTradedAmount(tradedAmount);
                order.setTurnover(turnover);
                if (!order.isCompleted()) {
                    tradingOrders.add(order);
                } else {
                    completedOrders.add(order);
                }
            });
            log.info("Initialize: tradingOrders total count({})", tradingOrders.size());
            try {
                trader.trade(tradingOrders);
            } catch (ParseException e) {
                log.info(e.toString());
                log.info("Abnormal：trader.trade(tradingOrders);");
            }
            // Determine the completed order send message notification
            if (!completedOrders.isEmpty()) {
                log.info("Initialize: completedOrders total count( {})", tradingOrders.size());
                try {
                    kafkaTemplate.send("exchange-order-completed", objectMapper.writeValueAsString(completedOrders));
                } catch (JsonProcessingException e) {
                    log.error("Error serializing completedOrders to JSON", e);
                }
            }
            trader.setReady(true);
        });

        // Set initialization status to true after all traders are initialized
        log.info("======coinTrader initialization completed======");
        initializationStatus.setInitialized(true);
    }

}
