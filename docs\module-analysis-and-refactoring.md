# Phân tích Module CEX-BE và Đề xuất Tách chuyển Logic Nghiệp vụ

## 1. <PERSON>ân tích cấu trúc Microservices hiện tại

### 1.1. **Active Microservices** (có main application class)
- **`future-core`**: Futures trading service (FuturesCoreApplication.java) - service name: "future"
- **`market`**: Market data service (MarketApplication.java) - service name: "market"
- **`wallet`**: Wallet management service (WalletApplication.java) - service name: "wallet"
- **`exchange`**: Spot trading service (ExchangeApplication.java) - service name: "exchange"
- **`exchange-api`**: Spot trading API (ExchangeApiApplication.java) - service name: "exchange-api"
- **`admin`**: Administrative service (AdminApplication.java) - service name: "admin"
- **`order-api`**: OTC trading API (OrderApiApplication.java) - service name: "order-api"
- **`external-api`**: External integrations API (ExternalApiApplication.java) - service name: "external-api"
- **`chat`**: Real-time messaging service (ChatApplication.java) - service name: "chat"

### 1.2. **Library Modules** (không có main class)
- **`core`**: Common utilities, base classes, constants, shared configurations
- **`exchange-core`**: Spot trading business logic library
- **`order-core`**: OTC trading business logic library

### 1.3. **Deprecated/Unused Modules**
- **`jobs`**: ❌ Quyết định bỏ không dùng
- **`netty`**: ❌ Quyết định bỏ không dùng
- **`hot-wallet`**: ⏸️ Tạm thời không phát triển thêm
- **`wallet-rpc`**: Blockchain RPC services (có thể integrate vào wallet service)

### 1.4. **Service Discovery & Communication**
- **Consul**: Service discovery và health checks
- **Kafka**: Async messaging giữa các services
- **WebSocket**: Real-time communication (STOMP over SockJS)

## 2. Phân tích Logic Nghiệp vụ trong Future-Core Service

### 2.1. **Core Trading Logic** (✅ Giữ trong future-core)
```
✅ GIỮ TRONG FUTURE-CORE:
- OrderMatchingEngineService: Matching engine logic
- PositionService: Position management
- LiquidationService: Liquidation logic
- FundingRateService: Funding rate calculations
- RiskManagementService: Risk assessment
- OrderService: Order lifecycle management
- TradeService: Trade execution and settlement
- ADLService: Auto-deleveraging logic
```

### 2.2. **Market Data Components** (🔄 Có thể tách ra)
```
🔄 HIỆN TẠI TRONG FUTURE-CORE (có thể tách):
- LastPriceService: Last price tracking cho futures
- IndexPriceService: Index price calculations
- MarkPriceService: Mark price calculations
- KLineManagementService: K-line cho futures contracts
- PriceCache: Caching giá futures

📍 MARKET SERVICE ĐÃ CÓ:
- MarketDataService: Market data cho spot trading
- KLineService: K-line cho spot markets
- WebsocketMarketHandler: Market data streaming
```

### 2.3. **WebSocket Components** (🔄 Đã có trong future-core)
```
✅ ĐÃ CÓ TRONG FUTURE-CORE:
- WebSocketConfig: STOMP configuration
- WebSocketMarketHandler: Market data streaming cho futures
- WebSocketPositionHandler: Position updates streaming
- PositionWebSocketController: Position WebSocket endpoints

❌ KHÔNG CẦN NETTY MODULE (đã quyết định bỏ)
```

### 2.4. **Wallet Integration** (🔄 Có thể tách ra)
```
🔄 HIỆN TẠI TRONG FUTURE-CORE:
- Wallet operations cho futures trading
- Balance management cho margin trading
- Transaction recording cho futures

📍 WALLET SERVICE ĐÃ CÓ:
- WalletService: Core wallet operations
- Balance management cho spot trading
- Deposit/Withdrawal processing
```

### 2.5. **Security Components** (🔄 Đã được loại bỏ)
```
✅ ĐÃ ĐƯỢC LOẠI BỎ KHỎI FUTURE-CORE:
- BruteForceProtectionService
- SignatureService
- AnomalyDetectionService
- JwtTokenProvider
- SecurityAuditService

📍 SECURITY ĐƯỢC XỬ LÝ BỞI:
- Core module: SecurityConfiguration, EnableCexResourceServer
- Individual services: Authentication filters
```

### 2.6. **Background Jobs & Scheduling** (🔄 Có thể tách ra)
```
🔄 HIỆN TẠI TRONG FUTURE-CORE:
- ADLScheduler: Auto-deleveraging scheduler
- LiquidationScheduler: Liquidation monitoring
- FundingScheduler: Funding rate calculations
- SettlementScheduler: Daily settlement
- HealthCheckScheduler: System health monitoring

❌ JOBS MODULE KHÔNG DÙNG (đã quyết định bỏ)
📍 CÓ THỂ CHUYỂN SANG: Admin service hoặc tạo dedicated scheduler service
```

## 3. Đề xuất Tối ưu hóa Kiến trúc hiện tại

### 3.1. **Future-Core Service (Optimized)**
```
future-core/ (MICROSERVICE)
├── core/domain/
│   ├── entity/           # Core entities: Order, Position, Trade
│   ├── valueobject/      # Value objects: Money, Symbol, OrderId
│   ├── service/          # Core business services
│   │   ├── OrderMatchingEngineService ✅
│   │   ├── PositionService ✅
│   │   ├── LiquidationService ✅
│   │   ├── FundingRateService ✅
│   │   ├── RiskManagementService ✅
│   │   └── ADLService ✅
│   └── repository/       # Repository interfaces
├── application/
│   ├── service/          # Application services
│   └── port/             # Use case interfaces
├── infrastructure/
│   ├── persistence/      # Database adapters
│   ├── messaging/        # Kafka producers/consumers
│   ├── matching/         # Matching engine implementations
│   └── websocket/        # WebSocket handlers cho futures ✅
└── FuturesCoreApplication.java
```

### 3.2. **Market Service (Enhanced)**
```
market/ (MICROSERVICE)
├── service/
│   ├── MarketDataService ✅ (spot market data)
│   ├── KLineService ✅ (spot K-lines)
│   ├── ExchangePushJob ✅ (real-time streaming)
│   └── 🔄 FuturesMarketDataService (NEW - từ future-core)
├── websocket/
│   ├── WebsocketMarketHandler ✅ (spot)
│   └── 🔄 FuturesMarketHandler (NEW - từ future-core)
├── integration/
│   ├── ExternalPriceProvider
│   └── SpotMarketIntegration ✅
├── storage/
│   ├── KLineRepository ✅
│   └── 🔄 FuturesPriceRepository (NEW)
└── MarketApplication.java ✅
```

### 3.3. **Wallet Service (Enhanced)**
```
wallet/ (MICROSERVICE)
├── service/
│   ├── WalletService ✅
│   ├── TestService ✅ (RPC health checks)
│   └── 🔄 FuturesWalletService (NEW - từ future-core)
├── integration/
│   ├── wallet-rpc/ ✅ (blockchain integration)
│   └── 🔄 FuturesBalanceService (NEW)
├── repository/
│   ├── WalletRepository ✅
│   └── 🔄 FuturesTransactionRepository (NEW)
└── WalletApplication.java ✅
```

### 3.4. **Admin Service (Enhanced)**
```
admin/ (MICROSERVICE)
├── service/
│   ├── AdminService ✅
│   ├── SystemManagementService ✅
│   └── 🔄 SchedulerService (NEW - từ future-core)
├── scheduler/
│   ├── 🔄 LiquidationScheduler (từ future-core)
│   ├── 🔄 FundingScheduler (từ future-core)
│   ├── 🔄 SettlementScheduler (từ future-core)
│   └── 🔄 HealthCheckScheduler (từ future-core)
├── monitoring/
│   ├── PerformanceMonitoringService
│   └── AuditLogService
└── AdminApplication.java ✅
```

### 3.5. **Chat Service (Enhanced)**
```
chat/ (MICROSERVICE)
├── service/
│   ├── ChatService ✅
│   ├── NotificationService ✅
│   └── 🔄 TradingNotificationService (NEW)
├── websocket/
│   ├── WebSocketController ✅
│   └── 🔄 TradingAlertHandler (NEW)
├── integration/
│   ├── EmailService ✅
│   ├── SMSService ✅
│   └── PushNotificationService ✅
└── ChatApplication.java ✅
```

## 4. Migration Plan (Revised)

### Phase 1: Extract Futures Market Data Services
**Mục tiêu**: Di chuyển futures market data từ future-core sang market service
1. **Tạo futures market package trong market service**
2. **Di chuyển các service:**
   - LastPriceService (futures-specific)
   - IndexPriceService
   - MarkPriceService
   - KLineManagementService (futures contracts)
3. **Tạo REST API endpoints trong market service**
4. **Cập nhật future-core để call market service qua HTTP/Kafka**
5. **Test integration và performance**

### Phase 2: Extract Background Schedulers
**Mục tiêu**: Di chuyển schedulers từ future-core sang admin service
1. **Tạo scheduler package trong admin service**
2. **Di chuyển các scheduler:**
   - LiquidationScheduler
   - FundingScheduler
   - SettlementScheduler
   - HealthCheckScheduler
3. **Configure scheduling trong admin service**
4. **Setup monitoring và alerting**
5. **Test scheduled operations**

### Phase 3: Extract Futures Wallet Operations
**Mục tiêu**: Di chuyển futures wallet logic sang wallet service
1. **Enhance wallet service với futures support**
2. **Di chuyển các service:**
   - Futures balance management
   - Margin calculations
   - Futures transaction recording
3. **Tạo futures-specific APIs trong wallet service**
4. **Cập nhật future-core integration**
5. **Test wallet operations và margin calculations**

### Phase 4: Enhance Chat Service với Trading Notifications
**Mục tiêu**: Centralize tất cả notifications trong chat service
1. **Tạo trading notification package trong chat service**
2. **Di chuyển notification logic từ future-core**
3. **Implement real-time trading alerts**
4. **Setup email/SMS notifications cho trading events**
5. **Test notification delivery**

## 5. Benefits của việc tối ưu hóa

### 5.1. **Improved Separation of Concerns**
- **Future-core**: Tập trung vào core trading logic
- **Market service**: Unified market data cho cả spot và futures
- **Wallet service**: Unified wallet operations cho tất cả trading types
- **Admin service**: Centralized system management và scheduling
- **Chat service**: Unified notification và communication

### 5.2. **Better Resource Utilization**
- **Market service**: Scale theo market data load
- **Future-core**: Scale theo trading volume
- **Admin service**: Lightweight cho background tasks
- **Wallet service**: Scale theo user activity

### 5.3. **Enhanced Maintainability**
- Mỗi service có clear boundaries
- Easier debugging và troubleshooting
- Independent deployment cycles
- Reduced blast radius cho changes

### 5.4. **Leveraging Existing Infrastructure**
- **Consul**: Service discovery đã có sẵn
- **Kafka**: Async messaging đã được setup
- **WebSocket**: STOMP infrastructure đã có
- **Database**: Shared PostgreSQL với proper isolation

## 6. Implementation Strategy

### 6.1. **Service Communication**
```
✅ ĐÃ CÓ:
- Consul service discovery
- Kafka async messaging
- REST API calls với LoadBalanced RestTemplate
- WebSocket STOMP messaging

🔄 CẦN ENHANCE:
- Circuit breakers cho service calls
- Retry mechanisms
- Timeout configurations
```

### 6.2. **Data Management**
```
✅ ĐÃ CÓ:
- PostgreSQL cho persistent data
- Redis cho caching
- MongoDB cho K-line data (market service)

🔄 CẦN OPTIMIZE:
- Database connection pooling
- Cache strategies
- Data partitioning
```

### 6.3. **Monitoring & Observability**
```
✅ ĐÃ CÓ:
- Health checks qua Consul
- Actuator endpoints
- Logging infrastructure

🔄 CẦN ADD:
- Distributed tracing
- Metrics collection
- Performance monitoring
```

## 7. Migration Approach

### 7.1. **Gradual Migration**
- **Phase-by-phase approach** để minimize risk
- **Feature flags** để enable/disable new functionality
- **Parallel running** old và new implementations
- **Gradual traffic shifting**

### 7.2. **Backward Compatibility**
- Maintain existing APIs during migration
- Deprecation notices cho old endpoints
- Migration guides cho clients
- Support period cho old APIs

### 7.3. **Testing Strategy**
- **Unit tests** cho individual services
- **Integration tests** cho service communication
- **End-to-end tests** cho critical trading flows
- **Performance tests** để ensure no regression

## 8. Success Metrics

### 8.1. **Performance Metrics**
- **Latency**: < 100ms cho market data queries
- **Throughput**: Support current trading volume
- **Availability**: 99.9% uptime cho critical services

### 8.2. **Development Metrics**
- **Build time**: Reduced build times cho individual services
- **Deployment frequency**: Faster deployment cycles
- **Mean time to recovery**: Faster issue resolution

### 8.3. **Business Metrics**
- **Zero downtime** during migration
- **No data loss** during migration
- **Maintained trading performance**

Việc tối ưu hóa này sẽ tận dụng infrastructure microservices đã có để tạo ra một kiến trúc clean hơn, maintainable hơn, và scalable hơn mà không cần thay đổi fundamental architecture.
