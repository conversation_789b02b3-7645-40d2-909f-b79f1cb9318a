package com.icetea.lotus.job;

import com.icetea.lotus.entity.Order;
import com.icetea.lotus.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class CheckOrderTask {
    @Autowired
    private OrderService orderService;

    @Scheduled(fixedRate = 60000)
    public void checkExpireOrder() {
        log.info("=========Start checking for overdue orders===========");
        List<Order> list = orderService.checkExpiredOrder();
        list.stream().forEach(x -> {
                    try {
                        orderService.cancelOrderTask(x);
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.warn("Order number {}: Automatic cancellation failed", x.getOrderSn());
                    }
                }
        );
        log.info("=========Check Expired Orders End===========");
    }
}
