//package com.icetea.lotus.config;
//
//import com.querydsl.jpa.impl.JPAQueryFactory;
//import jakarta.persistence.EntityManager;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * <AUTHOR>
// * @description
// * @date 2018/1/18 11:29
// */
//@Configuration
//public class JPAQueryConfig {
//    @Bean
//    public JPAQueryFactory getJPAQueryFactory(EntityManager entityManager){
//        return new JPAQueryFactory(entityManager);
//    }
//}
