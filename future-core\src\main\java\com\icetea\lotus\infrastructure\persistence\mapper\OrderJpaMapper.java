package com.icetea.lotus.infrastructure.persistence.mapper;

import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.persistence.entity.OrderJpaEntity;
import com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.math.BigDecimal;

/**
 * Mapper để chuyển đổi giữa Order và OrderJpaEntity
 */
@Mapper(componentModel = "spring")
public interface OrderJpaMapper {

    /**
     * Chuyển đổi từ OrderJpaEntity sang Order
     * @param entity OrderJpaEntity
     * @return Order
     */
    @Mapping(target = "orderId", source = "orderId", qualifiedByName = "stringToOrderId")
    @Mapping(target = "memberId", source = "memberId")
    @Mapping(target = "contractId", source = "contractId")
    @Mapping(target = "symbol", source = "symbol", qualifiedByName = "stringToSymbol")
    @Mapping(target = "coinSymbol", source = "coinSymbol")
    @Mapping(target = "baseSymbol", source = "baseSymbol")
    @Mapping(target = "direction", source = "direction")
    @Mapping(target = "type", source = "type")
    @Mapping(target = "price", source = "price", qualifiedByName = "bigDecimalToMoney")
    @Mapping(target = "triggerPrice", source = "triggerPrice", qualifiedByName = "bigDecimalToMoney")
    @Mapping(target = "triggerType", source = "triggerType")
    @Mapping(target = "volume", source = "volume")
    @Mapping(target = "dealVolume", source = "dealVolume")
    @Mapping(target = "dealMoney", source = "dealMoney", qualifiedByName = "bigDecimalToMoney")
    @Mapping(target = "fee", source = "fee", qualifiedByName = "bigDecimalToMoney")
    @Mapping(target = "status", source = "status")
    @Mapping(target = "createTime", source = "createTime")
    @Mapping(target = "completeTime", source = "completeTime")
    @Mapping(target = "canceledTime", source = "canceledTime")
    @Mapping(target = "executeTime", source = "executeTime")
    @Mapping(target = "expireTime", source = "expireTime")
    @Mapping(target = "timeInForce", source = "timeInForce")
    @Mapping(target = "leverage", source = "leverage")
    @Mapping(target = "reduceOnly", source = "reduceOnly")
    @Mapping(target = "liquidation", source = "liquidation")
    @Mapping(target = "adl", source = "adl")
    @Mapping(target = "implied", source = "implied")
    @Mapping(target = "sourceOrderId", source = "sourceOrderId")
    @Mapping(target = "ocoId", source = "ocoId")
    @Mapping(target = "ocoOrderNo", source = "ocoOrderNo")
    @Mapping(target = "callbackRate", source = "callbackRate")
    @Mapping(target = "activationPrice", source = "activationPrice", qualifiedByName = "bigDecimalToMoney")
    @Mapping(target = "postOnly", source = "postOnly")
    @Mapping(target = "cancelReason", source = "cancelReason")
    @Mapping(target = "maxSlippage", source = "maxSlippage")
    @Mapping(target = "fillOrKill", source = "fillOrKill")
    @Mapping(target = "immediateOrCancel", source = "immediateOrCancel")
    Order toDomain(OrderJpaEntity entity);

    /**
     * Alias cho phương thức toDomain
     * @param entity OrderJpaEntity
     * @return Order
     */
    default Order jpaToDomain(OrderJpaEntity entity) {
        return toDomain(entity);
    }

    /**
     * Chuyển đổi từ Order sang OrderJpaEntity
     * @param domain Order
     * @return OrderJpaEntity
     */
    @Mapping(target = "orderId", source = "orderId", qualifiedByName = "orderIdToString")
    @Mapping(target = "memberId", source = "memberId")
    @Mapping(target = "contractId", source = "contractId")
    @Mapping(target = "symbol", source = "symbol", qualifiedByName = "symbolToString")
    @Mapping(target = "coinSymbol", source = "coinSymbol")
    @Mapping(target = "baseSymbol", source = "baseSymbol")
    @Mapping(target = "direction", source = "direction")
    @Mapping(target = "type", source = "type")
    @Mapping(target = "price", source = "price", qualifiedByName = "moneyToBigDecimal")
    @Mapping(target = "triggerPrice", source = "triggerPrice", qualifiedByName = "moneyToBigDecimal")
    @Mapping(target = "triggerType", source = "triggerType")
    @Mapping(target = "volume", source = "volume")
    @Mapping(target = "dealVolume", source = "dealVolume")
    @Mapping(target = "dealMoney", source = "dealMoney", qualifiedByName = "moneyToBigDecimal")
    @Mapping(target = "fee", source = "fee", qualifiedByName = "moneyToBigDecimal")
    @Mapping(target = "status", source = "status")
    @Mapping(target = "createTime", source = "createTime")
    @Mapping(target = "completeTime", source = "completeTime")
    @Mapping(target = "canceledTime", source = "canceledTime")
    @Mapping(target = "executeTime", source = "executeTime")
    @Mapping(target = "expireTime", source = "expireTime")
    @Mapping(target = "timeInForce", source = "timeInForce")
    @Mapping(target = "leverage", source = "leverage")
    @Mapping(target = "reduceOnly", source = "reduceOnly")
    @Mapping(target = "liquidation", source = "liquidation")
    @Mapping(target = "adl", source = "adl")
    @Mapping(target = "implied", source = "implied")
    @Mapping(target = "sourceOrderId", source = "sourceOrderId")
    @Mapping(target = "ocoId", source = "ocoId")
    @Mapping(target = "ocoOrderNo", source = "ocoOrderNo")
    @Mapping(target = "callbackRate", source = "callbackRate")
    @Mapping(target = "activationPrice", source = "activationPrice", qualifiedByName = "moneyToBigDecimal")
    @Mapping(target = "postOnly", source = "postOnly")
    @Mapping(target = "cancelReason", source = "cancelReason")
    @Mapping(target = "maxSlippage", source = "maxSlippage")
    @Mapping(target = "fillOrKill", source = "fillOrKill")
    @Mapping(target = "immediateOrCancel", source = "immediateOrCancel")
    OrderJpaEntity toEntity(Order domain);

    /**
     * Alias cho phương thức toEntity
     * @param domain Order
     * @return OrderJpaEntity
     */
    default OrderJpaEntity domainToJpa(Order domain) {
        return toEntity(domain);
    }

    /**
     * Chuyển đổi từ String sang OrderId
     * @param value String
     * @return OrderId
     */
    @Named("stringToOrderId")
    default OrderId stringToOrderId(String value) {
        if (value == null) {
            return null;
        }
        return OrderId.of(value);
    }

    /**
     * Chuyển đổi từ OrderId sang String
     * @param value OrderId
     * @return String
     */
    @Named("orderIdToString")
    default String orderIdToString(OrderId value) {
        if (value == null) {
            return null;
        }
        return value.getValue();
    }

    /**
     * Chuyển đổi từ String sang Symbol
     * @param value String
     * @return Symbol
     */
    @Named("stringToSymbol")
    default Symbol stringToSymbol(String value) {
        if (value == null) {
            return null;
        }
        return Symbol.of(value);
    }

    /**
     * Chuyển đổi từ Symbol sang String
     * @param value Symbol
     * @return String
     */
    @Named("symbolToString")
    default String symbolToString(Symbol value) {
        if (value == null) {
            return null;
        }
        return value.getValue();
    }

    /**
     * Chuyển đổi từ BigDecimal sang Money
     * @param value BigDecimal
     * @return Money
     */
    @Named("bigDecimalToMoney")
    default Money bigDecimalToMoney(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return Money.of(value);
    }

    /**
     * Chuyển đổi từ Money sang BigDecimal với validation
     * @param value Money
     * @return BigDecimal
     */
    @Named("moneyToBigDecimal")
    default BigDecimal moneyToBigDecimal(Money value) {
        if (value == null) {
            return null;
        }
        return BigDecimalValidator.validateAndScale(value.getValue(), "money");
    }
}
