//package com.icetea.lotus.config;
//
//import com.alibaba.druid.support.http.StatViewServlet;
//import com.alibaba.druid.support.http.WebStatFilter;
//import jakarta.servlet.Filter;
//import jakarta.servlet.Servlet;
//import org.springframework.boot.web.servlet.FilterRegistrationBean;
//import org.springframework.boot.web.servlet.ServletRegistrationBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//public class DruidConfig {
//    /**
//     * Register a StatViewServlet
//     *
//     * @return
//     */
//    @Bean
//    public ServletRegistrationBean DruidStatViewServlet() {
//        ServletRegistrationBean<Servlet> servletRegistrationBean = new ServletRegistrationBean<>(new StatViewServlet(), "/my/druid/*");
//        // Add initialization parameters：initParams
//        // Whitelist：
//        servletRegistrationBean.addInitParameter("allow", "**************");
//        // IP blacklist (if there is a common IP, deny takes precedence over allow):
//        // If deny is satisfied, it will prompt: Sorry, you are not permitted to view this page.
//        servletRegistrationBean.addInitParameter("deny", "************");
//        // Login to view the information.
//        servletRegistrationBean.addInitParameter("loginUsername", "admin");
//        servletRegistrationBean.addInitParameter("loginPassword", "654321");
//        // Is it possible to reset the data.
//        servletRegistrationBean.addInitParameter("resetEnable", "false");
//        return servletRegistrationBean;
//    }
//
//    /**
//     * Register one: filterRegistrationBean
//     *
//     * @return
//     */
//    @Bean
//    public FilterRegistrationBean druidStatFilter() {
//        FilterRegistrationBean<Filter> filterRegistrationBean = new FilterRegistrationBean<>(new WebStatFilter());
//        // Add filter rules.
//        filterRegistrationBean.addUrlPatterns("/*");
//        // Add format information that does not need to be ignored.
//        filterRegistrationBean.addInitParameter("exclusions", "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/my/druid/*");
//        return filterRegistrationBean;
//    }
//}
