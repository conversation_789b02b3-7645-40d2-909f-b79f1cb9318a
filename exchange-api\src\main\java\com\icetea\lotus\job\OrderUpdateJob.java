package com.icetea.lotus.job;


import com.alibaba.fastjson.JSON;
import com.icetea.lotus.entity.ExchangeCoin;
import com.icetea.lotus.entity.ExchangeOrder;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.service.ExchangeOrderService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class OrderUpdateJob {
    private final ExchangeOrderService orderService;
    private final ExchangeCoinService coinService;
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final Logger logger = LoggerFactory.getLogger(OrderUpdateJob.class);

    // Check timed orders every 5 minutes
    @Scheduled(fixedRate = 300 * 1000)
    public void autoCancelOrder() {
        logger.info("start autoCancelOrder...");
        List<ExchangeCoin> coinList = coinService.findAllEnabled();
        coinList.forEach(coin -> {
            if (coin.getMaxTradingTime() > 0) {
                List<ExchangeOrder> orders = orderService.findOvertimeOrder(coin.getSymbol(), coin.getMaxTradingTime());
                orders.forEach(order -> {
                    // Send messages to the Exchange system
                    kafkaTemplate.send("exchange-order-cancel", JSON.toJSONString(order));
                    logger.info("orderId: {}, time:{}", order.getOrderId(), order.getTime());
                });
            }
        });
        logger.info("end autoCancelOrder...");
    }


}
