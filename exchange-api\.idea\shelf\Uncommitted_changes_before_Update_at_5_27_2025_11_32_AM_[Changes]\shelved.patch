Index: .settings/org.eclipse.jdt.core.prefs
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>eclipse.preferences.version=1\r\norg.eclipse.jdt.core.compiler.codegen.targetPlatform=1.8\r\norg.eclipse.jdt.core.compiler.compliance=1.8\r\norg.eclipse.jdt.core.compiler.problem.forbiddenReference=warning\r\norg.eclipse.jdt.core.compiler.release=disabled\r\norg.eclipse.jdt.core.compiler.source=1.8\r\n
===================================================================
diff --git a/.settings/org.eclipse.jdt.core.prefs b/.settings/org.eclipse.jdt.core.prefs
--- a/.settings/org.eclipse.jdt.core.prefs	(revision 9f4753afafcd0b93f0875fb2a7eb16a4b0ffea24)
+++ b/.settings/org.eclipse.jdt.core.prefs	(date 1748250923822)
@@ -1,6 +1,9 @@
 eclipse.preferences.version=1
 org.eclipse.jdt.core.compiler.codegen.targetPlatform=1.8
 org.eclipse.jdt.core.compiler.compliance=1.8
+org.eclipse.jdt.core.compiler.problem.enablePreviewFeatures=disabled
 org.eclipse.jdt.core.compiler.problem.forbiddenReference=warning
+org.eclipse.jdt.core.compiler.problem.reportPreviewFeatures=ignore
+org.eclipse.jdt.core.compiler.processAnnotations=enabled
 org.eclipse.jdt.core.compiler.release=disabled
 org.eclipse.jdt.core.compiler.source=1.8
Index: .settings/org.eclipse.core.resources.prefs
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>eclipse.preferences.version=1\r\nencoding//src/main/java=UTF-8\r\nencoding//src/main/resources/dev=UTF-8\r\nencoding//src/main/resources/prod=UTF-8\r\nencoding//src/main/resources/test=UTF-8\r\nencoding/<project>=UTF-8\r\n
===================================================================
diff --git a/.settings/org.eclipse.core.resources.prefs b/.settings/org.eclipse.core.resources.prefs
--- a/.settings/org.eclipse.core.resources.prefs	(revision 9f4753afafcd0b93f0875fb2a7eb16a4b0ffea24)
+++ b/.settings/org.eclipse.core.resources.prefs	(date 1748250923463)
@@ -1,5 +1,6 @@
 eclipse.preferences.version=1
 encoding//src/main/java=UTF-8
+encoding//src/main/resources=UTF-8
 encoding//src/main/resources/dev=UTF-8
 encoding//src/main/resources/prod=UTF-8
 encoding//src/main/resources/test=UTF-8
