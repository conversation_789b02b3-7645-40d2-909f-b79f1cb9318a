spring:
  zipkin:
    base-url: http://************:9411
    enabled: true
    sender:
      type: web

  sleuth:
    sampler:
      percentage: 1.0
      probability: 1.0
    enabled: true

  datasource:
    driver-class-name: org.postgresql.Driver
    url: ${DATASOURCE_URL:*******************************************************************}
    username: ${DATASOURCE_USERNAME:lotus_root}
    password: ${DATASOURCE_PASSWORD:5mxFmM53B0BnKli34RlhfyXBjVf2A4TDMzd1yRRHtVgIieJrw2IRpOnEfSLVJ3si}
    hikari:
      minimum-idle: 1
      maximum-pool-size: 2
  jpa:
    properties:
      hibernate:
        default_schema: ${DEFAULT_SCHEMA:public}
    show-sql: ${SHOW_SQL:true}
    database: postgresql

  data:
    mongodb:
      uri: ${SPRING_MONGODB_URI:****************************************************}
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:2m0881Xc30Wh}

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:9092}
    consumer:
      group.id: ${KAFKA_CONSUMER_GROUP_ID:default-group}
    properties:
      #      security.protocol: SASL_PLAINTEXT
      sasl.mechanism: PLAIN
      #      sasl.jaas.config: >-
      #        org.apache.kafka.common.security.plain.PlainLoginModule required username=""
      #        password="2m0881Xc30Wh";

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30500}

  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: http://************:8082/realms/cex-lotus
      client:
        registration:
          keycloak:
            client-id: internal-service
            client-secret: X9EopoZ44E0gmdBrVpia8zZI9xDsR37e
            authorization-grant-type: client_credentials
            scope: openid
        provider:
          keycloak:
            issuer-uri: http://************:8082/realms/cex-lotus
            token-uri: http://************:8082/realms/cex-lotus/protocol/openid-connect/token

topic-kafka:
  exchange:
    order: ${EXCHANGE_ORDER:exchange-order}
    order-cancel-success: ${EXCHANGE_ORDER_CANCEL_SUCCESS:exchange-order-cancel-success}
    order-completed: ${EXCHANGE_ORDER_COMPLETED:exchange-order-completed}
    order-cancel: ${EXCHANGE_ORDER_CANCEL:exchange-order-cancel}
    trade: ${EXCHANGE_TRADE:exchange-trade}
    trade-plate: ${EXCHANGE_TRADE_PLATE:exchange-trade-plate}

keycloak:
  auth-server-url: http://************:8082
  realm: cex-lotus
  resource: cex-exchange
  credentials:
    secret: Okc6TpmuPBeySygoiUXTtsRKzdvC1sg0
cex-security:
  resource-server-enabled: true
  permit-all-endpoints:
    - "/**"
    - "/exchange/actuator/health"
    - "/actuator/health"