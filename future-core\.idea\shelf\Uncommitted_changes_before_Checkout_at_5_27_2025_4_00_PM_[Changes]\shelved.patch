Index: src/main/java/com/icetea/lotus/infrastructure/service/LiquidationCheckService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.icetea.lotus.infrastructure.service;\r\n\r\nimport com.icetea.lotus.core.domain.entity.Contract;\r\nimport com.icetea.lotus.core.domain.entity.Position;\r\nimport com.icetea.lotus.core.domain.entity.PositionStatus;\r\nimport com.icetea.lotus.core.domain.repository.ContractRepository;\r\nimport com.icetea.lotus.core.domain.repository.PositionRepository;\r\nimport com.icetea.lotus.core.domain.service.LiquidationService;\r\nimport com.icetea.lotus.core.domain.service.PriceManagementService;\r\nimport com.icetea.lotus.core.domain.valueobject.Money;\r\nimport com.icetea.lotus.core.domain.valueobject.Symbol;\r\nimport com.icetea.lotus.infrastructure.service.PositionDiagnosticService;\r\n\r\nimport lombok.extern.slf4j.Slf4j;\r\n\r\nimport org.springframework.beans.factory.annotation.Autowired;\r\nimport org.springframework.beans.factory.annotation.Qualifier;\r\nimport org.springframework.beans.factory.annotation.Value;\r\nimport org.springframework.scheduling.annotation.Scheduled;\r\nimport org.springframework.stereotype.Service;\r\nimport org.springframework.transaction.annotation.Transactional;\r\n\r\nimport java.util.List;\r\nimport java.util.Optional;\r\n\r\n/**\r\n * Service kiểm tra thanh lý vị thế\r\n * Chạy định kỳ để kiểm tra các vị thế cần thanh lý\r\n */\r\n@Slf4j\r\n@Service\r\npublic class LiquidationCheckService {\r\n\r\n    private final PositionRepository positionRepository;\r\n    private final ContractRepository contractRepository;\r\n    private final LiquidationService liquidationService;\r\n    private final PriceManagementService priceManagementService;\r\n    private final PositionDiagnosticService positionDiagnosticService;\r\n\r\n    @Autowired\r\n    public LiquidationCheckService(\r\n            @Qualifier(\"positionRepositoryAdapter\") PositionRepository positionRepository,\r\n            ContractRepository contractRepository,\r\n            LiquidationService liquidationService,\r\n            PriceManagementService priceManagementService,\r\n            PositionDiagnosticService positionDiagnosticService) {\r\n        this.positionRepository = positionRepository;\r\n        this.contractRepository = contractRepository;\r\n        this.liquidationService = liquidationService;\r\n        this.priceManagementService = priceManagementService;\r\n        this.positionDiagnosticService = positionDiagnosticService;\r\n    }\r\n\r\n    @Value(\"${contract.liquidation.check-interval:5000}\")\r\n    private long checkInterval;\r\n\r\n    @Value(\"${contract.liquidation.max-positions-per-batch:100}\")\r\n    private int maxPositionsPerBatch;\r\n\r\n    @Value(\"${contract.liquidation.enabled:true}\")\r\n    private boolean liquidationEnabled;\r\n\r\n    /**\r\n     * Kiểm tra thanh lý cho một symbol cụ thể\r\n     * @param symbol Symbol cần kiểm tra\r\n     */\r\n    @Transactional\r\n    public void checkLiquidationForSymbol(String symbol) {\r\n        if (!liquidationEnabled) {\r\n            log.debug(\"Thanh lý vị thế đã bị vô hiệu hóa trong cấu hình\");\r\n            return;\r\n        }\r\n\r\n        log.info(\"Kiểm tra thanh lý cho symbol: {}\", symbol);\r\n\r\n        try {\r\n            // Lấy giá đánh dấu hiện tại\r\n            Money markPrice = priceManagementService.getCurrentMarkPrice(Symbol.of(symbol));\r\n            if (markPrice == null || markPrice.getValue().compareTo(java.math.BigDecimal.ZERO) <= 0) {\r\n                log.warn(\"Không có giá đánh dấu hợp lệ cho symbol: {}\", symbol);\r\n                return;\r\n            }\r\n\r\n            // Lấy hợp đồng\r\n            Optional<Contract> contractOpt = contractRepository.findBySymbolOptional(Symbol.of(symbol));\r\n            if (contractOpt.isEmpty()) {\r\n                log.warn(\"Không tìm thấy hợp đồng cho symbol: {}\", symbol);\r\n                return;\r\n            }\r\n            Contract contract = contractOpt.get();\r\n\r\n            // Lấy các vị thế đang mở cho symbol này\r\n            List<Position> riskPositions = positionRepository.findAllBySymbolAndStatus(Symbol.of(symbol), PositionStatus.OPEN);\r\n\r\n            if (riskPositions.isEmpty()) {\r\n                log.debug(\"Không có vị thế đang mở nào cho symbol: {}\", symbol);\r\n                return;\r\n            }\r\n\r\n            // Giới hạn số lượng vị thế kiểm tra trong một lần\r\n            if (riskPositions.size() > maxPositionsPerBatch) {\r\n                riskPositions = riskPositions.subList(0, maxPositionsPerBatch);\r\n            }\r\n\r\n            if (riskPositions.isEmpty()) {\r\n                log.debug(\"Không có vị thế nào có nguy cơ cao cho symbol: {}\", symbol);\r\n                return;\r\n            }\r\n\r\n            log.debug(\"Kiểm tra {} vị thế có nguy cơ cao cho symbol: {}\", riskPositions.size(), symbol);\r\n\r\n            // Kiểm tra từng vị thế\r\n            for (Position position : riskPositions) {\r\n                checkAndLiquidatePositionIfNeeded(position, markPrice, contract, symbol);\r\n            }\r\n\r\n            log.info(\"Đã hoàn thành kiểm tra thanh lý cho symbol: {}\", symbol);\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi kiểm tra thanh lý cho symbol: {}\", symbol, e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Kiểm tra và thanh lý vị thế nếu cần\r\n     * @param position Vị thế cần kiểm tra\r\n     * @param markPrice Giá đánh dấu hiện tại\r\n     * @param contract Hợp đồng\r\n     * @param symbol Symbol\r\n     */\r\n    private void checkAndLiquidatePositionIfNeeded(Position position, Money markPrice, Contract contract, String symbol) {\r\n        try {\r\n            // Kiểm tra xem vị thế có cần thanh lý không\r\n            boolean needsLiquidation = liquidationService.needsLiquidation(position, markPrice, contract);\r\n\r\n            if (needsLiquidation) {\r\n                log.warn(\"Vị thế cần thanh lý, positionId = {}, memberId = {}, symbol = {}, markPrice = {}, margin = {}, maintenanceMarginRatio = {}\",\r\n                        position.getId(), position.getMemberId(), symbol, markPrice,\r\n                        position.getMargin(), contract.getMaintenanceMarginRatio());\r\n\r\n                liquidatePosition(position, markPrice, contract, symbol);\r\n            }\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi kiểm tra thanh lý cho vị thế, positionId = {}, memberId = {}, symbol = {}\",\r\n                    position.getId(), position.getMemberId(), symbol, e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Thanh lý vị thế\r\n     * @param position Vị thế cần thanh lý\r\n     * @param markPrice Giá đánh dấu hiện tại\r\n     * @param contract Hợp đồng\r\n     * @param symbol Symbol\r\n     */\r\n    private void liquidatePosition(Position position, Money markPrice, Contract contract, String symbol) {\r\n        try {\r\n            // Chẩn đoán vị thế trước khi thanh lý\r\n            positionDiagnosticService.logPositionDetails(position, markPrice);\r\n\r\n            if (!positionDiagnosticService.canLiquidate(position, markPrice)) {\r\n                log.warn(\"Vị thế không thể thanh lý theo kết quả chẩn đoán, positionId = {}, memberId = {}, symbol = {}\",\r\n                        position.getId(), position.getMemberId(), symbol);\r\n                return;\r\n            }\r\n\r\n            // Log thông tin trước khi thanh lý\r\n            log.info(\"Bắt đầu thanh lý vị thế, positionId = {}, memberId = {}, symbol = {}, status = {}, volume = {}, margin = {}, markPrice = {}\",\r\n                    position.getId(), position.getMemberId(), symbol, position.getStatus(),\r\n                    position.getVolume(), position.getMargin() != null ? position.getMargin().getValue() : null, markPrice.getValue());\r\n\r\n            // Kiểm tra trạng thái vị thế trước khi thanh lý\r\n            if (position.getStatus() != null && position.getStatus() != PositionStatus.OPEN) {\r\n                log.warn(\"Vị thế không ở trạng thái OPEN, bỏ qua thanh lý, positionId = {}, memberId = {}, symbol = {}, status = {}\",\r\n                        position.getId(), position.getMemberId(), symbol, position.getStatus());\r\n                return;\r\n            }\r\n\r\n            // Thanh lý vị thế\r\n            Position liquidatedPosition = liquidationService.liquidatePosition(position, markPrice, contract);\r\n\r\n            // Kiểm tra xem liquidatedPosition có null không\r\n            if (liquidatedPosition == null) {\r\n                log.error(\"Thanh lý vị thế thất bại - liquidatedPosition trả về null, \" +\r\n                        \"positionId = {}, memberId = {}, symbol = {}, originalStatus = {}, volume = {}, margin = {}\",\r\n                        position.getId(), position.getMemberId(), symbol, position.getStatus(),\r\n                        position.getVolume(), position.getMargin() != null ? position.getMargin().getValue() : null);\r\n\r\n                // Có thể cần cập nhật trạng thái vị thế để tránh thanh lý lại\r\n                markPositionAsLiquidationFailed(position);\r\n                return;\r\n            }\r\n\r\n            // Kiểm tra số tiền ký quỹ còn lại\r\n            if (liquidatedPosition.getRemainingMargin() != null) {\r\n                if (liquidatedPosition.getRemainingMargin().isLessThan(Money.ZERO)) {\r\n                    log.warn(\"Vị thế thanh lý cần sử dụng quỹ bảo hiểm, positionId = {}, memberId = {}, symbol = {}, amount = {}\",\r\n                            liquidatedPosition.getId(), liquidatedPosition.getMemberId(), symbol,\r\n                            liquidatedPosition.getRemainingMargin().getValue().abs());\r\n                } else if (liquidatedPosition.getRemainingMargin().isGreaterThan(Money.ZERO)) {\r\n                    log.info(\"Vị thế thanh lý có số tiền ký quỹ còn lại, positionId = {}, memberId = {}, symbol = {}, amount = {}\",\r\n                            liquidatedPosition.getId(), liquidatedPosition.getMemberId(), symbol,\r\n                            liquidatedPosition.getRemainingMargin().getValue());\r\n                }\r\n            }\r\n\r\n            log.info(\"Đã thanh lý vị thế thành công, positionId = {}, memberId = {}, symbol = {}, newStatus = {}\",\r\n                    liquidatedPosition.getId(), liquidatedPosition.getMemberId(), symbol, liquidatedPosition.getStatus());\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi thanh lý vị thế, positionId = {}, memberId = {}, symbol = {}, errorType = {}, errorMessage = {}\",\r\n                    position.getId(), position.getMemberId(), symbol, e.getClass().getSimpleName(), e.getMessage(), e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Đánh dấu vị thế là thanh lý thất bại để tránh thanh lý lại\r\n     * @param position Vị thế\r\n     */\r\n    private void markPositionAsLiquidationFailed(Position position) {\r\n        try {\r\n            // Có thể cập nhật status hoặc thêm remark để tránh thanh lý lại\r\n            log.warn(\"Đánh dấu vị thế thanh lý thất bại, positionId = {}, memberId = {}, symbol = {}\",\r\n                    position.getId(), position.getMemberId(), position.getSymbol().getValue());\r\n\r\n            // TODO: Implement logic để đánh dấu position hoặc ghi vào blacklist\r\n            // Ví dụ: có thể set status thành LIQUIDATION_FAILED hoặc thêm vào cache\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi đánh dấu vị thế thanh lý thất bại, positionId = {}\", position.getId(), e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Kiểm tra thanh lý cho tất cả các symbol\r\n     * Chạy định kỳ theo cấu hình contract.liquidation.check-interval\r\n     */\r\n    @Scheduled(fixedRateString = \"${contract.liquidation.check-interval:5000}\")\r\n    public void checkLiquidationForAllSymbols() {\r\n        if (!liquidationEnabled) {\r\n            log.debug(\"Thanh lý vị thế đã bị vô hiệu hóa trong cấu hình\");\r\n            return;\r\n        }\r\n\r\n        log.debug(\"Bắt đầu kiểm tra thanh lý cho tất cả các symbol\");\r\n\r\n        try {\r\n            // Lấy tất cả các hợp đồng và giá hiện tại trong một lần truy vấn\r\n            List<Contract> contracts = contractRepository.findAll();\r\n\r\n            // Lấy giá hiện tại cho tất cả các symbol\r\n            java.util.Map<Symbol, Money> currentPrices = new java.util.HashMap<>();\r\n            for (Contract contract : contracts) {\r\n                Symbol symbol = contract.getSymbol();\r\n                Money markPrice = priceManagementService.getCurrentMarkPrice(symbol);\r\n                if (markPrice != null && markPrice.getValue().compareTo(java.math.BigDecimal.ZERO) > 0) {\r\n                    currentPrices.put(symbol, markPrice);\r\n                }\r\n            }\r\n\r\n            // Lấy tất cả các vị thế đang mở\r\n            List<Position> allRiskPositions = positionRepository.findAllByStatus(PositionStatus.OPEN);\r\n\r\n            // Nhóm các vị thế theo symbol\r\n            java.util.Map<Symbol, java.util.List<Position>> positionsBySymbol = allRiskPositions.stream()\r\n                .collect(java.util.stream.Collectors.groupingBy(Position::getSymbol));\r\n\r\n            // Kiểm tra từng symbol\r\n            processContracts(contracts, currentPrices, positionsBySymbol);\r\n\r\n            log.debug(\"Đã hoàn thành kiểm tra thanh lý cho tất cả các symbol\");\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi kiểm tra thanh lý cho tất cả các symbol\", e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Xử lý danh sách các hợp đồng để kiểm tra thanh lý\r\n     * @param contracts Danh sách các hợp đồng\r\n     * @param currentPrices Map chứa giá hiện tại cho mỗi symbol\r\n     * @param positionsBySymbol Map chứa danh sách vị thế cho mỗi symbol\r\n     */\r\n    private void processContracts(List<Contract> contracts,\r\n                                 java.util.Map<Symbol, Money> currentPrices,\r\n                                 java.util.Map<Symbol, java.util.List<Position>> positionsBySymbol) {\r\n        // Lọc các hợp đồng có giá hợp lệ và có vị thế\r\n        contracts.forEach(contract -> {\r\n            Symbol symbol = contract.getSymbol();\r\n            Money markPrice = currentPrices.get(symbol);\r\n\r\n            // Kiểm tra điều kiện\r\n            if (markPrice == null) {\r\n                log.warn(\"Không có giá đánh dấu hợp lệ cho symbol: {}\", symbol.getValue());\r\n                return; // Tương đương với continue trong vòng lặp for\r\n            }\r\n\r\n            // Lấy danh sách vị thế cho symbol này\r\n            java.util.List<Position> symbolPositions = positionsBySymbol.getOrDefault(symbol, java.util.Collections.emptyList());\r\n            if (symbolPositions.isEmpty()) {\r\n                log.debug(\"Không có vị thế nào cho symbol: {}\", symbol.getValue());\r\n                return; // Tương đương với continue trong vòng lặp for\r\n            }\r\n\r\n            // Xử lý các vị thế cho symbol này\r\n            processPositionsForSymbol(symbolPositions, markPrice, contract, symbol);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Xử lý danh sách các vị thế cho một symbol\r\n     * @param positions Danh sách các vị thế\r\n     * @param markPrice Giá đánh dấu hiện tại\r\n     * @param contract Hợp đồng\r\n     * @param symbol Symbol\r\n     */\r\n    private void processPositionsForSymbol(java.util.List<Position> positions, Money markPrice, Contract contract, Symbol symbol) {\r\n        // Giới hạn số lượng vị thế kiểm tra trong một lần\r\n        java.util.List<Position> positionsToCheck = positions;\r\n        if (positions.size() > maxPositionsPerBatch) {\r\n            positionsToCheck = positions.subList(0, maxPositionsPerBatch);\r\n        }\r\n\r\n        log.debug(\"Kiểm tra {} vị thế cho symbol: {}\", positionsToCheck.size(), symbol.getValue());\r\n\r\n        // Kiểm tra từng vị thế\r\n        for (Position position : positionsToCheck) {\r\n            checkAndLiquidatePositionIfNeeded(position, markPrice, contract, symbol.getValue());\r\n        }\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/icetea/lotus/infrastructure/service/LiquidationCheckService.java b/src/main/java/com/icetea/lotus/infrastructure/service/LiquidationCheckService.java
--- a/src/main/java/com/icetea/lotus/infrastructure/service/LiquidationCheckService.java	(revision 22f7c51b71f7b86a0e25a6ad30b64f747bf0f0e4)
+++ b/src/main/java/com/icetea/lotus/infrastructure/service/LiquidationCheckService.java	(date 1748335418640)
@@ -13,6 +13,8 @@
 
 import lombok.extern.slf4j.Slf4j;
 
+import java.time.LocalDateTime;
+
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.beans.factory.annotation.Qualifier;
 import org.springframework.beans.factory.annotation.Value;
@@ -217,12 +219,51 @@
      */
     private void markPositionAsLiquidationFailed(Position position) {
         try {
-            // Có thể cập nhật status hoặc thêm remark để tránh thanh lý lại
             log.warn("Đánh dấu vị thế thanh lý thất bại, positionId = {}, memberId = {}, symbol = {}",
                     position.getId(), position.getMemberId(), position.getSymbol().getValue());
 
-            // TODO: Implement logic để đánh dấu position hoặc ghi vào blacklist
-            // Ví dụ: có thể set status thành LIQUIDATION_FAILED hoặc thêm vào cache
+            // 1. Cập nhật remark của position để đánh dấu lỗi thanh lý
+            String failureRemark = String.format("LIQUIDATION_FAILED_%d_%s",
+                    System.currentTimeMillis(),
+                    position.getStatus() != null ? position.getStatus().toString() : "UNKNOWN_STATUS");
+
+            Position updatedPosition = Position.builder()
+                    .id(position.getId())
+                    .memberId(position.getMemberId())
+                    .symbol(position.getSymbol())
+                    .direction(position.getDirection())
+                    .volume(position.getVolume())
+                    .openPrice(position.getOpenPrice())
+                    .closePrice(position.getClosePrice())
+                    .liquidationPrice(position.getLiquidationPrice())
+                    .maintenanceMargin(position.getMaintenanceMargin())
+                    .margin(position.getMargin())
+                    .profit(position.getProfit())
+                    .remainingMargin(position.getRemainingMargin())
+                    .marginMode(position.getMarginMode())
+                    .leverage(position.getLeverage())
+                    .createTime(position.getCreateTime())
+                    .updateTime(LocalDateTime.now())
+                    .remark(failureRemark)
+                    .status(position.getStatus()) // Giữ nguyên status hiện tại
+                    .build();
+
+            // Lưu position với remark mới
+            positionRepository.save(updatedPosition);
+
+            // 2. Ghi log vào hệ thống để tracking
+            log.error("LIQUIDATION_FAILURE_TRACKING: positionId = {}, memberId = {}, symbol = {}, " +
+                    "originalStatus = {}, volume = {}, margin = {}, openPrice = {}, " +
+                    "failureTime = {}, failureRemark = {}",
+                    position.getId(), position.getMemberId(), position.getSymbol().getValue(),
+                    position.getStatus(), position.getVolume(),
+                    position.getMargin() != null ? position.getMargin().getValue() : null,
+                    position.getOpenPrice() != null ? position.getOpenPrice().getValue() : null,
+                    LocalDateTime.now(), failureRemark);
+
+            // 3. Có thể thêm vào cache để tránh retry ngay lập tức (optional)
+            // liquidationFailureCache.put(position.getId().getValue(), LocalDateTime.now());
+
         } catch (Exception e) {
             log.error("Lỗi khi đánh dấu vị thế thanh lý thất bại, positionId = {}", position.getId(), e);
         }
