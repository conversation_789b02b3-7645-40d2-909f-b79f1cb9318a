package com.icetea.lotus.application.port.input;

import com.icetea.lotus.application.dto.OrderBookDto;
import com.icetea.lotus.application.dto.OrderDto;
import com.icetea.lotus.application.dto.PlaceOrderCommand;
import com.icetea.lotus.application.dto.PlaceOrderResult;
import com.icetea.lotus.core.domain.valueobject.Page;
import com.icetea.lotus.infrastructure.api.request.FindOrderRequest;

import java.util.List;

/**
 * Use case interface cho việc đặt lệnh
 * Định nghĩa các phương thức cho việc đặt lệnh
 */
public interface PlaceOrderUseCase {

    /**
     * Đặt lệnh mới
     * @param command Command chứa thông tin lệnh
     * @return Kết quả đặt lệnh
     */
    PlaceOrderResult placeOrder(PlaceOrderCommand command);

    /**
     * Hủy lệnh
     * @param orderId ID của lệnh
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return OrderDto
     */
    OrderDto cancelOrder(String orderId, Long memberId, String symbol);

    /**
     * Hủy tất cả các lệnh của một thành viên
     * @param memberId ID của thành viên
     * @return Số lượng lệnh đã hủy
     */
    int cancelAllOrders(Long memberId);

    /**
     * Hủy tất cả các lệnh của một thành viên cho một symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return List<OrderDto>
     */
    List<OrderDto> cancelAllOrders(Long memberId, String symbol);

    /**
     * Lấy thông tin lệnh
     * @param orderId ID của lệnh
     * @return Thông tin lệnh
     */
    OrderDto getOrder(String orderId);

    /**
     * Lấy danh sách lệnh theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @param status Trạng thái lệnh
     * @param page Số trang
     * @param size Kích thước trang
     * @return Danh sách lệnh
     */
    List<OrderDto> getOrders(Long memberId, String symbol, String status, int page, int size);

    /**
     * Lấy danh sách lệnh đang hoạt động theo memberId và symbol
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return Danh sách lệnh
     */
    List<OrderDto> getActiveOrders(Long memberId, String symbol);

    /**
     * Cập nhật lệnh
     * @param orderId ID của lệnh
     * @param price Giá mới
     * @param volume Khối lượng mới
     * @return Kết quả cập nhật lệnh
     */
    PlaceOrderResult updateOrder(String orderId, java.math.BigDecimal price, java.math.BigDecimal volume);

    /**
     * Lấy danh sách order theo yêu cầu
     * @param request yêu cầu ấy order
     * @return Trả về danh sách order
     */
    OrderBookDto findOrder(FindOrderRequest request);
}
