package com.icetea.lotus.Trader;

import lombok.Getter;

import java.util.concurrent.ConcurrentHashMap;

@Getter
public class CoinTraderFactory {

    private ConcurrentHashMap<String, CoinTrader> traderMap;

    public CoinTraderFactory() {
        traderMap = new ConcurrentHashMap<>();
    }

    // Add, existing ones cannot be added
    public void addTrader(String symbol, CoinTrader trader) {
        if (!traderMap.containsKey(symbol)) {
            traderMap.put(symbol, trader);
        }
    }

    // Reset, overwrite even if it already exists
    public void resetTrader(String symbol, CoinTrader trader) {
        traderMap.put(symbol, trader);
    }

    public boolean containsTrader(String symbol) {
        return traderMap.containsKey(symbol);
    }

    public CoinTrader getTrader(String symbol) {
        return traderMap.get(symbol);
    }

}
