package com.icetea.lotus.event;

import com.alibaba.fastjson.JSONObject;
import com.icetea.lotus.coin.CoinExchangeFactory;
import com.icetea.lotus.constant.PromotionRewardType;
import com.icetea.lotus.constant.RewardRecordType;
import com.icetea.lotus.dao.MemberDao;
import com.icetea.lotus.entity.*;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.service.RewardPromotionSettingService;
import com.icetea.lotus.service.RewardRecordService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.icetea.lotus.util.BigDecimalUtils.*;

import java.math.BigDecimal;
import java.util.Arrays;

@Service
public class OrderEvent {
    @Autowired
    private MemberDao memberDao;
    @Autowired
    private MemberWalletService memberWalletService;
    @Autowired
    private RewardRecordService rewardRecordService;
    @Autowired
    private RewardPromotionSettingService rewardPromotionSettingService;
    @Autowired
    private CoinExchangeFactory coins;

    public void onOrderCompleted(Order order) {
//        Member member = memberDao.findOne(order.getMemberId());
//        member.setTransactions(member.getTransactions() + 1);
//        Member member1 = memberDao.findOne(order.getCustomerId());
//        member1.setTransactions(member1.getTransactions() + 1);
//        RewardPromotionSetting rewardPromotionSetting = rewardPromotionSettingService.findByType(PromotionRewardType.TRANSACTION);
//        if (rewardPromotionSetting != null && coins.get("USDT").compareTo(BigDecimal.ZERO) > 0) {
//            Member[] array = {member, member1};
//            Arrays.stream(array).forEach(
//                    x -> {
//                        // If you want to return commissions based on a certain time, replace the following line
//                        //if (x.getInviterId() != null&&!(DateUtil.diffDays(new Date(), x.getRegistrationTime()) > rewardPromotionSetting.getEffectiveTime())) {
//                        // Only the first transaction will earn commission
//                        if (x.getTransactions() == 1 && x.getInviterId() != null) {
//                            Member member2 = memberDao.findOne(x.getInviterId());
//                            MemberWallet memberWallet1 = memberWalletService.findByCoinAndMember(rewardPromotionSetting.getCoin(), member2);
//                            BigDecimal number = mulRound(order.getNumber(), div(coins.get(order.getCoin().getUnit()), coins.get("USDT")));
//                            BigDecimal amount1 = mulRound(number, getRate(JSONObject.parseObject(rewardPromotionSetting.getInfo()).getBigDecimal("one")));
//                            memberWallet1.setBalance(add(memberWallet1.getBalance(), amount1));
//                            memberWalletService.save(memberWallet1);
//                            RewardRecord rewardRecord1 = new RewardRecord();
//                            rewardRecord1.setAmount(amount1);
//                            rewardRecord1.setCoin(rewardPromotionSetting.getCoin());
//                            rewardRecord1.setMember(member2);
//                            rewardRecord1.setRemark(rewardPromotionSetting.getType().getCnName());
//                            rewardRecord1.setType(RewardRecordType.PROMOTION);
//                            rewardRecordService.save(rewardRecord1);
//                            if (member2.getInviterId() != null) {
//                                Member member3 = memberDao.findOne(member2.getInviterId());
//                                MemberWallet memberWallet2 = memberWalletService.findByCoinAndMember(rewardPromotionSetting.getCoin(), member3);
//                                BigDecimal amount2 = mulRound(number, getRate(JSONObject.parseObject(rewardPromotionSetting.getInfo()).getBigDecimal("two")));
//                                memberWallet2.setBalance(add(memberWallet2.getBalance(), amount2));
//                                RewardRecord rewardRecord2 = new RewardRecord();
//                                rewardRecord2.setAmount(amount2);
//                                rewardRecord2.setCoin(rewardPromotionSetting.getCoin());
//                                rewardRecord2.setMember(member3);
//                                rewardRecord2.setRemark(rewardPromotionSetting.getType().getCnName());
//                                rewardRecord2.setType(RewardRecordType.PROMOTION);
//                                rewardRecordService.save(rewardRecord2);
//                            }
//                        }
//                    }
//            );
//        }
    }
}
