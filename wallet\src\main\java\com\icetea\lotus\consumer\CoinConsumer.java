package com.icetea.lotus.consumer;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.constant.BooleanEnum;
import com.icetea.lotus.entity.Coin;
import com.icetea.lotus.entity.Member;
import com.icetea.lotus.entity.MemberWallet;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.MemberWalletService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class CoinConsumer {

    private final CoinService coinService;
    private final MemberWalletService walletService;
    private final RestTemplate restTemplate;
    private final MemberService memberService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @PersistenceContext
    private EntityManager em;

    @Autowired
    public CoinConsumer(CoinService coinService,
                        MemberWalletService walletService,
                        RestTemplate restTemplate,
                        MemberService memberService) {
        this.coinService = coinService;
        this.walletService = walletService;
        this.restTemplate = restTemplate;
        this.memberService = memberService;
    }

    @KafkaListener(topics = "coin-start")
    public void handle(String content) {
        log.info("handle coin-start, data={}", content);
        if (!StringUtils.hasText(content)) {
            return;
        }
        try {
            JsonNode json = objectMapper.readTree(content);
            if (json == null) {
                return;
            }

            Coin coin = coinService.findOne(json.get("name").asText());
            if (coin != null && coin.getEnableRpc().equals(BooleanEnum.IS_TRUE)) {
                List<Member> members = memberService.findAll();
                List<String> batch = new ArrayList<>();
                List<Map> addresses = new ArrayList<>();

                for (int i = 0; i < members.size(); i++) {
                    batch.add("U" + members.get(i).getId());
                    if (i % 1000 == 0 || i == members.size() - 1) {
                        String serviceName = "SERVICE-RPC-" + coin.getUnit();
                        String url = "http://" + serviceName + "/rpc/address/batch";
                        ResponseEntity<List> result = restTemplate.postForEntity(url, batch, List.class);
                        log.info("remote call: service={}, result={}", serviceName, result);
                        if (result.getStatusCode().is2xxSuccessful()) {
                            addresses.addAll(result.getBody());
                        }
                        batch.clear();
                    }
                }

                for (int i = 0; i < addresses.size(); i++) {
                    Map data = addresses.get(i);
                    MemberWallet wallet = new MemberWallet();
                    wallet.setCoin(coin);
                    wallet.setMemberId(((Number) data.get("uid")).longValue());
                    wallet.setBalance(BigDecimal.ZERO);
                    wallet.setFrozenBalance(BigDecimal.ZERO);
                    wallet.setAddress(data.get("address").toString());
                    em.persist(wallet);

                    if (i % 1000 == 0 || i == addresses.size() - 1) {
                        em.flush();
                        em.clear();
                    }
                }
            }
        } catch (Exception e) {
            log.error("handle coin-start failed, error={}", e.getMessage());
        }
    }
}
