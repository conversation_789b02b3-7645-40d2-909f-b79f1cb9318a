package com.icetea.lotus.consumer;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.entity.Addressext;
import com.icetea.lotus.entity.Coin;
import com.icetea.lotus.entity.Coinext;
import com.icetea.lotus.service.AddressextService;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.CoinextService;
import com.icetea.lotus.service.MemberWalletService;
import com.icetea.lotus.service.WithdrawRecordService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class FinanceConsumer {
    private final CoinService coinService;
    private final CoinextService coinextService;
    private final MemberWalletService walletService;
    private final AddressextService addressextService;
    private final RestTemplate restTemplate;
    private final WithdrawRecordService withdrawRecordService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Process recharge messages, the key value is the name of the currency (note that it is the full name, such as Bitcoin)
     *
     * @param recordRequest
     */
    @KafkaListener(topics = {"deposit"})
    public void handleDeposit(List<ConsumerRecord<String, String>> recordRequest) {
        ConsumerRecord<String, String> record = recordRequest.get(0);
        log.info("topic={},key={},value={}", record.topic(), record.key(), record.value());
        if (StringUtils.isEmpty(record.value())) {
            return;
        }
        try {
            JsonNode json = objectMapper.readTree(record.value());
            if (json == null) {
                return;
            }
            BigDecimal amount = new BigDecimal(json.get("amount").asText());
            String txid = json.get("txid").asText();
            String address = json.get("address").asText();
            String fromAddress = json.get("fromAddress").asText();
            String protocol = json.get("protocol").asText();
            Long blockHeight = json.get("blockHeight").asLong();
            Coin coin = coinService.findByName(record.key());
            Addressext addressext = addressextService.findByAddress(address);
            if (addressext == null) {
                log.info("The recharge address does not exist{}", address);
                return;
            }
            Coinext coinext = coinextService.findFirstByCoinnameAndProtocol(coin.getName(), addressext.getCoinProtocol());
            if (coinext == null) {
                log.info("Currency expansion does not exist{}", coin.getName());
                return;
            }
            if (amount.compareTo(coinext.getMinrecharge()) >= 0) {
                log.info("coin={}", coin);
                if (walletService.findDeposit(address, txid) == null) {
                    MessageResult mr = walletService.recharge(coin, address, amount, txid, fromAddress, protocol, blockHeight);
                    log.info("wallet recharge result:{}", mr);
                }
            } else {
                log.info("The recharge number is less than the minimum number and no additional recharge record is added.");
            }
        } catch (Exception e) {
            log.error("Error handling deposit", e);
        }

//        if(coin.getAccountType() == 1) {
//            Long memoId = json.getLong("userId"); // Note Memo
//            Long userId = memoId - 345678; // Note that this must be consistent with the front-end memo
//            Member m = memberService.findOne(userId);
//            if(m != null && walletService.findDepositByTxid(txid) == null) {
//
//                MessageResult mr = walletService.recharge2(userId, coin, address, amount, txid);
//                log.info("wallet recharge result:{}", mr);
//            }
//        }else {
//            if (coin != null && walletService.findDeposit(address, txid) == null) {
//                MessageResult mr = walletService.recharge(coin, address, amount, txid,fromAddress,protocol,blockHeight);
//                log.info("wallet recharge result:{}", mr);
//            }
//        }
    }
//    /**
//     * Process submission request, call wallet rpc, and automatically transfer money
//     *
//     * @param record
//     */
//    @KafkaListener(topics = {"withdraw"})
//    public void handleWithdraw(ConsumerRecord<String, String> record) {
//        log.info("topic={},key={},value={}", record.topic(), record.key(), record.value());
//        if (StringUtils.isEmpty(record.value())) {
//            return;
//        }
//        JSONObject json = JSON.parseObject(record.value());
//        Long withdrawId = json.getLong("withdrawId");
//        try {
//            String serviceName = "SERVICE-RPC-" + record.key().toUpperCase();
//            String url = "http://" + serviceName + "/rpc/withdraw?address={1}&amount={2}&fee={3}";
//
//            Coin coin = coinService.findByUnit(record.key());
//            log.info("coin = {}",coin.toString());
//            if (coin != null && coin.getCanAutoWithdraw() == BooleanEnum.IS_TRUE) {
//                BigDecimal minerFee = coin.getMinerFee();
//                MessageResult result = restTemplate.getForObject(url,
//                        MessageResult.class, json.getString("address"), json.getBigDecimal("arriveAmount"), minerFee);
//                log.info("=========================rpc end================================");
//                log.info("result = {}", result);
//                if (result.getCode() == 0 && result.getData() != null) {
//                    log.info("====================== Processing is successful, data updates business for txid ==================================");
//                    //Processing successfully, data is txid, update business order
//                    String txid = (String) result.getData();
//                    withdrawRecordService.withdrawSuccess(withdrawId, txid);
//                } else {
//                    log.info("====================== Automatic transfer failed and converted to manual processing ==================================");
//                    //Automatic transfer failed and converted to manual processing
//                    withdrawRecordService.autoWithdrawFail(withdrawId);
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("auto withdraw failed,error={}", e.getMessage());
//            log.info("====================== Automatic transfer failed and converted to manual processing ==================================");
////            Automatic transfer failed and converted to manual processing
//            withdrawRecordService.autoWithdrawFail(withdrawId);
//        }
//    }


//    /**
//     * Return to the status after asynchronous money transfer
//     * @param record
//     */
//    @KafkaListener(topics = {"withdraw-notify"})
//    public void withdrawNotify(ConsumerRecord<String, String> record){
//        log.info("topic={},accessKey={},value={}", record.topic(), record.key(), record.value());
//        if (StringUtils.isEmpty(record.value())) {
//            return;
//        }
//        JSONObject json = JSON.parseObject(record.value());
//        Long withdrawId = json.getLong("withdrawId");
//        WithdrawRecord withdrawRecord=withdrawRecordService.findOne(withdrawId);
//        if(withdrawRecord==null){
//            return;
//        }
//        String txid=json.getString("txid");
//        int status=json.getInteger("status");
//        //Transfer failed, status changed back to wait for coins to be released
//        if(status==0){
//            withdrawRecord.setStatus(WithdrawStatus.WAITING);
//            withdrawRecordService.save(withdrawRecord);
//        }else if(status==1){
//            withdrawRecordService.withdrawSuccess(withdrawId, txid);
//        }
//    }
}
