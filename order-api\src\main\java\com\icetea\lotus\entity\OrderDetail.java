package com.icetea.lotus.entity;

import com.icetea.lotus.constant.AdvertiseType;
import com.icetea.lotus.constant.OrderStatus;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Builder
@Data
public class OrderDetail {
    private String orderSn;
    private AdvertiseType type;
    private String unit;
    private OrderStatus status;
    private BigDecimal price;
    private BigDecimal money;
    private BigDecimal amount;
    private BigDecimal commission;
    private PayInfo payInfo;
    private List<PaymentTypeRecord> payInfos;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    private Date payTime;
    private int timeLimit;
    private String otherSide;
    private long myId;
    private long hisId;
    private String memberMobile;
    private String currency;
}
