# Stage 1: Build
FROM maven:3.8.8-eclipse-temurin-8 as build

# Set the working directory inside the container
WORKDIR /app

COPY ../deploy/settings.xml /root/.m2/settings.xml

# Copy only the pom.xml to download dependencies first (caching layer)
COPY ../pom.xml .

# Download dependencies without copying the source code (caches dependencies)
RUN mvn dependency:go-offline -B

# Copy the source code into the working directory
COPY ../src ./src

# Package the application JAR
RUN mvn package -B -DskipTests

# Stage 2: Runtime
FROM openjdk:17-jdk-alpine

# Set the working directory for the runtime environment
WORKDIR /app

ARG JAR_FILE=exchange-api.jar


# Copy the JAR file from the build stage to the runtime stage
COPY --from=build /app/target/${JAR_FILE} app.jar

# Expose the application port
EXPOSE 6003

# Run the application
ENTRYPOINT ["java", "-jar", "app.jar"]