package com.icetea.lotus.infrastructure.service;

import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.PositionStatus;
import com.icetea.lotus.core.domain.valueobject.Money;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * Service để chẩn đoán vị thế trước khi thanh lý
 */
@Service
@Slf4j
public class PositionDiagnosticService {

    /**
     * Chẩn đoán vị thế và trả về thông tin chi tiết
     * @param position Vị thế cần chẩn đoán
     * @param markPrice Giá đánh dấu
     * @return Kết quả chẩn đoán
     */
    public PositionDiagnosticResult diagnose(Position position, Money markPrice) {
        PositionDiagnosticResult result = new PositionDiagnosticResult();
        
        if (position == null) {
            result.setValid(false);
            result.addIssue("Position is null");
            return result;
        }

        // Kiểm tra ID
        if (position.getId() == null) {
            result.addIssue("Position ID is null");
        }

        // Kiểm tra member ID
        if (position.getMemberId() == null) {
            result.addIssue("Member ID is null");
        }

        // Kiểm tra symbol
        if (position.getSymbol() == null) {
            result.addIssue("Symbol is null");
        }

        // Kiểm tra status
        if (position.getStatus() == null) {
            result.addIssue("Status is null");
        } else if (position.getStatus() != PositionStatus.OPEN) {
            result.addIssue("Status is not OPEN: " + position.getStatus());
            result.setValid(false);
        }

        // Kiểm tra volume
        if (position.getVolume() == null) {
            result.addIssue("Volume is null");
        } else if (position.getVolume().compareTo(BigDecimal.ZERO) <= 0) {
            result.addIssue("Volume is not positive: " + position.getVolume());
            result.setValid(false);
        }

        // Kiểm tra margin
        if (position.getMargin() == null) {
            result.addIssue("Margin is null");
        } else if (position.getMargin().getValue().compareTo(BigDecimal.ZERO) <= 0) {
            result.addIssue("Margin is not positive: " + position.getMargin().getValue());
            result.setValid(false);
        }

        // Kiểm tra open price
        if (position.getOpenPrice() == null) {
            result.addIssue("Open price is null");
        } else if (position.getOpenPrice().getValue().compareTo(BigDecimal.ZERO) <= 0) {
            result.addIssue("Open price is not positive: " + position.getOpenPrice().getValue());
        }

        // Kiểm tra direction
        if (position.getDirection() == null) {
            result.addIssue("Direction is null");
        }

        // Kiểm tra mark price
        if (markPrice == null) {
            result.addIssue("Mark price is null");
        } else if (markPrice.getValue().compareTo(BigDecimal.ZERO) <= 0) {
            result.addIssue("Mark price is not positive: " + markPrice.getValue());
        }

        // Tính toán unrealized profit nếu có đủ thông tin
        if (position.getOpenPrice() != null && markPrice != null && position.getVolume() != null) {
            try {
                Money unrealizedProfit = position.calculateUnrealizedProfit(markPrice);
                result.setUnrealizedProfit(unrealizedProfit);
                
                if (position.getMargin() != null) {
                    Money availableMargin = position.getMargin().add(unrealizedProfit);
                    result.setAvailableMargin(availableMargin);
                    
                    if (availableMargin.getValue().compareTo(BigDecimal.ZERO) <= 0) {
                        result.addIssue("Available margin is not positive: " + availableMargin.getValue());
                    }
                }
            } catch (Exception e) {
                result.addIssue("Error calculating unrealized profit: " + e.getMessage());
            }
        }

        // Log kết quả chẩn đoán
        if (!result.isValid() || !result.getIssues().isEmpty()) {
            log.warn("Position diagnostic issues found for positionId = {}, memberId = {}, symbol = {}, issues = {}",
                    position.getId(), position.getMemberId(), 
                    position.getSymbol() != null ? position.getSymbol().getValue() : null,
                    result.getIssues());
        }

        return result;
    }

    /**
     * Kiểm tra xem vị thế có thể thanh lý được không
     * @param position Vị thế
     * @param markPrice Giá đánh dấu
     * @return true nếu có thể thanh lý
     */
    public boolean canLiquidate(Position position, Money markPrice) {
        PositionDiagnosticResult result = diagnose(position, markPrice);
        return result.isValid();
    }

    /**
     * Log thông tin chi tiết về vị thế
     * @param position Vị thế
     * @param markPrice Giá đánh dấu
     */
    public void logPositionDetails(Position position, Money markPrice) {
        if (position == null) {
            log.error("Position is null");
            return;
        }

        log.info("Position details: " +
                "positionId = {}, " +
                "memberId = {}, " +
                "symbol = {}, " +
                "status = {}, " +
                "direction = {}, " +
                "volume = {}, " +
                "openPrice = {}, " +
                "margin = {}, " +
                "markPrice = {}, " +
                "createTime = {}, " +
                "updateTime = {}",
                position.getId(),
                position.getMemberId(),
                position.getSymbol() != null ? position.getSymbol().getValue() : null,
                position.getStatus(),
                position.getDirection(),
                position.getVolume(),
                position.getOpenPrice() != null ? position.getOpenPrice().getValue() : null,
                position.getMargin() != null ? position.getMargin().getValue() : null,
                markPrice != null ? markPrice.getValue() : null,
                position.getCreateTime(),
                position.getUpdateTime());

        // Chẩn đoán và log issues
        PositionDiagnosticResult result = diagnose(position, markPrice);
        if (!result.getIssues().isEmpty()) {
            log.warn("Position diagnostic issues: {}", result.getIssues());
        }
    }

    /**
     * Kết quả chẩn đoán vị thế
     */
    public static class PositionDiagnosticResult {
        private boolean valid = true;
        private java.util.List<String> issues = new java.util.ArrayList<>();
        private Money unrealizedProfit;
        private Money availableMargin;

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public java.util.List<String> getIssues() {
            return issues;
        }

        public void addIssue(String issue) {
            this.issues.add(issue);
        }

        public Money getUnrealizedProfit() {
            return unrealizedProfit;
        }

        public void setUnrealizedProfit(Money unrealizedProfit) {
            this.unrealizedProfit = unrealizedProfit;
        }

        public Money getAvailableMargin() {
            return availableMargin;
        }

        public void setAvailableMargin(Money availableMargin) {
            this.availableMargin = availableMargin;
        }
    }
}
