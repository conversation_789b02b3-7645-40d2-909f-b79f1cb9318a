# Kế hoạch Kiến trúc Future: Matching Engine + Module Separation

## 1. Tổng quan

Kế hoạch này kết hợp:
1. **Tạo Future Matching Engine Service** (theo mô hình exchange)
2. **Tách các chức năng** sang các module phù hợp theo nghiệp vụ
3. **Bỏ qua module chat** - không tách notification functions

Mục tiêu là tạo ra kiến trúc **hoàn chỉnh**, **scalable**, và **maintainable** cho futures trading system.

## 2. Target Architecture - 6 Services Model

### 2.1. **Future-Core (Library)**
```
future-core/ (LIBRARY)
├── core/domain/
│   ├── entity/ ✅ (Order, Position, Trade, Contract)
│   ├── valueobject/ ✅ (Money, Symbol, OrderId)
│   ├── service/ ✅ (Business logic interfaces)
│   │   ├── OrderMatchingEngineService
│   │   ├── PositionService
│   │   ├── LiquidationService
│   │   ├── FundingRateService
│   │   ├── RiskManagementService
│   │   └── ADLService
│   └── repository/ ✅ (Repository interfaces)
├── application/
│   ├── service/ ✅ (Application services)
│   ├── port/ ✅ (Use case interfaces)
│   └── mapper/ ✅ (Domain ↔ DTO mappers)
└── infrastructure/
    ├── persistence/ ✅ (Database adapters)
    └── config/ ✅ (Shared configurations)
```

### 2.2. **Future-API (API Service)**
```
future-api/ (MICROSERVICE)
├── FutureApiApplication.java ✅
├── controller/ ✅ (REST endpoints)
│   ├── TradingController
│   ├── PositionController
│   └── TradingManagementController
├── websocket/ ✅ (Client-facing WebSocket)
│   ├── WebSocketPositionHandler
│   ├── PositionWebSocketController
│   └── NotificationWebSocketHandler ✅ (giữ lại)
├── messaging/ ✅ (API messaging)
│   ├── producer/OrderCommandProducer
│   └── consumer/OrderEventConsumer
├── notification/ ✅ (giữ trong future-api)
│   ├── NotificationService
│   ├── EmailService
│   └── SMSService
├── security/ ✅ (Authentication & Authorization)
└── config/ ✅ (API configurations)
```

### 2.3. **Future (Matching Engine Service)** 🆕
```
future/ (MICROSERVICE - NEW)
├── FutureApplication.java 🆕
├── matching/ 🆕 (Core matching engines)
│   ├── DistributedLockingMatchingEngine ✅ (primary)
│   ├── OptimizedMatchingEngine ✅ (backup/alternative)
│   ├── SymbolMatchingEngine ✅ (symbol-specific)
│   ├── DistributedMatchingEngineFactory ✅
│   └── DistributedOrderBookSnapshot ✅ (Redis snapshots)
├── service/ 🆕 (Engine services)
│   ├── OrderProcessingService
│   ├── TradeExecutionService
│   └── OrderBookService
├── messaging/ 🆕 (Engine messaging)
│   ├── consumer/OrderCommandConsumer
│   └── producer/TradeEventProducer
└── config/ 🆕 (Engine configurations)
    ├── MatchingEngineConfig
    └── RedisLockingConfig
```

### 2.4. **Market Service (Enhanced)** 🔄
```
market/ (MICROSERVICE - ENHANCED)
├── MarketApplication.java ✅
├── service/
│   ├── spot/ ✅ (existing)
│   └── futures/ 🆕 (from future-core)
│       ├── FuturesLastPriceService
│       ├── FuturesIndexPriceService
│       ├── FuturesMarkPriceService
│       └── FuturesKLineService
├── websocket/
│   ├── spot/ ✅ (existing)
│   └── futures/ 🆕 (from future-core)
│       └── FuturesMarketHandler
├── cache/
│   ├── spot/ ✅ (existing)
│   └── futures/ 🆕 (from future-core)
│       ├── FuturesPriceCache
│       └── FuturesKLineCache
└── integration/ 🆕
    └── FuturesMarketIntegration
```

### 2.5. **Admin Service (Enhanced)** 🔄
```
admin/ (MICROSERVICE - ENHANCED)
├── AdminApplication.java ✅
├── service/
│   ├── AdminService ✅ (existing)
│   └── scheduler/ 🆕 (from future-core)
│       ├── LiquidationScheduler
│       ├── FundingScheduler
│       ├── SettlementScheduler
│       ├── HealthCheckScheduler
│       └── ADLScheduler
├── monitoring/ 🆕
│   ├── PerformanceMonitoringService
│   └── SystemHealthService
└── management/ 🆕
    └── TradingSystemManagementService
```

### 2.6. **Wallet Service (Enhanced)** 🔄
```
wallet/ (MICROSERVICE - ENHANCED)
├── WalletApplication.java ✅
├── service/
│   ├── WalletService ✅ (existing)
│   └── futures/ 🆕 (from future-core)
│       ├── FuturesWalletService
│       ├── MarginCalculationService
│       └── FuturesTransactionService
├── integration/
│   ├── wallet-rpc/ ✅ (existing)
│   └── futures/ 🆕
│       └── FuturesBalanceService
└── risk/ 🆕
    └── MarginRiskService
```

## 3. Migration Plan - 5 Phases

### Phase 1: Setup Future-API Structure (1 week)
**Mục tiêu**: Chuẩn bị future-api để nhận API components

**Steps**:
1. Create future-api package structure
2. Setup pom.xml với dependencies
3. Create main application class
4. Setup configuration files
5. Create placeholder controllers
6. **Setup notification services trong future-api** (thay vì chat)

### Phase 2: Create Future Matching Engine Service (1 week)
**Mục tiêu**: Tạo dedicated matching engine service

**Steps**:
1. Create future service structure
2. Setup pom.xml và configurations
3. Create main application class
4. Setup Kafka topics và messaging
5. Create placeholder matching engines

### Phase 3: Extract Market Data Services (2 weeks)
**Mục tiêu**: Di chuyển market data từ future-core sang market service

**Steps**:
1. Enhance market service với futures support
2. Di chuyển market data services
3. Di chuyển WebSocket market handlers
4. Di chuyển cache components
5. Setup REST API endpoints
6. Update future-core integration

### Phase 4: Extract Background Schedulers (1 week)
**Mục tiêu**: Di chuyển schedulers sang admin service

**Steps**:
1. Enhance admin service với scheduler support
2. Di chuyển all schedulers
3. Setup monitoring và alerting
4. Configure scheduling
5. Update future-core integration

### Phase 5: Extract Wallet Operations & Complete Migration (2 weeks)
**Mục tiêu**: Di chuyển wallet logic và hoàn thành migration

**Steps**:
1. Enhance wallet service với futures support
2. Di chuyển wallet services
3. Di chuyển API components sang future-api
4. Di chuyển matching engines sang future service
5. Convert future-core thành library
6. Integration testing

## 4. Service Communication Architecture

### 4.1. **Trading Flow**
```
Client Request → Future-API → Kafka → Future (Matching Engine)
                     ↓                        ↓
              WebSocket Response ← Kafka ← Trade Events
```

### 4.2. **Market Data Flow**
```
External Data → Market Service → Cache/DB
                     ↓
              WebSocket Streaming → Clients
                     ↓
              Kafka Events → Future-API & Future Service
```

### 4.3. **Wallet Operations Flow**
```
Trading Events → Wallet Service → Balance Updates
                     ↓
              Kafka Events → Future-API
```

### 4.4. **Notification Flow** (trong Future-API)
```
Trading Events → Future-API → NotificationService → Email/SMS
                     ↓
              WebSocket Alerts → Clients
```

## 5. Kafka Topics Architecture

### 5.1. **Core Trading Topics**
```
future-order-commands     → Future-API produces, Future consumes
future-order-events       → Future produces, Future-API consumes
future-trade-events       → Future produces, Multiple services consume
future-position-updates   → Future produces, Future-API consumes
```

### 5.2. **Market Data Topics**
```
future-price-updates      → Market produces, Multiple services consume
future-kline-updates      → Market produces, Multiple services consume
future-orderbook-updates  → Future produces, Market & Future-API consume
```

### 5.3. **Wallet Topics**
```
future-balance-updates    → Wallet produces, Multiple services consume
future-margin-calls       → Wallet produces, Future-API consumes
future-transaction-events → Wallet produces, Multiple services consume
```

### 5.4. **System Topics**
```
future-liquidation-events → Admin produces, Multiple services consume
future-funding-events     → Admin produces, Multiple services consume
future-system-alerts      → Admin produces, Future-API consumes
```

## 6. Benefits của Architecture

### 6.1. **Performance Benefits**
- **Future service**: Dedicated matching engine performance
- **Future-api**: Optimized API response times với integrated notifications
- **Market service**: Unified market data với caching
- **Wallet service**: Specialized wallet operations
- **Independent scaling** theo workload characteristics

### 6.2. **Operational Benefits**
- **Clear separation** của concerns
- **Independent deployment** cycles
- **Better fault isolation**
- **Specialized monitoring** cho từng service type
- **Simplified notification management** trong future-api

### 6.3. **Development Benefits**
- **Team specialization** - API, Engine, Market, Wallet teams
- **Parallel development** của features
- **Easier testing** với isolated components
- **Clear ownership** boundaries
- **Simplified notification logic** không cần separate service

## 7. Implementation Priorities

### 7.1. **High Priority (Phases 1-2)**
- Setup future-api structure với notification support
- Create future matching engine service
- Essential infrastructure setup

### 7.2. **Medium Priority (Phases 3-4)**
- Extract market data services
- Extract background schedulers
- Core functionality migration

### 7.3. **Lower Priority (Phase 5)**
- Extract wallet operations
- Complete library conversion
- Performance optimization

## 8. Success Criteria

### 8.1. **Functional Requirements**
- ✅ All trading functionality maintained
- ✅ Market data served từ market service
- ✅ Matching engine performance improved
- ✅ Wallet operations centralized
- ✅ Notifications handled trong future-api

### 8.2. **Non-Functional Requirements**
- ✅ Latency < 50ms cho matching engine
- ✅ API response time < 100ms
- ✅ 99.9% uptime cho critical services
- ✅ Zero data loss during migration

### 8.3. **Architecture Goals**
- ✅ Future-core becomes pure library
- ✅ Clear service boundaries established
- ✅ Scalable architecture achieved
- ✅ Maintainable codebase created
- ✅ Simplified notification management

## 9. Matching Engine Configuration

### 9.1. **Primary Engine: DistributedLockingMatchingEngine**
- **Current Production Engine**: Đã proven và stable
- **Redis Distributed Locking**: Ensures consistency across pods
- **Symbol-based Sharding**: Each symbol assigned to specific pod
- **Order Book Recovery**: Using Redis snapshots every 5 minutes

### 9.2. **Removed Components**
- **❌ LockFreeMatchingEngine**: Không sử dụng trong production
- **❌ Lock-free algorithms**: Complexity không cần thiết
- **❌ Multi-threading optimizations**: DistributedLocking đã sufficient

### 9.3. **Enhanced Components**
- **✅ DistributedOrderBookSnapshot**: Improved Redis snapshot mechanism
- **✅ SymbolMatchingEngine**: Enhanced symbol-specific processing
- **✅ OptimizedMatchingEngine**: Backup/alternative engine
- **✅ RedisLockingConfig**: Optimized locking configuration

### 9.4. **Configuration Focus**
```yaml
matching-engine:
  primary-engine: DistributedLockingMatchingEngine
  backup-engine: OptimizedMatchingEngine
  use-lock-free: false  # Disabled
  redis-locking:
    enabled: true
    timeout: 5000ms
    retry-attempts: 3
  order-book-snapshot:
    enabled: true
    interval: 300000  # 5 minutes
    redis-key-prefix: "future:orderbook:"
```

## 10. Key Differences từ Previous Plan

### 10.1. **❌ Bỏ Chat Service Enhancement**
- Không tách notification functions sang chat service
- Giữ notification logic trong future-api
- Simplified architecture với ít service dependencies

### 10.2. **❌ Bỏ LockFreeMatchingEngine**
- Focus vào DistributedLockingMatchingEngine đã proven
- Simplified matching engine architecture
- Reduced complexity trong future service

### 10.3. **✅ Integrated Notification trong Future-API**
- NotificationService trong future-api
- EmailService và SMSService trong future-api
- WebSocket notifications từ future-api
- Easier management và testing

### 10.4. **✅ Cleaner Service Boundaries**
- 6 services thay vì 7 services
- Reduced inter-service communication
- Simplified deployment và monitoring
- Focus vào proven matching engine technology

Kiến trúc này sẽ tạo ra một **efficient futures trading system** với **optimal performance**, **clear boundaries**, và **simplified management** mà không cần phức tạp hóa với chat service!
