package com.icetea.lotus.bot;

import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Controller for managing the TradeBot
 */
@RestController
@RequestMapping("/tradebot")
@Slf4j
@RequiredArgsConstructor
public class TradeBotController {

    private final TradeBot tradeBot;

    /**
     * Start the auto buy task with the specified parameters
     * @param fixedRate the fixed rate in milliseconds (default: 1000)
     * @param symbol the trading symbol (optional, if null will use random symbol)
     * @param maxExecutions the maximum number of times to run the task (default: -1, which means no limit)
     * @return MessageResult with success/failure information
     */
    @PostMapping("/start")
    public MessageResult startAutoBuy(
            @RequestParam(value = "fixedRate") long fixedRate,
            @RequestParam(value = "symbol", required = false) String symbol,
            @RequestParam(value = "maxExecutions") int maxExecutions) {

        log.info("Received request to start auto buy task with fixedRate={} ms, symbol={}, maxExecutions={}", 
                fixedRate, symbol != null ? symbol : "random", maxExecutions > 0 ? maxExecutions : "unlimited");

        boolean started = tradeBot.startAutoBuy(fixedRate, symbol, maxExecutions);

        if (started) {
            return MessageResult.success("Auto buy task started successfully");
        } else {
            return MessageResult.error("Auto buy task is already running. Stop it first before starting with new parameters.");
        }
    }

    /**
     * Stop the auto-buy task
     * @return MessageResult with success/failure information
     */
    @PostMapping("/stop")
    public MessageResult stopAutoBuy() {
        log.info("Received request to stop auto buy task");

        boolean stopped = tradeBot.stopAutoBuy();

        if (stopped) {
            return MessageResult.success("Auto buy task stopped successfully");
        } else {
            return MessageResult.error("Auto buy task is not running");
        }
    }

    /**
     * Get the current status and configuration of the auto buy task
     * @return Map containing the current configuration
     */
    @GetMapping("/status")
    public Map<String, Object> getAutoBuyStatus() {
        log.info("Received request to get auto buy task status");
        return tradeBot.getAutoBuyConfig();
    }
}
