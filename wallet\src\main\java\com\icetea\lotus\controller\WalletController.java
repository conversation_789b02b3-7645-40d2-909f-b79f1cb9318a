package com.icetea.lotus.controller;


import com.icetea.lotus.config.security.CurrentUser;
import com.icetea.lotus.dto.WalletDto;
import com.icetea.lotus.dto.request.TransferRequestDto;
import com.icetea.lotus.entity.transform.AuthMember;
import com.icetea.lotus.service.WalletFutureService;
import com.icetea.lotus.util.MessageResult;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller cho quản lý ví
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/wallets")
@RequiredArgsConstructor
public class WalletController {

    private final WalletFutureService walletFutureService;

    @PostMapping("/transfer-future")
    public MessageResult transferFuture(@Valid @RequestBody TransferRequestDto requestDto) {
        log.info("transferFuture");
        WalletDto walletDto = walletFutureService.transferWalletFuture(requestDto);
        MessageResult result = MessageResult.success();
        result.setData(walletDto);
        return result;
    }
}
