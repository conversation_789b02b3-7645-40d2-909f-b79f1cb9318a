server.port=7001
spring.application.name=service-rpc-btc
#server.context-path=/
#kafka
spring.kafka.bootstrap-servers=${KAFKA_BOOTSTRAP_SERVER:************:9092}
spring.kafka.consumer.group-id=default-group
spring.kafka.template.default-topic=test
spring.kafka.listener.concurrency=1
spring.kafka.producer.batch-size=1000

# mongodb
spring.data.mongodb.uri=${MONGO_URI:*****************************************************

# Consul Host and Port
spring.cloud.consul.host=${CONSUL_HOST:************}
spring.cloud.consul.port=${CONSUL_PORT:8500}

# Enable Consul Config & Discovery
spring.cloud.consul.discovery.enabled=${CONSUL_DISCOVERY:true}
spring.cloud.consul.discovery.service-name=service-rpc-btc
#spring.cloud.consul.config.enabled=${CONSUL_CONFIG:true}
#spring.cloud.consul.config.fail-fast=false
spring.cloud.consul.discovery.register=true
spring.cloud.consul.discovery.instance-id=service-rpc-btc

# Optional: Add health check configuration
spring.cloud.consul.discovery.health-check-path=/actuator/health
spring.cloud.consul.discovery.prefer-ip-address=true
spring.cloud.consul.discovery.health-check-interval=10s

management.context-path=/actuator

# Actuator settings
endpoints.health.sensitive=false
endpoints.info.sensitive=false
endpoints.metrics.sensitive=false
management.security.enabled=false

# coin rpc
coin.rpc=${COIN_RPC:********************************************/}
coin.name=Bitcoin
coin.unit=BTC

#coin.init-block-height=8336120
#coin.step=10
#coin.withdraw-wallet=UTC--2019-08-13T06-24-07.378035684Z--672881426632b13d8f474664c039acc7b5610b7
#coin.withdraw-wallet-password=fdsafdsafdsafdsa
#coin.gas-limit=40000
#coin.min-collect-amount=0.1
#coin.ignore-from-address=0x672881426632b13d18f74664c039acc7b5610b7
watcher.init-block-height=latest
watcher.step=10
watcher.confirmation=0
watcher.interval=5000
