package com.icetea.lotus.core.domain.service.impl;

import com.icetea.lotus.core.domain.entity.Contract;
import com.icetea.lotus.core.domain.entity.OrderDirection;
import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.PositionDirection;
import com.icetea.lotus.core.domain.entity.PositionStatus;
import com.icetea.lotus.core.domain.entity.Trade;
import com.icetea.lotus.core.domain.exception.ValidationException;
import com.icetea.lotus.core.domain.repository.PositionRepository;
import com.icetea.lotus.core.domain.service.PositionService;
import com.icetea.lotus.core.domain.service.PriceManagementService;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Triển khai PositionService
 */
@Slf4j
@Service
@Transactional
public class PositionServiceImpl implements PositionService {

    private final PositionRepository positionRepository;
    private final PriceManagementService priceManagementService;

    public PositionServiceImpl(PositionRepository positionRepository, PriceManagementService priceManagementService) {
        this.positionRepository = positionRepository;
        this.priceManagementService = priceManagementService;
    }

    /**
     * Cập nhật vị thế sau khi có giao dịch mới với xử lý ngoại lệ và thử lại
     * @param position Vị thế hiện tại
     * @param trade Giao dịch mới
     * @param contract Hợp đồng
     * @return Vị thế đã được cập nhật
     */
    /**
     * Cập nhật vị thế sau khi có giao dịch mới với xử lý ngoại lệ và thử lại
     * @param position Vị thế hiện tại
     * @param trade Giao dịch mới
     * @param contract Hợp đồng
     * @return Vị thế đã được cập nhật
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Position updatePositionAfterTrade(Position position, Trade trade, Contract contract) {
        try {
            log.info("updatePositionAfterTrade==>Cập nhật vị thế sau khi có giao dịch mới, positionId = {}, tradeId = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    trade.getId() != null ? trade.getId().getValue() : "null");

            if (ObjectUtils.isEmpty(position)) {
                throw new ValidationException("Position không được để trống");
            }

            if (ObjectUtils.isEmpty(trade)) {
                throw new ValidationException("Trade không được để trống");
            }

            if (ObjectUtils.isEmpty(contract)) {
                throw new ValidationException("Contract không được để trống");
            }

            // Kiểm tra xem giao dịch có liên quan đến thành viên không
            if (!trade.involveMember(position.getMemberId())) {
                throw new ValidationException("Trade does not involve this member");
            }

            // Lấy hướng giao dịch cho thành viên
            OrderDirection tradeDirection = trade.getDirectionForMember(position.getMemberId());
            boolean isTradeOpeningPosition = false;
            boolean isTradeClosingPosition = false;

            // Xác định giao dịch là mở hay đóng vị thế
            if (position.getDirection() == PositionDirection.LONG && tradeDirection == OrderDirection.BUY) {
                isTradeOpeningPosition = true;
            } else if (position.getDirection() == PositionDirection.LONG && tradeDirection == OrderDirection.SELL) {
                isTradeClosingPosition = true;
            } else if (position.getDirection() == PositionDirection.SHORT && tradeDirection == OrderDirection.SELL) {
                isTradeOpeningPosition = true;
            } else if (position.getDirection() == PositionDirection.SHORT && tradeDirection == OrderDirection.BUY) {
                isTradeClosingPosition = true;
            }

            // Tính toán vị thế mới dựa trên loại giao dịch
            if (isTradeOpeningPosition) {
                // Mở rộng vị thế cùng hướng (giống Binance futures: tính lại giá vào trung bình)
                BigDecimal oldValue = position.getOpenPrice().getValue().multiply(position.getVolume());
                BigDecimal newValue = trade.getPrice().getValue().multiply(trade.getVolume());
                BigDecimal totalValue = oldValue.add(newValue);
                BigDecimal newVolume = position.getVolume().add(trade.getVolume());
                Money newOpenPrice = Money.of(totalValue.divide(newVolume, 8, RoundingMode.HALF_UP));

                // Cập nhật vị thế với giá vào trung bình mới
                return Position.builder()
                        .id(position.getId())
                        .memberId(position.getMemberId())
                        .symbol(position.getSymbol())
                        .direction(position.getDirection())
                        .volume(newVolume)
                        .openPrice(newOpenPrice)
                        .closePrice(null)
                        .liquidationPrice(calculateLiquidationPrice(position.getDirection(), newOpenPrice, contract))
                        .maintenanceMargin(calculateMaintenanceMargin(newOpenPrice, newVolume, contract))
                        .margin(calculateMargin(newOpenPrice, newVolume, position.getLeverage()))
                        .profit(null)
                        .marginMode(position.getMarginMode())
                        .leverage(position.getLeverage())
                        .createTime(position.getCreateTime())
                        .updateTime(LocalDateTime.now())
                        .remark(position.getRemark())
                        .status(PositionStatus.OPEN)
                        .build();
            } else if (isTradeClosingPosition) {
                // Giảm hoặc đóng vị thế
                if (trade.getVolume().compareTo(position.getVolume()) < 0) {
                    // Đóng một phần vị thế - giữ nguyên giá vào (không thay đổi giá vào trung bình)
                    BigDecimal newVolume = position.getVolume().subtract(trade.getVolume());

                    // Tính toán lợi nhuận từ phần vị thế đã đóng
                    Money partialProfit = calculatePartialProfit(position, trade.getPrice(), trade.getVolume());

                    return Position.builder()
                            .id(position.getId())
                            .memberId(position.getMemberId())
                            .symbol(position.getSymbol())
                            .direction(position.getDirection())
                            .volume(newVolume)
                            .openPrice(position.getOpenPrice()) // Giữ nguyên giá vào khi đóng một phần
                            .closePrice(null)
                            .liquidationPrice(calculateLiquidationPrice(position.getDirection(), position.getOpenPrice(), contract))
                            .maintenanceMargin(calculateMaintenanceMargin(position.getOpenPrice(), newVolume, contract))
                            .margin(calculateMargin(position.getOpenPrice(), newVolume, position.getLeverage()))
                            .profit(partialProfit) // Ghi nhận lợi nhuận từ phần đã đóng
                            .marginMode(position.getMarginMode())
                            .leverage(position.getLeverage())
                            .createTime(position.getCreateTime())
                            .updateTime(LocalDateTime.now())
                            .remark(position.getRemark())
                            .status(PositionStatus.OPEN)
                            .build();
                } else if (trade.getVolume().compareTo(position.getVolume()) == 0) {
                    // Đóng hoàn toàn vị thế
                    Money totalProfit = calculateProfit(position, trade.getPrice());

                    return Position.builder()
                            .id(position.getId())
                            .memberId(position.getMemberId())
                            .symbol(position.getSymbol())
                            .direction(position.getDirection())
                            .volume(BigDecimal.ZERO)
                            .openPrice(position.getOpenPrice())
                            .closePrice(trade.getPrice())
                            .liquidationPrice(Money.ZERO)
                            .maintenanceMargin(Money.ZERO)
                            .margin(Money.ZERO)
                            .profit(totalProfit)
                            .marginMode(position.getMarginMode())
                            .leverage(position.getLeverage())
                            .createTime(position.getCreateTime())
                            .updateTime(LocalDateTime.now())
                            .remark(position.getRemark())
                            .status(PositionStatus.CLOSED) // Đánh dấu vị thế đã đóng
                            .build();
                } else {
                    // Đảo ngược vị thế (đóng hoàn toàn vị thế hiện tại và mở vị thế mới theo hướng ngược lại)
                    Money totalProfit = calculateProfit(position, trade.getPrice());
                    BigDecimal newVolume = trade.getVolume().subtract(position.getVolume());
                    PositionDirection newDirection = position.getDirection() == PositionDirection.LONG ?
                            PositionDirection.SHORT : PositionDirection.LONG;

                    // Tạo vị thế mới với hướng ngược lại
                    return Position.builder()
                            .id(position.getId())
                            .memberId(position.getMemberId())
                            .symbol(position.getSymbol())
                            .direction(newDirection) // Đảo chiều vị thế
                            .volume(newVolume)
                            .openPrice(trade.getPrice()) // Giá vào mới là giá của giao dịch hiện tại
                            .closePrice(null)
                            .liquidationPrice(calculateLiquidationPrice(newDirection, trade.getPrice(), contract))
                            .maintenanceMargin(calculateMaintenanceMargin(trade.getPrice(), newVolume, contract))
                            .margin(calculateMargin(trade.getPrice(), newVolume, position.getLeverage()))
                            .profit(totalProfit) // Ghi nhận lợi nhuận từ việc đóng vị thế cũ
                            .marginMode(position.getMarginMode())
                            .leverage(position.getLeverage())
                            .createTime(position.getCreateTime())
                            .updateTime(LocalDateTime.now())
                            .remark(position.getRemark())
                            .status(PositionStatus.OPEN)
                            .build();
                }
            } else {
                // Trường hợp không xác định được loại giao dịch
                throw new ValidationException("Không thể xác định loại giao dịch cho vị thế");
            }
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi cập nhật vị thế sau khi có giao dịch mới, positionId = {}, tradeId = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    trade.getId() != null ? trade.getId().getValue() : "null", e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi cập nhật vị thế sau khi có giao dịch mới: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            log.error("Lỗi tính toán khi cập nhật vị thế sau khi có giao dịch mới, positionId = {}, tradeId = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    trade.getId() != null ? trade.getId().getValue() : "null", e);
            throw new BusinessException("Lỗi tính toán khi cập nhật vị thế: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi cập nhật vị thế sau khi có giao dịch mới, positionId = {}, tradeId = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    trade.getId() != null ? trade.getId().getValue() : "null", e);
            throw new BusinessException("Lỗi khi cập nhật vị thế: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi cập nhật vị thế sau khi có giao dịch mới thất bại
     * @param e Ngoại lệ
     * @param position Vị thế hiện tại
     * @param trade Giao dịch mới
     * @param contract Hợp đồng
     * @return Position
     */
    @Recover
    public Position recoverUpdatePositionAfterTrade(Exception e, Position position, Trade trade, Contract contract) {
        log.error("Đã thử lại cập nhật vị thế sau khi có giao dịch mới 3 lần nhưng thất bại, positionId = {}, tradeId = {}",
                position.getId() != null ? position.getId().getValue() : "null",
                trade.getId() != null ? trade.getId().getValue() : "null", e);
        throw new BusinessException("Không thể cập nhật vị thế sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tạo vị thế mới từ giao dịch
     * @param trade Giao dịch
     * @param memberId ID thành viên
     * @param contract Hợp đồng
     * @return Vị thế mới được tạo
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Position createPositionFromTrade(Trade trade, Long memberId, Contract contract) {
        try {
            log.info("createPositionFromTrade==>Tạo vị thế mới từ giao dịch, tradeId = {}, memberId = {}",
                    trade.getId() != null ? trade.getId().getValue() : "null", memberId);

            if (ObjectUtils.isEmpty(trade)) {
                throw new ValidationException("Trade không được để trống");
            }

            if (ObjectUtils.isEmpty(memberId)) {
                throw new ValidationException("MemberId không được để trống");
            }

            if (ObjectUtils.isEmpty(contract)) {
                throw new ValidationException("Contract không được để trống");
            }

            // Kiểm tra xem giao dịch có liên quan đến thành viên không
            if (!trade.involveMember(memberId)) {
                throw new ValidationException("Trade does not involve this member");
            }

            // Lấy hướng giao dịch cho thành viên
            OrderDirection tradeDirection = trade.getDirectionForMember(memberId);
            PositionDirection positionDirection = tradeDirection == OrderDirection.BUY ?
                    PositionDirection.LONG : PositionDirection.SHORT;

            // Tạo vị thế mới với các tính toán phù hợp với Binance futures
            Money entryPrice = trade.getPrice();
            BigDecimal volume = trade.getVolume();
            BigDecimal leverage = trade.getLeverage();

            return Position.builder()
                    .id(null) // ID sẽ được tạo khi lưu vào database
                    .memberId(memberId)
                    .symbol(trade.getSymbol())
                    .direction(positionDirection)
                    .volume(volume)
                    .openPrice(entryPrice)
                    .closePrice(null)
                    .liquidationPrice(calculateLiquidationPrice(positionDirection, entryPrice, contract))
                    .maintenanceMargin(calculateMaintenanceMargin(entryPrice, volume, contract))
                    .margin(calculateMargin(entryPrice, volume, leverage))
                    .profit(null)
                    .marginMode(contract.getMarginMode())
                    .leverage(leverage)
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .remark(null)
                    .status(PositionStatus.OPEN) // Thiết lập trạng thái mặc định là OPEN
                    .build();
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tạo vị thế mới từ giao dịch, tradeId = {}, memberId = {}",
                    trade.getId() != null ? trade.getId().getValue() : "null", memberId, e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi tạo vị thế mới từ giao dịch: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            log.error("Lỗi tính toán khi tạo vị thế mới từ giao dịch, tradeId = {}, memberId = {}",
                    trade.getId() != null ? trade.getId().getValue() : "null", memberId, e);
            throw new BusinessException("Lỗi tính toán khi tạo vị thế mới: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tạo vị thế mới từ giao dịch, tradeId = {}, memberId = {}",
                    trade.getId() != null ? trade.getId().getValue() : "null", memberId, e);
            throw new BusinessException("Lỗi khi tạo vị thế mới: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tạo vị thế mới từ giao dịch thất bại
     * @param e Ngoại lệ
     * @param trade Giao dịch
     * @param memberId ID của thành viên
     * @param contract Hợp đồng
     * @return Position
     */
    @Recover
    public Position recoverCreatePositionFromTrade(Exception e, Trade trade, Long memberId, Contract contract) {
        log.error("Đã thử lại tạo vị thế mới từ giao dịch 3 lần nhưng thất bại, tradeId = {}, memberId = {}",
                trade.getId() != null ? trade.getId().getValue() : "null", memberId, e);
        throw new BusinessException("Không thể tạo vị thế mới sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tính toán giá thanh lý cho vị thế với xử lý ngoại lệ và thử lại
     * @param position Vị thế
     * @param contract Hợp đồng
     * @return Giá thanh lý
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Money calculateLiquidationPrice(Position position, Contract contract) {
        try {
            log.debug("Tính toán giá thanh lý cho vị thế, positionId = {}",
                    position.getId() != null ? position.getId().getValue() : "null");

            if (ObjectUtils.isEmpty(position)) {
                throw new ValidationException("Position không được để trống");
            }

            if (ObjectUtils.isEmpty(contract)) {
                throw new ValidationException("Contract không được để trống");
            }

            if (position.getVolume().compareTo(BigDecimal.ZERO) == 0) {
                return Money.ZERO;
            }

            Money liquidationPrice = position.calculateLiquidationPrice(contract.getMaintenanceMarginRate().getValue());

            log.debug("Đã tính toán giá thanh lý thành công cho vị thế, positionId = {}, liquidationPrice = {}",
                    position.getId().getValue(), liquidationPrice.getValue());

            return liquidationPrice;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tính toán giá thanh lý cho vị thế, positionId = {}",
                    position.getId() != null ? position.getId().getValue() : "null", e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi tính toán giá thanh lý cho vị thế: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            log.error("Lỗi tính toán khi tính toán giá thanh lý cho vị thế, positionId = {}",
                    position.getId() != null ? position.getId().getValue() : "null", e);
            throw new BusinessException("Lỗi tính toán khi tính toán giá thanh lý: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tính toán giá thanh lý cho vị thế, positionId = {}",
                    position.getId() != null ? position.getId().getValue() : "null", e);
            throw new BusinessException("Lỗi khi tính toán giá thanh lý: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tính toán giá thanh lý cho vị thế thất bại
     * @param e Ngoại lệ
     * @param position Vị thế
     * @param contract Hợp đồng
     * @return Money
     */
    @Recover
    public Money recoverCalculateLiquidationPrice(Exception e, Position position, Contract contract) {
        log.error("Đã thử lại tính toán giá thanh lý cho vị thế 3 lần nhưng thất bại, positionId = {}",
                position.getId() != null ? position.getId().getValue() : "null", e);
        return Money.ZERO;
    }

    /**
     * Tính toán giá thanh lý dựa trên hướng vị thế, giá vào và thông tin hợp đồng
     * Sử dụng công thức Binance chính xác
     * @param direction Hướng vị thế
     * @param entryPrice Giá vào
     * @param contract Hợp đồng
     * @return Giá thanh lý
     */
    private Money calculateLiquidationPrice(PositionDirection direction, Money entryPrice, Contract contract) {
        // ✅ SỬ DỤNG công thức đúng từ Position entity thay vì công thức sai
        // Tạo temporary position với volume = 1 để tính toán chuẩn
        BigDecimal volume = BigDecimal.ONE;
        BigDecimal leverage = BigDecimal.valueOf(contract.getLeverageMax()); // Sử dụng leverage mặc định

        // Tính margin = (Entry Price × Volume) / Leverage
        Money margin = Money.of(entryPrice.getValue().multiply(volume).divide(leverage, 8, RoundingMode.HALF_UP));

        // Tạo temporary position để sử dụng công thức đúng
        Position tempPosition = Position.builder()
                .direction(direction)
                .openPrice(entryPrice)
                .volume(volume)
                .margin(margin)
                .build();

        // Sử dụng công thức chính xác từ Position entity
        return tempPosition.calculateLiquidationPrice(contract.getMaintenanceMarginRate().getValue());
    }

    /**
     * Tính toán maintenance margin dựa trên giá vào, khối lượng và thông tin hợp đồng
     */
    private Money calculateMaintenanceMargin(Money entryPrice, BigDecimal volume, Contract contract) {
        BigDecimal maintenanceMargin = contract.getMaintenanceMarginRate().getValue()
                .multiply(entryPrice.getValue())
                .multiply(volume);

        // Giới hạn giá trị maintenance margin để không vượt quá giới hạn của cơ sở dữ liệu (numeric(18,8))
        // Giới hạn là 10^10 - 0.00000001 = 9999999999.99999999
        BigDecimal maxValue = new BigDecimal("9999999999.99999999");
        if (maintenanceMargin.compareTo(maxValue) > 0) {
            log.warn("Maintenance margin value exceeds database limit, capping at maximum value. Original: {}, Capped: {}",
                    maintenanceMargin, maxValue);
            maintenanceMargin = maxValue;
        }

        return Money.of(maintenanceMargin);
    }

    /**
     * Tính toán margin dựa trên giá vào, khối lượng và đòn bẩy
     */
    private Money calculateMargin(Money entryPrice, BigDecimal volume, BigDecimal leverage) {
        if (BigDecimal.ZERO.compareTo(volume) == 0 || BigDecimal.ZERO.compareTo(leverage) == 0) {
            return Money.ZERO;
        }

        BigDecimal margin = entryPrice.getValue()
                .multiply(volume)
                .divide(leverage, 8, RoundingMode.HALF_UP);

        // Giới hạn giá trị margin để không vượt quá giới hạn của cơ sở dữ liệu (numeric(18,8))
        // Giới hạn là 10^10 - 0.00000001 = 9999999999.99999999
        BigDecimal maxValue = new BigDecimal("9999999999.99999999");
        if (margin.compareTo(maxValue) > 0) {
            log.warn("Margin value exceeds database limit, capping at maximum value. Original: {}, Capped: {}",
                    margin, maxValue);
            margin = maxValue;
        }

        return Money.of(margin);
    }

    /**
     * Tính toán lợi nhuận cho một phần vị thế đã đóng
     */
    private Money calculatePartialProfit(Position position, Money closePrice, BigDecimal closedVolume) {
        BigDecimal profit;
        if (position.getDirection() == PositionDirection.LONG) {
            // Long: (ClosePrice - EntryPrice) * ClosedVolume
            profit = closePrice.getValue().subtract(position.getOpenPrice().getValue()).multiply(closedVolume);
        } else {
            // Short: (EntryPrice - ClosePrice) * ClosedVolume
            profit = position.getOpenPrice().getValue().subtract(closePrice.getValue()).multiply(closedVolume);
        }
        return Money.of(profit);
    }

    /**
     * Kiểm tra xem vị thế có cần thanh lý không với xử lý ngoại lệ và thử lại
     * @param position Vị thế
     * @param currentPrice Giá hiện tại
     * @param contract Hợp đồng
     * @return true nếu vị thế cần thanh lý
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public boolean needsLiquidation(Position position, Money currentPrice, Contract contract) {
        try {
            log.debug("Kiểm tra xem vị thế có cần thanh lý không, positionId = {}, currentPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    currentPrice != null ? currentPrice.getValue() : "null");

            if (position == null) {
                throw new ValidationException("Position không được để trống");
            }

            if (currentPrice == null) {
                throw new ValidationException("CurrentPrice không được để trống");
            }

            if (contract == null) {
                throw new ValidationException("Contract không được để trống");
            }

            boolean needsLiquidation = position.needsLiquidation(currentPrice, contract.getMaintenanceMarginRate().getValue());

            log.debug("Kết quả kiểm tra thanh lý vị thế, positionId = {}, needsLiquidation = {}",
                    position.getId().getValue(), needsLiquidation);

            return needsLiquidation;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi kiểm tra thanh lý vị thế, positionId = {}, currentPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    currentPrice != null ? currentPrice.getValue() : "null", e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi kiểm tra thanh lý vị thế: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi kiểm tra thanh lý vị thế, positionId = {}, currentPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    currentPrice != null ? currentPrice.getValue() : "null", e);
            throw new BusinessException("Lỗi khi kiểm tra thanh lý vị thế: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi kiểm tra thanh lý vị thế thất bại
     * @param e Ngoại lệ
     * @param position Vị thế
     * @param currentPrice Giá hiện tại
     * @param contract Hợp đồng
     * @return false
     */
    @Recover
    public boolean recoverNeedsLiquidation(Exception e, Position position, Money currentPrice, Contract contract) {
        log.error("Đã thử lại kiểm tra thanh lý vị thế 3 lần nhưng thất bại, positionId = {}, currentPrice = {}",
                position.getId() != null ? position.getId().getValue() : "null",
                currentPrice != null ? currentPrice.getValue() : "null", e);
        return false; // Mặc định không thanh lý nếu có lỗi
    }

    /**
     * Tính toán lợi nhuận không thực hiện cho vị thế với xử lý ngoại lệ và thử lại
     * @param position Vị thế
     * @param currentPrice Giá hiện tại
     * @return Lợi nhuận không thực hiện
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Money calculateUnrealizedProfit(Position position, Money currentPrice) {
        try {
            log.debug("Tính toán lợi nhuận không thực hiện cho vị thế, positionId = {}, currentPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    currentPrice != null ? currentPrice.getValue() : "null");

            if (position == null) {
                throw new ValidationException("Position không được để trống");
            }

            if (currentPrice == null) {
                throw new ValidationException("CurrentPrice không được để trống");
            }

            Money unrealizedProfit = position.calculateUnrealizedProfit(currentPrice);

            log.debug("Đã tính toán lợi nhuận không thực hiện thành công cho vị thế, positionId = {}, unrealizedProfit = {}",
                    position.getId().getValue(), unrealizedProfit.getValue());

            return unrealizedProfit;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tính toán lợi nhuận không thực hiện cho vị thế, positionId = {}, currentPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    currentPrice != null ? currentPrice.getValue() : "null", e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi tính toán lợi nhuận không thực hiện cho vị thế: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            log.error("Lỗi tính toán khi tính toán lợi nhuận không thực hiện cho vị thế, positionId = {}, currentPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    currentPrice != null ? currentPrice.getValue() : "null", e);
            return Money.ZERO;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tính toán lợi nhuận không thực hiện cho vị thế, positionId = {}, currentPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    currentPrice != null ? currentPrice.getValue() : "null", e);
            throw new BusinessException("Lỗi khi tính toán lợi nhuận không thực hiện: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tính toán lợi nhuận không thực hiện cho vị thế thất bại
     * @param e Ngoại lệ
     * @param position Vị thế
     * @param currentPrice Giá hiện tại
     * @return Money.ZERO
     */
    @Recover
    public Money recoverCalculateUnrealizedProfit(Exception e, Position position, Money currentPrice) {
        log.error("Đã thử lại tính toán lợi nhuận không thực hiện cho vị thế 3 lần nhưng thất bại, positionId = {}, currentPrice = {}",
                position.getId() != null ? position.getId().getValue() : "null",
                currentPrice != null ? currentPrice.getValue() : "null", e);
        return Money.ZERO;
    }

    /**
     * Tính toán tỷ lệ lợi nhuận cho vị thế với xử lý ngoại lệ và thử lại
     * @param position Vị thế
     * @param currentPrice Giá hiện tại
     * @return Tỷ lệ lợi nhuận
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public BigDecimal calculateProfitRatio(Position position, Money currentPrice) {
        try {
            log.debug("Tính toán tỷ lệ lợi nhuận cho vị thế, positionId = {}, currentPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    currentPrice != null ? currentPrice.getValue() : "null");

            if (position == null) {
                throw new ValidationException("Position không được để trống");
            }

            if (currentPrice == null) {
                throw new ValidationException("CurrentPrice không được để trống");
            }

            BigDecimal profitRatio = position.calculateProfitRatio(currentPrice);

            log.debug("Đã tính toán tỷ lệ lợi nhuận thành công cho vị thế, positionId = {}, profitRatio = {}",
                    position.getId().getValue(), profitRatio);

            return profitRatio;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tính toán tỷ lệ lợi nhuận cho vị thế, positionId = {}, currentPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    currentPrice != null ? currentPrice.getValue() : "null", e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi tính toán tỷ lệ lợi nhuận cho vị thế: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            log.error("Lỗi tính toán khi tính toán tỷ lệ lợi nhuận cho vị thế, positionId = {}, currentPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    currentPrice != null ? currentPrice.getValue() : "null", e);
            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tính toán tỷ lệ lợi nhuận cho vị thế, positionId = {}, currentPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    currentPrice != null ? currentPrice.getValue() : "null", e);
            throw new BusinessException("Lỗi khi tính toán tỷ lệ lợi nhuận: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tính toán tỷ lệ lợi nhuận cho vị thế thất bại
     * @param e Ngoại lệ
     * @param position Vị thế
     * @param currentPrice Giá hiện tại
     * @return BigDecimal.ZERO
     */
    @Recover
    public BigDecimal recoverCalculateProfitRatio(Exception e, Position position, Money currentPrice) {
        log.error("Đã thử lại tính toán tỷ lệ lợi nhuận cho vị thế 3 lần nhưng thất bại, positionId = {}, currentPrice = {}",
                position.getId() != null ? position.getId().getValue() : "null",
                currentPrice != null ? currentPrice.getValue() : "null", e);
        return BigDecimal.ZERO;
    }

    /**
     * Tính toán tổng giá trị vị thế cho một thành viên với xử lý ngoại lệ và thử lại
     * @param positions Danh sách các vị thế
     * @param currentPrices Giá hiện tại cho mỗi symbol
     * @return Tổng giá trị vị thế
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Money calculateTotalPositionValue(List<Position> positions, Map<Symbol, Money> currentPrices) {
        try {
            log.debug("Tính toán tổng giá trị vị thế, positions.size = {}, currentPrices.size = {}",
                    positions != null ? positions.size() : 0,
                    currentPrices != null ? currentPrices.size() : 0);

            if (positions == null) {
                throw new ValidationException("Positions không được để trống");
            }

            if (currentPrices == null) {
                throw new ValidationException("CurrentPrices không được để trống");
            }

            Money totalValue = Money.ZERO;

            for (Position position : positions) {
                if (position != null && position.getSymbol() != null) {
                    Money currentPrice = currentPrices.get(position.getSymbol());
                    if (currentPrice != null && position.getVolume() != null && currentPrice.getValue() != null) {
                        // Tính toán giá trị vị thế và kiểm tra giới hạn
                        BigDecimal positionValue = position.getVolume().multiply(currentPrice.getValue());

                        // Giới hạn giá trị để không vượt quá giới hạn của cơ sở dữ liệu (numeric(18,8))
                        BigDecimal maxValue = new BigDecimal("9999999999.99999999");
                        if (positionValue.compareTo(maxValue) > 0) {
                            log.warn("Position value exceeds database limit, capping at maximum value. Symbol: {}, Original: {}, Capped: {}",
                                    position.getSymbol().getValue(), positionValue, maxValue);
                            positionValue = maxValue;
                        }

                        totalValue = totalValue.add(Money.of(positionValue));
                    }
                }
            }

            log.debug("Đã tính toán tổng giá trị vị thế thành công, totalValue = {}", totalValue.getValue());

            return totalValue;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tính toán tổng giá trị vị thế, positions.size = {}, currentPrices.size = {}",
                    positions != null ? positions.size() : 0,
                    currentPrices != null ? currentPrices.size() : 0, e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi tính toán tổng giá trị vị thế: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            log.error("Lỗi tính toán khi tính toán tổng giá trị vị thế", e);
            return Money.ZERO;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tính toán tổng giá trị vị thế", e);
            throw new BusinessException("Lỗi khi tính toán tổng giá trị vị thế: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tính toán tổng giá trị vị thế thất bại
     * @param e Ngoại lệ
     * @param positions Danh sách các vị thế
     * @param currentPrices Giá hiện tại cho mỗi symbol
     * @return Money.ZERO
     */
    @Recover
    public Money recoverCalculateTotalPositionValue(Exception e, List<Position> positions, Map<Symbol, Money> currentPrices) {
        log.error("Đã thử lại tính toán tổng giá trị vị thế 3 lần nhưng thất bại", e);
        return Money.ZERO;
    }

    /**
     * Tính toán tổng lợi nhuận không thực hiện cho một thành viên với xử lý ngoại lệ và thử lại
     * @param positions Danh sách các vị thế
     * @param currentPrices Giá hiện tại cho mỗi symbol
     * @return Tổng lợi nhuận không thực hiện
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Money calculateTotalUnrealizedProfit(List<Position> positions, Map<Symbol, Money> currentPrices) {
        try {
            log.debug("Tính toán tổng lợi nhuận không thực hiện, positions.size = {}, currentPrices.size = {}",
                    positions != null ? positions.size() : 0,
                    currentPrices != null ? currentPrices.size() : 0);

            if (positions == null) {
                throw new ValidationException("Positions không được để trống");
            }

            if (currentPrices == null) {
                throw new ValidationException("CurrentPrices không được để trống");
            }

            Money totalProfit = Money.ZERO;

            for (Position position : positions) {
                if (position != null && position.getSymbol() != null) {
                    Money currentPrice = currentPrices.get(position.getSymbol());
                    if (currentPrice != null) {
                        totalProfit = totalProfit.add(calculateUnrealizedProfit(position, currentPrice));
                    }
                }
            }

            log.debug("Đã tính toán tổng lợi nhuận không thực hiện thành công, totalProfit = {}", totalProfit.getValue());

            return totalProfit;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tính toán tổng lợi nhuận không thực hiện, positions.size = {}, currentPrices.size = {}",
                    positions != null ? positions.size() : 0,
                    currentPrices != null ? currentPrices.size() : 0, e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi tính toán tổng lợi nhuận không thực hiện: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            log.error("Lỗi tính toán khi tính toán tổng lợi nhuận không thực hiện", e);
            return Money.ZERO;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tính toán tổng lợi nhuận không thực hiện", e);
            throw new BusinessException("Lỗi khi tính toán tổng lợi nhuận không thực hiện: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi tính toán tổng lợi nhuận không thực hiện thất bại
     * @param e Ngoại lệ
     * @param positions Danh sách các vị thế
     * @param currentPrices Giá hiện tại cho mỗi symbol
     * @return Money.ZERO
     */
    @Recover
    public Money recoverCalculateTotalUnrealizedProfit(Exception e, List<Position> positions, Map<Symbol, Money> currentPrices) {
        log.error("Đã thử lại tính toán tổng lợi nhuận không thực hiện 3 lần nhưng thất bại", e);
        return Money.ZERO;
    }

    /**
     * Đóng vị thế với xử lý ngoại lệ và thử lại
     * @param position Vị thế cần đóng
     * @param closePrice Giá đóng
     * @param closeVolume Khối lượng đóng
     * @return Vị thế đã được cập nhật
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Position closePosition(Position position, Money closePrice, BigDecimal closeVolume) {
        try {
            log.debug("Đóng vị thế, positionId = {}, closePrice = {}, closeVolume = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    closePrice != null ? closePrice.getValue() : "null",
                    closeVolume);

            if (position == null) {
                throw new ValidationException("Position không được để trống");
            }

            if (closePrice == null) {
                throw new ValidationException("ClosePrice không được để trống");
            }

            if (closeVolume == null) {
                throw new ValidationException("CloseVolume không được để trống");
            }

            if (closeVolume.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ValidationException("CloseVolume phải lớn hơn 0");
            }

            if (closeVolume.compareTo(position.getVolume()) > 0) {
                throw new ValidationException("CloseVolume không thể lớn hơn khối lượng vị thế");
            }

            Position updatedPosition;

            if (closeVolume.compareTo(position.getVolume()) == 0) {
                // Đóng toàn bộ vị thế
                log.debug("Đóng toàn bộ vị thế, positionId = {}", position.getId().getValue());

                updatedPosition = Position.builder()
                        .id(position.getId())
                        .memberId(position.getMemberId())
                        .symbol(position.getSymbol())
                        .direction(position.getDirection())
                        .volume(BigDecimal.ZERO)
                        .openPrice(position.getOpenPrice())
                        .closePrice(closePrice)
                        .liquidationPrice(Money.ZERO)
                        .maintenanceMargin(Money.ZERO)
                        .margin(Money.ZERO)
                        .profit(calculateProfit(position, closePrice))
                        .marginMode(position.getMarginMode())
                        .leverage(position.getLeverage())
                        .createTime(position.getCreateTime())
                        .updateTime(LocalDateTime.now())
                        .remark(position.getRemark())
                        .status(PositionStatus.CLOSED)
                        .build();
            } else {
                // Đóng một phần vị thế
                log.debug("Đóng một phần vị thế, positionId = {}, closeVolume = {}",
                        position.getId().getValue(), closeVolume);

                BigDecimal remainingVolume = position.getVolume().subtract(closeVolume);

                updatedPosition = Position.builder()
                        .id(position.getId())
                        .memberId(position.getMemberId())
                        .symbol(position.getSymbol())
                        .direction(position.getDirection())
                        .volume(remainingVolume)
                        .openPrice(position.getOpenPrice())
                        .closePrice(null)
                        .liquidationPrice(position.getLiquidationPrice())
                        .maintenanceMargin(Money.of(position.getMaintenanceMargin().getValue().multiply(remainingVolume).divide(position.getVolume(), 8, RoundingMode.HALF_UP)))
                        .margin(Money.of(position.getMargin().getValue().multiply(remainingVolume).divide(position.getVolume(), 8, RoundingMode.HALF_UP)))
                        .profit(null)
                        .marginMode(position.getMarginMode())
                        .leverage(position.getLeverage())
                        .createTime(position.getCreateTime())
                        .updateTime(LocalDateTime.now())
                        .remark(position.getRemark())
                        .status(PositionStatus.CLOSED)
                        .build();
            }

            log.debug("Đã đóng vị thế thành công, positionId = {}", position.getId().getValue());

            return updatedPosition;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi đóng vị thế, positionId = {}, closePrice = {}, closeVolume = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    closePrice != null ? closePrice.getValue() : "null",
                    closeVolume, e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi đóng vị thế: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            log.error("Lỗi tính toán khi đóng vị thế, positionId = {}, closePrice = {}, closeVolume = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    closePrice != null ? closePrice.getValue() : "null",
                    closeVolume, e);
            throw new BusinessException("Lỗi tính toán khi đóng vị thế: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Lỗi không xác định khi đóng vị thế, positionId = {}, closePrice = {}, closeVolume = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    closePrice != null ? closePrice.getValue() : "null",
                    closeVolume, e);
            throw new BusinessException("Lỗi khi đóng vị thế: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi đóng vị thế thất bại
     * @param e Ngoại lệ
     * @param position Vị thế cần đóng
     * @param closePrice Giá đóng
     * @param closeVolume Khối lượng đóng
     * @return Position
     */
    @Recover
    public Position recoverClosePosition(Exception e, Position position, Money closePrice, BigDecimal closeVolume) {
        log.error("Đã thử lại đóng vị thế 3 lần nhưng thất bại, positionId = {}, closePrice = {}, closeVolume = {}",
                position.getId() != null ? position.getId().getValue() : "null",
                closePrice != null ? closePrice.getValue() : "null",
                closeVolume, e);
        throw new BusinessException("Không thể đóng vị thế sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Đảo ngược vị thế với xử lý ngoại lệ và thử lại
     * @param position Vị thế cần đảo ngược
     * @param newDirection Hướng mới
     * @param newVolume Khối lượng mới
     * @param newPrice Giá mới
     * @return Vị thế đã được cập nhật
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Position flipPosition(Position position, PositionDirection newDirection, BigDecimal newVolume, Money newPrice) {
        try {
            log.debug("Đảo ngược vị thế, positionId = {}, newDirection = {}, newVolume = {}, newPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    newDirection,
                    newVolume,
                    newPrice != null ? newPrice.getValue() : "null");

            if (position == null) {
                throw new ValidationException("Position không được để trống");
            }

            if (newDirection == null) {
                throw new ValidationException("NewDirection không được để trống");
            }

            if (newVolume == null) {
                throw new ValidationException("NewVolume không được để trống");
            }

            if (newPrice == null) {
                throw new ValidationException("NewPrice không được để trống");
            }

            if (newVolume.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ValidationException("NewVolume phải lớn hơn 0");
            }

            if (position.getDirection() == newDirection) {
                throw new ValidationException("Hướng mới phải khác với hướng hiện tại");
            }

            Position flippedPosition = Position.builder()
                    .id(position.getId())
                    .memberId(position.getMemberId())
                    .symbol(position.getSymbol())
                    .direction(newDirection)
                    .volume(newVolume)
                    .openPrice(newPrice)
                    .closePrice(null)
                    .liquidationPrice(null) // Sẽ được tính toán sau khi lưu vào database
                    .maintenanceMargin(null) // Sẽ được tính toán sau khi lưu vào database
                    .margin(null) // Sẽ được tính toán sau khi lưu vào database
                    .profit(null)
                    .marginMode(position.getMarginMode())
                    .leverage(position.getLeverage())
                    .createTime(position.getCreateTime())
                    .updateTime(LocalDateTime.now())
                    .remark(position.getRemark())
                    .build();

            log.debug("Đã đảo ngược vị thế thành công, positionId = {}", position.getId().getValue());

            return flippedPosition;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi đảo ngược vị thế, positionId = {}, newDirection = {}, newVolume = {}, newPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    newDirection,
                    newVolume,
                    newPrice != null ? newPrice.getValue() : "null", e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi đảo ngược vị thế: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi đảo ngược vị thế, positionId = {}, newDirection = {}, newVolume = {}, newPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    newDirection,
                    newVolume,
                    newPrice != null ? newPrice.getValue() : "null", e);
            throw new BusinessException("Lỗi khi đảo ngược vị thế: " + e.getMessage(), e);
        }
    }

    /**
     * Phục hồi khi đảo ngược vị thế thất bại
     * @param e Ngoại lệ
     * @param position Vị thế cần đảo ngược
     * @param newDirection Hướng mới
     * @param newVolume Khối lượng mới
     * @param newPrice Giá mới
     * @return Position
     */
    @Recover
    public Position recoverFlipPosition(Exception e, Position position, PositionDirection newDirection, BigDecimal newVolume, Money newPrice) {
        log.error("Đã thử lại đảo ngược vị thế 3 lần nhưng thất bại, positionId = {}, newDirection = {}, newVolume = {}, newPrice = {}",
                position.getId() != null ? position.getId().getValue() : "null",
                newDirection,
                newVolume,
                newPrice != null ? newPrice.getValue() : "null", e);
        throw new BusinessException("Không thể đảo ngược vị thế sau 3 lần thử lại: " + e.getMessage(), e);
    }

    /**
     * Tính toán lợi nhuận cho vị thế với xử lý ngoại lệ
     * @param position Vị thế
     * @param closePrice Giá đóng
     * @return Lợi nhuận
     */
    private Money calculateProfit(Position position, Money closePrice) {
        BigDecimal profit;
        if (position.getDirection() == PositionDirection.LONG) {
            // Long: (ClosePrice - EntryPrice) * Volume
            profit = closePrice.getValue().subtract(position.getOpenPrice().getValue()).multiply(position.getVolume());
        } else {
            // Short: (EntryPrice - ClosePrice) * Volume
            profit = position.getOpenPrice().getValue().subtract(closePrice.getValue()).multiply(position.getVolume());
        }

        // Giới hạn giá trị profit để không vượt quá giới hạn của cơ sở dữ liệu (numeric(18,8))
        BigDecimal maxValue = new BigDecimal("9999999999.99999999");
        BigDecimal minValue = maxValue.negate();

        if (profit.compareTo(maxValue) > 0) {
            log.warn("Profit value exceeds database limit, capping at maximum value. PositionId: {}, Original: {}, Capped: {}",
                    position.getId() != null ? position.getId().getValue() : "null", profit, maxValue);
            profit = maxValue;
        } else if (profit.compareTo(minValue) < 0) {
            log.warn("Profit value exceeds database limit, capping at minimum value. PositionId: {}, Original: {}, Capped: {}",
                    position.getId() != null ? position.getId().getValue() : "null", profit, minValue);
            profit = minValue;
        }

        return Money.of(profit);
    }

    /**
     * Tìm các vị thế có lợi nhuận cao nhất theo symbol và hướng
     * @param symbol Symbol của hợp đồng
     * @param direction Hướng của vị thế
     * @param limit Số lượng vị thế tối đa
     * @return Danh sách các vị thế có lợi nhuận cao nhất
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<Position> findProfitablePositionsBySymbolAndDirection(Symbol symbol, PositionDirection direction, int limit) {
        try {
            log.debug("Tìm các vị thế có lợi nhuận cao nhất theo symbol và hướng, symbol = {}, direction = {}, limit = {}",
                    symbol.getValue(), direction, limit);

            if (symbol == null) {
                throw new ValidationException("Symbol không được để trống");
            }

            if (direction == null) {
                throw new ValidationException("Direction không được để trống");
            }

            if (limit <= 0) {
                throw new ValidationException("Limit phải lớn hơn 0");
            }

            // Lấy giá đánh dấu hiện tại
            Money markPrice = getMarkPrice(symbol);

            // Tìm tất cả các vị thế theo symbol và hướng
            List<Position> positions = positionRepository.findAllBySymbolAndDirection(symbol, direction);

            // Tính toán lợi nhuận cho mỗi vị thế và sắp xếp theo lợi nhuận giảm dần
            return positions.stream()
                    .filter(position -> position.getVolume().compareTo(BigDecimal.ZERO) > 0)
                    .sorted((p1, p2) -> {
                        Money profit1 = calculateUnrealizedPnl(p1, markPrice);
                        Money profit2 = calculateUnrealizedPnl(p2, markPrice);
                        return profit2.getValue().compareTo(profit1.getValue());
                    })
                    .limit(limit)
                    .collect(java.util.stream.Collectors.toList());
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm các vị thế có lợi nhuận cao nhất, symbol = {}, direction = {}", symbol.getValue(), direction, e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi tìm các vị thế có lợi nhuận cao nhất: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm các vị thế có lợi nhuận cao nhất, symbol = {}, direction = {}", symbol.getValue(), direction, e);
            throw new BusinessException("Lỗi khi tìm các vị thế có lợi nhuận cao nhất: " + e.getMessage(), e);
        }
    }

    /**
     * Lấy giá đánh dấu hiện tại cho một symbol
     * @param symbol Symbol của hợp đồng
     * @return Giá đánh dấu hiện tại
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Money getMarkPrice(Symbol symbol) {
        try {
            log.debug("Lấy giá đánh dấu hiện tại cho symbol, symbol = {}", symbol.getValue());

            if (symbol == null) {
                throw new ValidationException("Symbol không được để trống");
            }

            Money markPrice = priceManagementService.getCurrentMarkPrice(symbol);

            log.debug("Đã lấy giá đánh dấu hiện tại thành công, symbol = {}, markPrice = {}", symbol.getValue(), markPrice.getValue());

            return markPrice;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lấy giá đánh dấu hiện tại, symbol = {}", symbol.getValue(), e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi lấy giá đánh dấu hiện tại: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lấy giá đánh dấu hiện tại, symbol = {}", symbol.getValue(), e);
            throw new BusinessException("Lỗi khi lấy giá đánh dấu hiện tại: " + e.getMessage(), e);
        }
    }

    /**
     * Tính toán lợi nhuận không thực hiện cho vị thế
     * @param position Vị thế
     * @param markPrice Giá đánh dấu
     * @return Lợi nhuận không thực hiện
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Money calculateUnrealizedPnl(Position position, Money markPrice) {
        try {
            log.debug("Tính toán lợi nhuận không thực hiện cho vị thế, positionId = {}, markPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    markPrice != null ? markPrice.getValue() : "null");

            if (position == null) {
                throw new ValidationException("Position không được để trống");
            }

            if (markPrice == null) {
                throw new ValidationException("MarkPrice không được để trống");
            }

            Money unrealizedPnl = position.calculateUnrealizedProfit(markPrice);

            log.debug("Đã tính toán lợi nhuận không thực hiện thành công, positionId = {}, unrealizedPnl = {}",
                    position.getId() != null ? position.getId().getValue() : "null", unrealizedPnl.getValue());

            return unrealizedPnl;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tính toán lợi nhuận không thực hiện, positionId = {}, markPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    markPrice != null ? markPrice.getValue() : "null", e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi tính toán lợi nhuận không thực hiện: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            log.error("Lỗi tính toán khi tính toán lợi nhuận không thực hiện, positionId = {}, markPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    markPrice != null ? markPrice.getValue() : "null", e);
            return Money.ZERO;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tính toán lợi nhuận không thực hiện, positionId = {}, markPrice = {}",
                    position.getId() != null ? position.getId().getValue() : "null",
                    markPrice != null ? markPrice.getValue() : "null", e);
            throw new BusinessException("Lỗi khi tính toán lợi nhuận không thực hiện: " + e.getMessage(), e);
        }
    }

    /**
     * Lưu vị thế
     * @param position Vị thế cần lưu
     * @return Vị thế đã được lưu
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Position save(Position position) {
        try {
            log.debug("Lưu vị thế, positionId = {}", position.getId() != null ? position.getId().getValue() : "null");

            if (position == null) {
                throw new ValidationException("Position không được để trống");
            }

            Position savedPosition = positionRepository.save(position);

            log.debug("Đã lưu vị thế thành công, positionId = {}", savedPosition.getId().getValue());

            return savedPosition;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi lưu vị thế, positionId = {}", position.getId() != null ? position.getId().getValue() : "null", e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi lưu vị thế: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi lưu vị thế, positionId = {}", position.getId() != null ? position.getId().getValue() : "null", e);
            throw new BusinessException("Lỗi khi lưu vị thế: " + e.getMessage(), e);
        }
    }
}
