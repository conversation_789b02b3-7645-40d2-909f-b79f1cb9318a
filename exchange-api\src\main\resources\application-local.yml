spring:
  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: http://************:8082/realms/cex-lotus
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ${DATASOURCE_URL:*****************************************************************}
    username: ${DATASOURCE_USERNAME:lotus_root}
    password: ${DATASOURCE_PASSWORD:Lotus12345!}

  data:
    mongodb:
      uri: ${SPRING_MONGODB_URI:****************************************************}
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:2m0881Xc30Wh}
      connect-timeout: ${REDIS_TIMEOUT:30000}
      jedis:
        pool:
          min-idle: 20
          max-idle: 100
          max-wait: 60000
          max-active: 300
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:9092}
    consumer:
      group-id: ${KAFKA_CONSUMER_GROUP_ID:default-group}
    properties:
      #      security.protocol: SASL_PLAINTEXT
      sasl.mechanism: PLAIN
      #      sasl.jaas.config: >-
      #        org.apache.kafka.common.security.plain.PlainLoginModule required username=""
      #        password="2m0881Xc30Wh";

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30500}

keycloak:
  auth-server-url: http://************:8082
  realm: cex-lotus
  resource: cex-admin
  credentials:
    secret: 6e4Le4p6rMNw6LZiUDEMHXUbCpq6YAAK
cex-security:
  permit-all-endpoints: /**
  resource-server-enabled: true
sms:
  driver: ${SMS_DRIVER:twilio}
  gateway: ${SMS_GATEWAY:}
  username: ${SMS_USERNAME:**********}
  password: ${SMS_PASSWORD:4901B0E56BD8CB679D8C822F8}
  sign: ${SMS_SIGN:Icetea-Software}
  internationalGateway: ${SMS_INTERNATIONAL_GATEWAY:}
  internationalUsername: ${SMS_INTERNATIONAL_USERNAME:}
  internationalPassword: ${SMS_INTERNATIONAL_PASSWORD:}

twilio:
  account_sid: **********************************
  auth_token: 9d6fe9aa6af7e56b72397b219b817e5f
  trial_number: +***********

