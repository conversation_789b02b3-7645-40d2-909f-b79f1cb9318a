package com.icetea.lotus.bot;

import com.icetea.lotus.controller.OrderController;
import com.icetea.lotus.entity.ExchangeOrderDirection;
import com.icetea.lotus.entity.ExchangeOrderType;
import com.icetea.lotus.entity.Member;
import com.icetea.lotus.entity.transform.AuthMember;
import com.icetea.lotus.service.MemberService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.PeriodicTrigger;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ScheduledFuture;


@Component
@Slf4j
@RequiredArgsConstructor
public class TradeBot {

    private final OrderController orderController;
    private final MemberService memberService;
    private final ExternalExchangeService externalExchangeService;

    @Autowired
    private TaskScheduler taskScheduler;

    private ScheduledFuture<?> scheduledTask;
    private long fixedRate = 1; // Default rate in seconds
    private String symbol; // If null, will use a random symbol
    private boolean isRunning = false;
    private int maxExecutions = -1; // Default: run indefinitely (-1 means no limit)
    private int executionCount = 0; // Counter for number of executions

    public static BigDecimal getRandomPrice(BigDecimal minPrice, BigDecimal maxPrice) {
        Random random = new Random();

        // Generate random value between 0 and 1
        BigDecimal randomFactor = BigDecimal.valueOf(random.nextDouble());

        // Scale it to the range between minPrice and maxPrice
        BigDecimal randomPrice = minPrice.add(randomFactor.multiply(maxPrice.subtract(minPrice)));

        // Set scale to 2 decimal places (or as needed) and round half up
        return randomPrice.setScale(0, RoundingMode.HALF_UP);
    }

    private static String pickRandomTradingPair() {
        // Prioritize BTC and ETH trading pairs
        List<String> prioritySymbols = Arrays.asList(
                "BTC/USDT",
                "ETH/USDT",
                "ETH/BTC"
        );

        List<String> otherSymbols = Arrays.asList(
                "BSV/USDT",
                "XRP/BTC",
                "WBTC/ETH",
                "DOGE/ETH",
                "HT/USDT",
                "EOS/USDT",
                "LTC/USDT",
                "PI/USDT",
                "BCH/USDT",
                "BNB/USDT",
                "ETC/USDT",
                "DOGE/USDT",
                "EOS/ETH",
                "DOGE/BTC",
                "XRP/USDT"
        );

        Random random = new Random();
        int priorityIndex = random.nextInt(prioritySymbols.size());
        return prioritySymbols.get(priorityIndex);

    }

    @PostConstruct
    public void init() {
        // Initialize the task scheduler if not provided by Spring
        if (taskScheduler == null) {
            ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
            scheduler.setPoolSize(1);
            scheduler.setThreadNamePrefix("trade-bot-scheduler-");
            scheduler.initialize();
            this.taskScheduler = scheduler;
        }
    }

    public void autoTrade() {
        // Check if we've reached the maximum number of executions before doing anything
        if (maxExecutions > 0 && executionCount >= maxExecutions) {
            log.info("Reached maximum number of executions ({}). Not executing any more trades but task remains running.", maxExecutions);
            // Don't stop the task, just skip execution
            return;
        }

        // Increment execution count
        executionCount++;

        Member member = memberService.findOne(600912L);
        AuthMember authMember = AuthMember.toAuthMember(member);

        String tradingSymbol = symbol != null ? symbol : pickRandomTradingPair();

        log.info("----------------------------------- BUY: {}", tradingSymbol);

        // Fetch detailed order book data from the preferred exchange
        OrderBook orderBook = externalExchangeService.fetchOrderBookDetailed(tradingSymbol, 10);

        if (orderBook.getBids().isEmpty() && orderBook.getAsks().isEmpty()) {
            log.warn("Could not fetch order book for {}. Using fallback strategy.", tradingSymbol);

            // Use LIMIT_PRICE as the default order type for safety
            ExchangeOrderType orderType = ExchangeOrderType.LIMIT_PRICE;

            // Since we can't determine direction from market data, use a more balanced approach
            // Instead of random selection, alternate between BUY and SELL based on timestamp
            ExchangeOrderDirection direction;
            if (System.currentTimeMillis() % 2 == 0) {
                direction = ExchangeOrderDirection.BUY;
                log.info("Using fallback strategy: BUY order with LIMIT_PRICE");
            } else {
                direction = ExchangeOrderDirection.SELL;
                log.info("Using fallback strategy: SELL order with LIMIT_PRICE");
            }

            this.autoBuySell(tradingSymbol, direction);

            orderController.addOrder(authMember, direction, tradingSymbol,
                    getRandomPrice(BigDecimal.valueOf(99000), BigDecimal.valueOf(100000)),
                    getRandomPrice(BigDecimal.valueOf(1), BigDecimal.valueOf(10)),
                    orderType);
        } else {
            // Use real market data for orders
            BigDecimal bestBidPrice = orderBook.getBestBidPrice();
            BigDecimal bestAskPrice = orderBook.getBestAskPrice();
            BigDecimal bestBidQuantity = orderBook.getBestBidQuantity();
            BigDecimal bestAskQuantity = orderBook.getBestAskQuantity();

            // Calculate market metrics
            BigDecimal midPrice = orderBook.getMidPrice();
            BigDecimal spread = orderBook.getSpread();
            BigDecimal spreadPercentage = orderBook.getSpreadPercentage();

            log.info("Order book for {}: Bid: {} ({}), Ask: {} ({}), Mid: {}, Spread: {} ({}%)",
                    tradingSymbol, bestBidPrice, bestBidQuantity, bestAskPrice, bestAskQuantity,
                    midPrice, spread, spreadPercentage);

            // Determine order type based on market conditions
            ExchangeOrderType orderType = orderBook.determineOrderType();
            log.info("Determined order type for {}: {}", tradingSymbol, orderType);

            // Determine direction based on order book (simplified logic)
            ExchangeOrderDirection direction = orderBook.determineOrderDirection();
            log.info("Determined order direction for {}: {}", tradingSymbol, direction);

            // Process all bids and asks from the order book and add them as orders
            log.info("Processing all bids and asks from the order book for {}", tradingSymbol);

            // Process all bids (buy orders) from the order book
            if (!orderBook.getBids().isEmpty()) {
                log.info("Processing {} bid entries from the order book", orderBook.getBids().size());
                Member buyMember = memberService.findOne(600911L);
                AuthMember buyAuthMember = AuthMember.toAuthMember(buyMember);

                for (OrderBook.OrderBookEntry bid : orderBook.getBids()) {
                    // Add each bid as a BUY order
                    log.info("Adding BUY order from order book: price={}, quantity={}", bid.getPrice(), bid.getQuantity());
                    orderController.addOrder(
                            buyAuthMember,
                            ExchangeOrderDirection.BUY,
                            tradingSymbol,
                            bid.getPrice(),
                            bid.getQuantity(),
                            ExchangeOrderType.LIMIT_PRICE
                    );
                }
            }

            // Process all asks (sell orders) from the order book
            if (!orderBook.getAsks().isEmpty()) {
                log.info("Processing {} ask entries from the order book", orderBook.getAsks().size());
                Member sellMember = memberService.findOne(600912L);
                AuthMember sellAuthMember = AuthMember.toAuthMember(sellMember);

                for (OrderBook.OrderBookEntry ask : orderBook.getAsks()) {
                    // Add each ask as a SELL order
                    log.info("Adding SELL order from order book: price={}, quantity={}", ask.getPrice(), ask.getQuantity());
                    orderController.addOrder(
                            sellAuthMember,
                            ExchangeOrderDirection.SELL,
                            tradingSymbol,
                            ask.getPrice(),
                            ask.getQuantity(),
                            ExchangeOrderType.LIMIT_PRICE
                    );
                }
            }
        }
    }

    public void autoBuySell(String randomSymbol, ExchangeOrderDirection randomDirection) {
        Member member = memberService.findOne(600911L);
        AuthMember authMember = AuthMember.toAuthMember(member);
        log.info("----------------------------------- SELL: {}", randomSymbol);

        // Always use SELL direction regardless of the provided direction
        // This ensures that all trading pairs, including ETH/USDT, have both BUY and SELL orders
        orderController.addOrder(authMember, randomDirection, randomSymbol,
                getRandomPrice(BigDecimal.valueOf(99000), BigDecimal.valueOf(100000)),
                getRandomPrice(BigDecimal.valueOf(1), BigDecimal.valueOf(10)),
                ExchangeOrderType.LIMIT_PRICE);
    }

    /**
     * Place a sell order using real market data from the order book
     *
     * @param symbol    The trading pair symbol
     * @param orderBook The order book data from external exchange
     */
    public void autoBuySell(String symbol, OrderBook orderBook) {
        Member member = memberService.findOne(600911L);
        AuthMember authMember = AuthMember.toAuthMember(member);
        log.info("----------------------------------- SELL: {}", symbol);

        // Use real market data for orders
        BigDecimal bestBidPrice = orderBook.getBestBidPrice();
        BigDecimal bestBidQuantity = orderBook.getBestBidQuantity();
        BigDecimal bestAskPrice = orderBook.getBestAskPrice();

        // Calculate market metrics
        BigDecimal midPrice = orderBook.getMidPrice();
        BigDecimal spreadPercentage = orderBook.getSpreadPercentage();

        // Enhanced selling strategy using multiple levels of the order book
        // Calculate volume-weighted average price (VWAP) for better price discovery
        BigDecimal totalAskVolume = BigDecimal.ZERO;
        BigDecimal weightedAskPrice = BigDecimal.ZERO;

        // Use up to 3 levels of asks for VWAP calculation (or fewer if not available)
        int askLevelsToUse = Math.min(3, orderBook.getAsks().size());
        for (int i = 0; i < askLevelsToUse; i++) {
            OrderBook.OrderBookEntry ask = orderBook.getAsks().get(i);
            totalAskVolume = totalAskVolume.add(ask.getQuantity());
            weightedAskPrice = weightedAskPrice.add(ask.getPrice().multiply(ask.getQuantity()));
        }

        // Calculate VWAP for asks
        BigDecimal askVWAP = totalAskVolume.compareTo(BigDecimal.ZERO) > 0
                ? weightedAskPrice.divide(totalAskVolume, 8, RoundingMode.HALF_UP)
                : bestAskPrice;

        // Place a SELL order based on market conditions
        BigDecimal sellPrice;
        BigDecimal sellAmount;

        // If spread is tight (< 0.2%), place order closer to bid
        if (spreadPercentage != null && spreadPercentage.compareTo(BigDecimal.valueOf(0.2)) < 0) {
            sellPrice = bestBidPrice.multiply(BigDecimal.valueOf(1.0005)).setScale(2, RoundingMode.HALF_UP);
            sellAmount = bestBidQuantity.multiply(BigDecimal.valueOf(0.7)).setScale(4, RoundingMode.HALF_UP);
            log.info("Tight spread detected ({}%). Placing aggressive SELL order.", spreadPercentage);
        } else {
            // For wider spreads, place order further from bid
            sellPrice = bestBidPrice.multiply(BigDecimal.valueOf(1.002)).setScale(2, RoundingMode.HALF_UP);
            sellAmount = bestBidQuantity.multiply(BigDecimal.valueOf(0.5)).setScale(4, RoundingMode.HALF_UP);
            log.info("Wide spread detected ({}%). Placing conservative SELL order.", spreadPercentage);
        }

        // Determine order type based on market conditions
        ExchangeOrderType orderType = orderBook.determineOrderType();
        log.info("Determined order type for {}: {}", symbol, orderType);

        log.info("Placing SELL order for {} at price {} and amount {} with order type {}",
                symbol, sellPrice, sellAmount, orderType);
        orderController.addOrder(authMember, ExchangeOrderDirection.SELL, symbol,
                sellPrice,
                sellAmount,
                orderType);

        // Process all bids and asks from the order book and add them as orders
        log.info("Processing all bids and asks from the order book for {}", symbol);

        // Process all bids (buy orders) from the order book
        if (!orderBook.getBids().isEmpty()) {
            log.info("Processing {} bid entries from the order book", orderBook.getBids().size());
            Member buyMember = memberService.findOne(600911L);
            AuthMember buyAuthMember = AuthMember.toAuthMember(buyMember);

            for (OrderBook.OrderBookEntry bid : orderBook.getBids()) {
                // Add each bid as a BUY order
                log.info("Adding BUY order from order book: price={}, quantity={}", bid.getPrice(), bid.getQuantity());
                orderController.addOrder(
                        buyAuthMember,
                        ExchangeOrderDirection.BUY,
                        symbol,
                        bid.getPrice(),
                        bid.getQuantity(),
                        ExchangeOrderType.LIMIT_PRICE
                );
            }
        }

        // Process all asks (sell orders) from the order book
        if (!orderBook.getAsks().isEmpty()) {
            log.info("Processing {} ask entries from the order book", orderBook.getAsks().size());
            Member sellMember = memberService.findOne(600912L);
            AuthMember sellAuthMember = AuthMember.toAuthMember(sellMember);

            for (OrderBook.OrderBookEntry ask : orderBook.getAsks()) {
                // Add each ask as a SELL order
                log.info("Adding SELL order from order book: price={}, quantity={}", ask.getPrice(), ask.getQuantity());
                orderController.addOrder(
                        sellAuthMember,
                        ExchangeOrderDirection.SELL,
                        symbol,
                        ask.getPrice(),
                        ask.getQuantity(),
                        ExchangeOrderType.LIMIT_PRICE
                );
            }
        }
    }

    /**
     * Legacy method for backward compatibility
     *
     * @param symbol    The trading pair symbol
     * @param orderBook The order book data in legacy Map format
     */
    public void autoBuySell(String symbol, Map<String, BigDecimal> orderBook) {
        // Convert legacy Map to OrderBook for backward compatibility
        OrderBook newOrderBook = new OrderBook();
        newOrderBook.setSymbol(symbol);

        if (orderBook.containsKey("bestBidPrice") && orderBook.containsKey("bestBidVolume")) {
            newOrderBook.getBids().add(new OrderBook.OrderBookEntry(
                    orderBook.get("bestBidPrice"),
                    orderBook.get("bestBidVolume")
            ));
        }

        if (orderBook.containsKey("bestAskPrice") && orderBook.containsKey("bestAskVolume")) {
            newOrderBook.getAsks().add(new OrderBook.OrderBookEntry(
                    orderBook.get("bestAskPrice"),
                    orderBook.get("bestAskVolume")
            ));
        }

        // Call the new method with the converted OrderBook
        autoBuySell(symbol, newOrderBook);
    }

    /**
     * Start the auto buy task with the specified parameters
     *
     * @param fixedRate     the fixed rate in milliseconds
     * @param symbol        the trading symbol (optional, if null will use random symbol)
     * @param maxExecutions the maximum number of times to run the task (-1 means no limit)
     * @return true if the task was started, false if it was already running
     */
    public boolean startAutoBuy(long fixedRate, String symbol, int maxExecutions) {
        if (isRunning) {
            log.info("Auto buy task is already running. Stop it first before starting with new parameters.");
            return false;
        }

        this.fixedRate = fixedRate;
        this.symbol = symbol;
        this.maxExecutions = maxExecutions;
        this.executionCount = 0; // Reset execution count

        // Create a periodic trigger with the specified fixed rate
        PeriodicTrigger trigger = new PeriodicTrigger(Duration.of(fixedRate, ChronoUnit.SECONDS));
        trigger.setFixedRate(true);

        // Schedule the task
        this.scheduledTask = taskScheduler.schedule(this::autoTrade, trigger);
        this.isRunning = true;

        log.info("Started auto buy task with fixedRate={} ms, symbol={}, maxExecutions={}",
                fixedRate, symbol != null ? symbol : "random", maxExecutions > 0 ? maxExecutions : "unlimited");
        return true;
    }

    /**
     * Stop the auto buy task
     *
     * @return true if the task was stopped, false if it wasn't running
     */
    public boolean stopAutoBuy() {
        if (!isRunning || scheduledTask == null) {
            log.info("Auto buy task is not running.");
            return false;
        }

        // Cancel the scheduled task
        boolean cancelled = scheduledTask.cancel(false);
        this.isRunning = false;
        this.scheduledTask = null;

        log.info("Stopped auto buy task. Cancelled: {}", cancelled);
        return true;
    }

    /**
     * Check if the auto buy task is running
     *
     * @return true if the task is running, false otherwise
     */
    public boolean isAutoBuyRunning() {
        return isRunning;
    }

    /**
     * Get the current configuration of the auto buy task
     *
     * @return a map containing the current configuration
     */
    public java.util.Map<String, Object> getAutoBuyConfig() {
        java.util.Map<String, Object> config = new java.util.HashMap<>();
        // Check if the task is actually running (has a valid scheduledTask)
        boolean actuallyRunning = isRunning && scheduledTask != null && !scheduledTask.isCancelled();
        config.put("isRunning", actuallyRunning);
        config.put("fixedRate", fixedRate);
        config.put("symbol", symbol);
        config.put("maxExecutions", maxExecutions);
        config.put("executionCount", executionCount);
        config.put("remainingExecutions", maxExecutions > 0 ? Math.max(0, maxExecutions - executionCount) : -1);
        return config;
    }
}
