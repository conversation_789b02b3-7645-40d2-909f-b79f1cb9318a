package com.icetea.lotus.walletrpc.util;


import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MessageResult {
    private int code;
    private String message;
    private Object data;

    public MessageResult(int code, String msg) {
        this.code = code;
        this.message = msg;
    }

    public MessageResult(int code, String msg, Object object) {
        this.code = code;
        this.message = msg;
        this.data = object;
    }

    public MessageResult() {
        // TODO Auto-generated constructor stub
    }

    public static MessageResult success() {
        return new MessageResult(0, "SUCCESS");
    }

    public static MessageResult success(String msg) {
        return new MessageResult(0, msg);
    }

    public static MessageResult error(int code, String msg) {
        return new MessageResult(code, msg);
    }

    public String toString() {
        return JSONObject.toJSONString(this);
        //return "{\"code\":"+code+",\"message\":\""+message+"\"}";
    }
}