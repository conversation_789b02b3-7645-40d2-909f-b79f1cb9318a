spring:

  zipkin:
    base-url: http://************:9411
    enabled: true
    sender:
      type: web

  sleuth:
    sampler:
      percentage: 1.0
      probability: 1.0
    enabled: true

  datasource:
    driver-class-name: org.postgresql.Driver
    url: ${DATASOURCE_URL:*****************************************************************}
    username: ${DATASOURCE_USERNAME:lotus_root}
    password: ${DATASOURCE_PASSWORD:Lotus12345!}
  jpa:
    properties:
      hibernate:
        default_schema: bizzan

  data:
    mongodb:
      uri: ${SPRING_MONGODB_URI:*****************************************************
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:2m0881Xc30Wh}

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:9092}
    consumer:
      group.id: ${KAFKA_CONSUMER_GROUP_ID:exchange-local}
    properties:
      #      security.protocol: SASL_PLAINTEXT
      sasl.mechanism: PLAIN
      #      sasl.jaas.config: >-
      #        org.apache.kafka.common.security.plain.PlainLoginModule required username=""
      #        password="2m0881Xc30Wh";

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:8500}
      #        instance-id: ${HOSTNAME}-${SPRING_APPLICATION_NAME:exchange}-${SERVER_PORT}
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://************:8082/realms/cex-lotus
keycloak:
  auth-server-url: http://************:8082
  realm: cex-lotus
  resource: cex-admin
  credentials:
    secret: 6e4Le4p6rMNw6LZiUDEMHXUbCpq6YAAK
cex-security:
  permit-all-endpoints:
    - "/**"
topic-kafka:
  exchange:
    order: ${EXCHANGE_ORDER:exchange-order}
    order-cancel-success: ${EXCHANGE_ORDER_CANCEL_SUCCESS:exchange-order-cancel-success}
    order-completed: ${EXCHANGE_ORDER_COMPLETED:exchange-order-completed}
    order-cancel: ${EXCHANGE_ORDER_CANCEL:exchange-order-cancel}
    trade: ${EXCHANGE_TRADE:exchange-trade}
    trade-plate: ${EXCHANGE_TRADE_PLATE:exchange-trade-plate}