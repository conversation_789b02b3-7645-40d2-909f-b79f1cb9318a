spring:

  datasource:
    url: ${DATASOURCE_URL:*****************************************************************}
    username: ${DATASOURCE_USERNAME:lotus_root}
    password: ${DATASOURCE_PASSWORD:Lotus12345!}

  jpa:
    properties:
      hibernate:
        default_schema: ${DEFAULT_SCHEMA:bizzan}

  data:
    mongodb:
      uri: ${SPRING_MONGODB_URI:****************************************************}
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:6379}

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30500}
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        health-check-path: "${server.servlet.context-path}/actuator/health"
        health-check-interval: 10s
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${random.value}
    stream:
      kafka:
        binder:
          headers:
            - spanId
            - spanSampled
            - spanProcessId
            - spanParentSpanId
            - spanTraceId
            - spanName
            - messageSent
