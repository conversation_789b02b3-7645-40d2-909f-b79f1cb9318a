package com.icetea.lotus.core.domain.service.impl;

import com.icetea.lotus.core.domain.constants.SystemConstants;
import com.icetea.lotus.core.domain.entity.ADLRecord;
import com.icetea.lotus.core.domain.entity.Contract;
import com.icetea.lotus.core.domain.entity.FinancialRecord;
import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.PositionDirection;
import com.icetea.lotus.core.domain.entity.PositionStatus;
import com.icetea.lotus.core.domain.exception.ValidationException;
import com.icetea.lotus.core.domain.repository.ADLRecordRepository;
import com.icetea.lotus.core.domain.repository.FinancialRecordRepository;
import com.icetea.lotus.core.domain.service.AccountService;
import com.icetea.lotus.core.domain.service.LiquidationService;
import com.icetea.lotus.core.domain.service.PositionService;
import com.icetea.lotus.core.domain.valueobject.AccountOperationType;
import com.icetea.lotus.core.domain.valueobject.ADLRecordId;
import com.icetea.lotus.core.domain.valueobject.FinancialRecordType;
import com.icetea.lotus.core.domain.valueobject.LiquidationType;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.infrastructure.util.SnowflakeIdGenerator;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.infrastructure.exception.BusinessException;
import com.icetea.lotus.infrastructure.messaging.producer.LiquidationEventProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Triển khai LiquidationService
 */
@Slf4j
@Service
@Transactional
public class LiquidationServiceImpl implements LiquidationService {

    private final PositionService positionService;
    private final LiquidationEventProducer liquidationEventProducer;
    private final AccountService accountService;
    private final FinancialRecordRepository financialRecordRepository;
    private final ADLRecordRepository adlRecordRepository;
    private final SnowflakeIdGenerator snowflakeIdGenerator;

    @Autowired
    public LiquidationServiceImpl(PositionService positionService,
                                 LiquidationEventProducer liquidationEventProducer,
                                 AccountService accountService,
                                 FinancialRecordRepository financialRecordRepository,
                                 ADLRecordRepository adlRecordRepository,
                                 SnowflakeIdGenerator snowflakeIdGenerator) {
        this.positionService = positionService;
        this.liquidationEventProducer = liquidationEventProducer;
        this.accountService = accountService;
        this.financialRecordRepository = financialRecordRepository;
        this.adlRecordRepository = adlRecordRepository;
        this.snowflakeIdGenerator = snowflakeIdGenerator;
    }

    /**
     * Kiểm tra xem vị thế có cần thanh lý không với xử lý ngoại lệ và thử lại
     * @param position Vị thế
     * @param currentPrice Giá hiện tại
     * @param contract Hợp đồng
     * @return true nếu vị thế cần thanh lý
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public boolean needsLiquidation(Position position, Money currentPrice, Contract contract) {
        try {
            logLiquidationCheck(position, currentPrice);

            // Kiểm tra đầu vào
            validateLiquidationCheckInput(position, currentPrice, contract);

            // Kiểm tra thanh lý
            return checkPositionLiquidation(position, currentPrice, contract);
        } catch (DataAccessException | TransactionException e) {
            logLiquidationCheckError(position, e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi kiểm tra thanh lý vị thế: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            logLiquidationCheckError(position, e);
            throw new BusinessException("Lỗi khi kiểm tra thanh lý vị thế: " + e.getMessage(), e);
        }
    }

    /**
     * Ghi log kiểm tra thanh lý
     * @param position Vị thế
     * @param currentPrice Giá hiện tại
     */
    private void logLiquidationCheck(Position position, Money currentPrice) {
        log.debug("Kiểm tra thanh lý vị thế, positionId = {}, symbol = {}, currentPrice = {}",
                position != null && position.getId() != null ? position.getId().getValue() : "null",
                position != null && position.getSymbol() != null ? position.getSymbol().getValue() : "null",
                currentPrice != null ? currentPrice.getValue() : "null");
    }

    /**
     * Ghi log lỗi kiểm tra thanh lý
     * @param position Vị thế
     * @param e Ngoại lệ
     */
    private void logLiquidationCheckError(Position position, Exception e) {
        log.error("Lỗi khi kiểm tra thanh lý vị thế, positionId = {}, symbol = {}",
                position != null && position.getId() != null ? position.getId().getValue() : "null",
                position != null && position.getSymbol() != null ? position.getSymbol().getValue() : "null", e);
    }

    /**
     * Kiểm tra đầu vào cho phương thức kiểm tra thanh lý
     * @param position Vị thế
     * @param currentPrice Giá hiện tại
     * @param contract Hợp đồng
     */
    private void validateLiquidationCheckInput(Position position, Money currentPrice, Contract contract) {
        if (position == null) {
            throw new ValidationException("Position không được để trống");
        }

        if (currentPrice == null) {
            throw new ValidationException("CurrentPrice không được để trống");
        }

        if (contract == null) {
            throw new ValidationException("Contract không được để trống");
        }
    }

    /**
     * Kiểm tra thanh lý vị thế
     * @param position Vị thế
     * @param currentPrice Giá hiện tại
     * @param contract Hợp đồng
     * @return true nếu vị thế cần thanh lý
     */
    private boolean checkPositionLiquidation(Position position, Money currentPrice, Contract contract) {
        boolean result = positionService.needsLiquidation(position, currentPrice, contract);

        if (result) {
            log.info("Vị thế cần thanh lý, positionId = {}, symbol = {}, currentPrice = {}",
                    position.getId().getValue(), position.getSymbol().getValue(), currentPrice.getValue());
        } else {
            log.debug("Vị thế không cần thanh lý, positionId = {}, symbol = {}, currentPrice = {}",
                    position.getId().getValue(), position.getSymbol().getValue(), currentPrice.getValue());
        }

        return result;
    }

    /**
     * Phục hồi khi kiểm tra thanh lý vị thế thất bại
     * @param e Ngoại lệ
     * @param position Vị thế
     * @param currentPrice Giá hiện tại
     * @param contract Hợp đồng
     * @return false
     */
    @Recover
    public boolean recoverNeedsLiquidation(Exception e, Position position, Money currentPrice, Contract contract) {
        log.error("Đã thử lại kiểm tra thanh lý vị thế 3 lần nhưng thất bại, positionId = {}, symbol = {}",
                position != null && position.getId() != null ? position.getId().getValue() : "null",
                position != null && position.getSymbol() != null ? position.getSymbol().getValue() : "null", e);
        return false;
    }

    /**
     * Tính toán giá thanh lý cho vị thế
     * @param position Vị thế
     * @param contract Hợp đồng
     * @return Giá thanh lý
     */
    @Override
    public Money calculateLiquidationPrice(Position position, Contract contract) {
        return positionService.calculateLiquidationPrice(position, contract);
    }

    /**
     * Tìm các vị thế cần thanh lý với xử lý ngoại lệ và thử lại
     * @param positions Danh sách các vị thế
     * @param currentPrices Giá hiện tại cho mỗi symbol
     * @param contracts Hợp đồng cho mỗi symbol
     * @return Danh sách các vị thế cần thanh lý
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public List<Position> findPositionsToLiquidate(List<Position> positions, Map<Symbol, Money> currentPrices, Map<Symbol, Contract> contracts) {
        try {
            log.debug("Tìm các vị thế cần thanh lý, số lượng vị thế = {}",
                    positions != null ? positions.size() : 0);

            // Kiểm tra đầu vào
            validateFindPositionsInput(positions, currentPrices, contracts);

            // Tìm các vị thế cần thanh lý
            List<Position> positionsToLiquidate = findPositionsNeedingLiquidation(positions, currentPrices, contracts);

            log.debug("Đã tìm thấy {} vị thế cần thanh lý", positionsToLiquidate.size());

            return positionsToLiquidate;
        } catch (DataAccessException | TransactionException e) {
            log.error("Lỗi khi tìm các vị thế cần thanh lý", e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi tìm các vị thế cần thanh lý: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Lỗi không xác định khi tìm các vị thế cần thanh lý", e);
            throw new BusinessException("Lỗi khi tìm các vị thế cần thanh lý: " + e.getMessage(), e);
        }
    }

    /**
     * Kiểm tra đầu vào cho phương thức tìm các vị thế cần thanh lý
     * @param positions Danh sách các vị thế
     * @param currentPrices Giá hiện tại cho mỗi symbol
     * @param contracts Hợp đồng cho mỗi symbol
     */
    private void validateFindPositionsInput(List<Position> positions, Map<Symbol, Money> currentPrices, Map<Symbol, Contract> contracts) {
        if (positions == null || positions.isEmpty()) {
            log.debug("Danh sách vị thế trống");
            return;
        }

        if (currentPrices == null || currentPrices.isEmpty()) {
            throw new ValidationException("CurrentPrices không được để trống");
        }

        if (contracts == null || contracts.isEmpty()) {
            throw new ValidationException("Contracts không được để trống");
        }
    }

    /**
     * Tìm các vị thế cần thanh lý
     * @param positions Danh sách các vị thế
     * @param currentPrices Giá hiện tại cho mỗi symbol
     * @param contracts Hợp đồng cho mỗi symbol
     * @return Danh sách các vị thế cần thanh lý
     */
    private List<Position> findPositionsNeedingLiquidation(List<Position> positions, Map<Symbol, Money> currentPrices, Map<Symbol, Contract> contracts) {
        if (positions == null || positions.isEmpty()) {
            return Collections.emptyList();
        }

        List<Position> positionsToLiquidate = new ArrayList<>();

        for (Position position : positions) {
            checkPositionForLiquidation(position, currentPrices, contracts, positionsToLiquidate);
        }

        return positionsToLiquidate;
    }

    /**
     * Kiểm tra một vị thế có cần thanh lý không
     * @param position Vị thế cần kiểm tra
     * @param currentPrices Giá hiện tại cho mỗi symbol
     * @param contracts Hợp đồng cho mỗi symbol
     * @param positionsToLiquidate Danh sách các vị thế cần thanh lý
     */
    private void checkPositionForLiquidation(Position position, Map<Symbol, Money> currentPrices, Map<Symbol, Contract> contracts, List<Position> positionsToLiquidate) {
        try {
            if (position == null || position.getSymbol() == null) {
                log.warn("Vị thế hoặc symbol không hợp lệ, bỏ qua");
                return;
            }

            Money currentPrice = currentPrices.get(position.getSymbol());
            Contract contract = contracts.get(position.getSymbol());

            if (currentPrice != null && contract != null) {
                if (needsLiquidation(position, currentPrice, contract)) {
                    positionsToLiquidate.add(position);
                    log.info("Đã thêm vị thế cần thanh lý, positionId = {}, symbol = {}",
                            position.getId().getValue(), position.getSymbol().getValue());
                }
            } else {
                log.warn("Không tìm thấy giá hiện tại hoặc hợp đồng cho symbol {}, bỏ qua",
                        position.getSymbol().getValue());
            }
        } catch (Exception e) {
            log.error("Lỗi khi kiểm tra vị thế, positionId = {}, symbol = {}",
                    position != null && position.getId() != null ? position.getId().getValue() : "null",
                    position != null && position.getSymbol() != null ? position.getSymbol().getValue() : "null", e);
            // Tiếp tục với vị thế tiếp theo
        }
    }

    /**
     * Phục hồi khi tìm các vị thế cần thanh lý thất bại
     * @param e Ngoại lệ
     * @param positions Danh sách các vị thế
     * @param currentPrices Giá hiện tại cho mỗi symbol
     * @param contracts Hợp đồng cho mỗi symbol
     * @return Danh sách trống
     */
    @Recover
    public List<Position> recoverFindPositionsToLiquidate(Exception e, List<Position> positions, Map<Symbol, Money> currentPrices, Map<Symbol, Contract> contracts) {
        log.error("Đã thử lại tìm các vị thế cần thanh lý 3 lần nhưng thất bại", e);
        return Collections.emptyList();
    }

    /**
     * Thanh lý vị thế với xử lý ngoại lệ và thử lại
     * @param position Vị thế cần thanh lý
     * @param liquidationPrice Giá thanh lý
     * @param contract Hợp đồng
     * @return Vị thế đã được thanh lý
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Position liquidatePosition(Position position, Money liquidationPrice, Contract contract) {
        try {
            logLiquidationStart(position, liquidationPrice);

            // Kiểm tra đầu vào và trạng thái vị thế
            if (!isPositionValidForLiquidation(position, liquidationPrice, contract)) {
                return null;
            }

            // Thực hiện thanh lý vị thế
            return performLiquidation(position, liquidationPrice, contract);
        } catch (DataAccessException | TransactionException e) {
            logLiquidationError(position, e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi thanh lý vị thế: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            logLiquidationError(position, e);
            throw new BusinessException("Lỗi khi thanh lý vị thế: " + e.getMessage(), e);
        }
    }

    /**
     * Ghi log bắt đầu thanh lý vị thế
     * @param position Vị thế cần thanh lý
     * @param liquidationPrice Giá thanh lý
     */
    private void logLiquidationStart(Position position, Money liquidationPrice) {
        log.debug("Thanh lý vị thế, positionId = {}, symbol = {}, liquidationPrice = {}",
                position != null && position.getId() != null ? position.getId().getValue() : "null",
                position != null && position.getSymbol() != null ? position.getSymbol().getValue() : "null",
                liquidationPrice != null ? liquidationPrice.getValue() : "null");
    }

    /**
     * Ghi log lỗi thanh lý vị thế
     * @param position Vị thế cần thanh lý
     * @param e Ngoại lệ
     */
    private void logLiquidationError(Position position, Exception e) {
        log.error("Lỗi khi thanh lý vị thế, positionId = {}, symbol = {}",
                position != null && position.getId() != null ? position.getId().getValue() : "null",
                position != null && position.getSymbol() != null ? position.getSymbol().getValue() : "null", e);
    }

    /**
     * Kiểm tra xem vị thế có hợp lệ để thanh lý không
     * @param position Vị thế cần thanh lý
     * @param liquidationPrice Giá thanh lý
     * @param contract Hợp đồng
     * @return true nếu vị thế hợp lệ để thanh lý
     */
    private boolean isPositionValidForLiquidation(Position position, Money liquidationPrice, Contract contract) {
        // Kiểm tra đầu vào
        validateLiquidationInput(position, liquidationPrice, contract);

        // Kiểm tra trạng thái vị thế
        if (position.getStatus() != null && position.getStatus() != PositionStatus.OPEN) {
            log.warn("Vị thế không ở trạng thái mở, không thể thanh lý, positionId = {}, status = {}, memberId = {}, symbol = {}",
                    position.getId().getValue(), position.getStatus(), position.getMemberId(), position.getSymbol().getValue());
            return false;
        }

        // Kiểm tra volume của vị thế
        if (position.getVolume() == null || position.getVolume().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("Vị thế có volume không hợp lệ, không thể thanh lý, positionId = {}, volume = {}, memberId = {}, symbol = {}",
                    position.getId().getValue(), position.getVolume(), position.getMemberId(), position.getSymbol().getValue());
            return false;
        }

        // Kiểm tra margin của vị thế
        if (position.getMargin() == null || position.getMargin().getValue().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("Vị thế có margin không hợp lệ, không thể thanh lý, positionId = {}, margin = {}, memberId = {}, symbol = {}",
                    position.getId().getValue(), position.getMargin() != null ? position.getMargin().getValue() : null,
                    position.getMemberId(), position.getSymbol().getValue());
            return false;
        }

        return true;
    }

    /**
     * Thực hiện thanh lý vị thế
     * @param position Vị thế cần thanh lý
     * @param liquidationPrice Giá thanh lý
     * @param contract Hợp đồng
     * @return Vị thế đã được thanh lý
     */
    private Position performLiquidation(Position position, Money liquidationPrice, Contract contract) {
        // Tính toán phí thanh lý và lợi nhuận
        Money liquidationFee = calculateLiquidationFeeSafely(position, liquidationPrice, contract);
        Money profit = calculateProfitSafely(position, liquidationPrice);

        // Tính toán số tiền ký quỹ còn lại
        Money remainingMargin = calculateRemainingMargin(position, profit, liquidationFee);

        // Xác định loại thanh lý và xử lý tài khoản
        LiquidationType liquidationType = handleRemainingMargin(position, remainingMargin);

        // Tạo vị thế đã được thanh lý với remainingMargin
        Position liquidatedPosition = createLiquidatedPosition(position, liquidationPrice, profit, remainingMargin);

        // Ghi nhận vào báo cáo tài chính
        createFinancialRecord(position, liquidationPrice, profit, liquidationFee, remainingMargin);

        // Ghi log thông tin thanh lý
        logLiquidationComplete(position, liquidationPrice, profit, liquidationFee, remainingMargin);

        // Gửi thông báo thanh lý vị thế
        sendLiquidationEvent(position, liquidationPrice, remainingMargin, liquidationType);

        return liquidatedPosition;
    }

    /**
     * Tính toán số tiền ký quỹ còn lại
     * @param position Vị thế
     * @param profit Lợi nhuận
     * @param liquidationFee Phí thanh lý
     * @return Số tiền ký quỹ còn lại
     */
    private Money calculateRemainingMargin(Position position, Money profit, Money liquidationFee) {
        // Validate input values
        BigDecimal margin = com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator
                .validateAndScale(position.getMargin().getValue(), "margin");
        BigDecimal profitValue = com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator
                .validateAndScale(profit.getValue(), "profit");
        BigDecimal feeValue = com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator
                .validateAndScale(liquidationFee.getValue(), "liquidationFee");

        // Calculate remaining margin: margin + profit - fee
        BigDecimal remainingMarginValue = margin.add(profitValue).subtract(feeValue);

        // Validate result
        remainingMarginValue = com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator
                .validateAndScale(remainingMarginValue, "remainingMargin");

        Money remainingMargin = Money.of(remainingMarginValue);

        log.debug("Số tiền ký quỹ còn lại sau khi thanh lý, positionId = {}, margin = {}, profit = {}, fee = {}, remainingMargin = {}",
                position.getId().getValue(), margin, profitValue, feeValue, remainingMargin.getValue());
        return remainingMargin;
    }

    /**
     * Ghi log hoàn thành thanh lý vị thế
     * @param position Vị thế
     * @param liquidationPrice Giá thanh lý
     * @param profit Lợi nhuận
     * @param liquidationFee Phí thanh lý
     * @param remainingMargin Số tiền ký quỹ còn lại
     */
    private void logLiquidationComplete(Position position, Money liquidationPrice, Money profit, Money liquidationFee, Money remainingMargin) {
        log.info("Đã thanh lý vị thế, positionId = {}, symbol = {}, liquidationPrice = {}, profit = {}, fee = {}, remainingMargin = {}",
                position.getId().getValue(), position.getSymbol().getValue(),
                liquidationPrice.getValue(), profit.getValue(), liquidationFee.getValue(), remainingMargin.getValue());
    }

    /**
     * Kiểm tra đầu vào cho phương thức thanh lý vị thế
     * @param position Vị thế cần thanh lý
     * @param liquidationPrice Giá thanh lý
     * @param contract Hợp đồng
     */
    private void validateLiquidationInput(Position position, Money liquidationPrice, Contract contract) {
        if (position == null) {
            throw new ValidationException("Position không được để trống");
        }

        if (liquidationPrice == null) {
            throw new ValidationException("LiquidationPrice không được để trống");
        }

        if (contract == null) {
            throw new ValidationException("Contract không được để trống");
        }
    }

    /**
     * Tính toán phí thanh lý một cách an toàn
     * @param position Vị thế cần thanh lý
     * @param liquidationPrice Giá thanh lý
     * @param contract Hợp đồng
     * @return Phí thanh lý
     */
    private Money calculateLiquidationFeeSafely(Position position, Money liquidationPrice, Contract contract) {
        try {
            return calculateLiquidationFee(position, liquidationPrice, contract);
        } catch (Exception e) {
            log.error("Lỗi khi tính toán phí thanh lý, sử dụng giá trị mặc định, positionId = {}",
                    position.getId().getValue(), e);
            return Money.ZERO;
        }
    }

    /**
     * Tính toán lợi nhuận một cách an toàn
     * @param position Vị thế cần thanh lý
     * @param liquidationPrice Giá thanh lý
     * @return Lợi nhuận
     */
    private Money calculateProfitSafely(Position position, Money liquidationPrice) {
        try {
            return positionService.calculateUnrealizedProfit(position, liquidationPrice);
        } catch (Exception e) {
            log.error("Lỗi khi tính toán lợi nhuận, sử dụng giá trị mặc định, positionId = {}",
                    position.getId().getValue(), e);
            return Money.ZERO;
        }
    }

    /**
     * Tạo vị thế đã được thanh lý
     * @param position Vị thế cần thanh lý
     * @param liquidationPrice Giá thanh lý
     * @param profit Lợi nhuận
     * @param remainingMargin Số tiền ký quỹ còn lại
     * @return Vị thế đã được thanh lý
     */
    private Position createLiquidatedPosition(Position position, Money liquidationPrice, Money profit, Money remainingMargin) {
        return Position.builder()
                .id(position.getId())
                .memberId(position.getMemberId())
                .symbol(position.getSymbol())
                .direction(position.getDirection())
                .volume(BigDecimal.ZERO)
                .openPrice(position.getOpenPrice())
                .closePrice(liquidationPrice)
                .liquidationPrice(Money.ZERO)
                .maintenanceMargin(Money.ZERO)
                .margin(Money.ZERO)
                .profit(profit)
                .remainingMargin(remainingMargin)
                .marginMode(position.getMarginMode())
                .leverage(position.getLeverage())
                .createTime(position.getCreateTime())
                .updateTime(LocalDateTime.now())
                .remark("Liquidated at " + liquidationPrice.getValue().toPlainString())
                .status(PositionStatus.LIQUIDATED)
                .build();
    }

    /**
     * Gửi thông báo thanh lý vị thế
     * @param position Vị thế cần thanh lý
     * @param liquidationPrice Giá thanh lý
     * @param remainingMargin Số tiền ký quỹ còn lại
     * @param liquidationType Loại thanh lý
     */
    private void sendLiquidationEvent(Position position, Money liquidationPrice, Money remainingMargin, LiquidationType liquidationType) {
        String reason = "Margin ratio below maintenance margin";
        try {
            liquidationEventProducer.sendLiquidationEvent(
                position,
                liquidationPrice,
                liquidationPrice,
                remainingMargin,
                liquidationType.name(),
                reason
            );
        } catch (Exception e) {
            log.error("Lỗi khi gửi thông báo thanh lý vị thế, positionId = {}, symbol = {}, remainingMargin = {}",
                    position.getId().getValue(), position.getSymbol().getValue(), remainingMargin.getValue(), e);
            // Không ảnh hưởng đến quá trình thanh lý, chỉ ghi log
        }
    }

    /**
     * Xử lý số tiền ký quỹ còn lại và xác định loại thanh lý
     * @param position Vị thế cần thanh lý
     * @param remainingMargin Số tiền ký quỹ còn lại
     * @return Loại thanh lý
     */
    private LiquidationType handleRemainingMargin(Position position, Money remainingMargin) {
        if (remainingMargin.isLessThan(Money.ZERO)) {
            return handleNegativeRemainingMargin(position, remainingMargin);
        } else if (remainingMargin.isGreaterThan(Money.ZERO)) {
            return handlePositiveRemainingMargin(position, remainingMargin);
        } else {
            return LiquidationType.NORMAL;
        }
    }

    /**
     * Xử lý số tiền ký quỹ còn lại âm
     * @param position Vị thế cần thanh lý
     * @param remainingMargin Số tiền ký quỹ còn lại
     * @return Loại thanh lý
     */
    private LiquidationType handleNegativeRemainingMargin(Position position, Money remainingMargin) {
        Money insuranceFundAmount = Money.of(remainingMargin.getValue().abs());
        log.info("Sử dụng quỹ bảo hiểm cho thanh lý, positionId = {}, amount = {}",
                position.getId().getValue(), insuranceFundAmount.getValue());

        // Ghi nợ vào quỹ bảo hiểm
        accountService.debit(
            "INSURANCE_FUND",
            insuranceFundAmount,
            AccountOperationType.INSURANCE_FUND,
            "Insurance fund used for liquidation of position " + position.getId().getValue()
        );

        return LiquidationType.INSURANCE_FUND;
    }

    /**
     * Xử lý số tiền ký quỹ còn lại dương
     * @param position Vị thế cần thanh lý
     * @param remainingMargin Số tiền ký quỹ còn lại
     * @return Loại thanh lý
     */
    private LiquidationType handlePositiveRemainingMargin(Position position, Money remainingMargin) {
        // Hoàn trả số dư còn lại cho người dùng
        accountService.credit(
            position.getMemberId().toString(),
            remainingMargin,
            AccountOperationType.LIQUIDATION_REFUND,
            "Refund from liquidation of position " + position.getId().getValue()
        );

        return LiquidationType.NORMAL;
    }

    /**
     * Ghi nhận vào báo cáo tài chính
     * @param position Vị thế cần thanh lý
     * @param liquidationPrice Giá thanh lý
     * @param profit Lợi nhuận
     * @param liquidationFee Phí thanh lý
     * @param remainingMargin Số tiền ký quỹ còn lại
     */
    private void createFinancialRecord(Position position, Money liquidationPrice, Money profit, Money liquidationFee, Money remainingMargin) {
        try {
            // Log giá trị trước khi validate
            log.debug("Tạo financial record với giá trị: positionId = {}, remainingMargin = {}, liquidationFee = {}, profit = {}",
                    position.getId().getValue(),
                    remainingMargin != null ? remainingMargin.getValue() : null,
                    liquidationFee != null ? liquidationFee.getValue() : null,
                    profit != null ? profit.getValue() : null);

            // Validate và cap các giá trị trước khi tạo FinancialRecord
            Money validatedRemainingMargin = validateFinancialAmount(remainingMargin, "remainingMargin");
            Money validatedLiquidationFee = validateFinancialAmount(liquidationFee, "liquidationFee");
            Money validatedProfit = validateFinancialAmount(profit, "profit");

            // Tạo ID cho FinancialRecord sử dụng SnowflakeIdGenerator với tiền tố "FR-"
            String financialRecordId = snowflakeIdGenerator.nextIdWithPrefix("FR");

            FinancialRecord financialRecord = FinancialRecord.builder()
                    .id(financialRecordId)
                    .memberId(position.getMemberId())
                    .positionId(position.getId())
                    .type(FinancialRecordType.LIQUIDATION)
                    .amount(validatedRemainingMargin)
                    .fee(validatedLiquidationFee)
                    .profit(validatedProfit)
                    .createTime(LocalDateTime.now())
                    .remark("Liquidation at " + liquidationPrice.getValue().toPlainString())
                    .build();

            financialRecordRepository.save(financialRecord);

            log.debug("Đã ghi nhận vào báo cáo tài chính, positionId = {}, memberId = {}, amount = {}, fee = {}, profit = {}",
                    position.getId().getValue(), position.getMemberId(),
                    validatedRemainingMargin.getValue(), validatedLiquidationFee.getValue(), validatedProfit.getValue());
        } catch (Exception e) {
            log.error("Lỗi khi ghi nhận vào báo cáo tài chính, positionId = {}, memberId = {}, " +
                    "remainingMargin = {}, liquidationFee = {}, profit = {}",
                    position.getId().getValue(), position.getMemberId(),
                    remainingMargin != null ? remainingMargin.getValue() : null,
                    liquidationFee != null ? liquidationFee.getValue() : null,
                    profit != null ? profit.getValue() : null, e);
            // Không ảnh hưởng đến quá trình thanh lý, chỉ ghi log
        }
    }

    /**
     * Validate và cap giá trị Money để phù hợp với NUMERIC(18,8)
     * @param money Giá trị Money cần validate
     * @param fieldName Tên field để log
     * @return Money đã được validate
     */
    private Money validateFinancialAmount(Money money, String fieldName) {
        if (money == null) {
            return Money.ZERO;
        }

        // Sử dụng BigDecimalValidator để validate và cap giá trị
        BigDecimal validatedValue = com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator
                .validateAndScale(money.getValue(), fieldName);

        // Nếu giá trị đã được cap, log warning
        if (validatedValue.compareTo(money.getValue()) != 0) {
            log.warn("Financial amount {} for field {} was capped from {} to {}",
                    fieldName, fieldName, money.getValue(), validatedValue);
        }

        return Money.of(validatedValue);
    }

    /**
     * Phục hồi khi thanh lý vị thế thất bại
     * @param e Ngoại lệ
     * @param position Vị thế
     * @param liquidationPrice Giá thanh lý
     * @param contract Hợp đồng
     * @return null
     */
    @Recover
    public Position recoverLiquidatePosition(Exception e, Position position, Money liquidationPrice, Contract contract) {
        String positionId = position != null && position.getId() != null ? position.getId().getValue().toString() : "null";
        String symbol = position != null && position.getSymbol() != null ? position.getSymbol().getValue() : "null";
        Long memberId = position != null ? position.getMemberId() : null;
        String status = position != null && position.getStatus() != null ? position.getStatus().toString() : "null";
        BigDecimal volume = position != null ? position.getVolume() : null;
        String marginValue = position != null && position.getMargin() != null ? position.getMargin().getValue().toString() : "null";
        String liquidationPriceValue = liquidationPrice != null ? liquidationPrice.getValue().toString() : "null";

        log.error("Đã thử lại thanh lý vị thế 3 lần nhưng thất bại, " +
                "positionId = {}, memberId = {}, symbol = {}, status = {}, volume = {}, margin = {}, liquidationPrice = {}, " +
                "errorType = {}, errorMessage = {}",
                positionId, memberId, symbol, status, volume, marginValue, liquidationPriceValue,
                e.getClass().getSimpleName(), e.getMessage(), e);
        return null;
    }
    /**
     * Tính toán phí thanh lý với xử lý ngoại lệ và thử lại
     * @param position Vị thế
     * @param liquidationPrice Giá thanh lý
     * @param contract Hợp đồng
     * @return Phí thanh lý
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Money calculateLiquidationFee(Position position, Money liquidationPrice, Contract contract) {
        try {
            logLiquidationFeeCalculation(position, liquidationPrice);

            // Kiểm tra đầu vào
            validateLiquidationFeeInput(position, liquidationPrice, contract);

            // Tính toán phí thanh lý
            return doCalculateLiquidationFee(position, liquidationPrice, contract);
        } catch (DataAccessException | TransactionException e) {
            logLiquidationFeeError(position, liquidationPrice, e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi tính toán phí thanh lý: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            logLiquidationFeeError(position, liquidationPrice, e);
            return Money.ZERO;
        } catch (Exception e) {
            logLiquidationFeeError(position, liquidationPrice, e);
            throw new BusinessException("Lỗi khi tính toán phí thanh lý: " + e.getMessage(), e);
        }
    }

    /**
     * Ghi log tính toán phí thanh lý
     * @param position Vị thế
     * @param liquidationPrice Giá thanh lý
     */
    private void logLiquidationFeeCalculation(Position position, Money liquidationPrice) {
        log.debug("Tính toán phí thanh lý, positionId = {}, symbol = {}, liquidationPrice = {}",
                position != null && position.getId() != null ? position.getId().getValue() : "null",
                position != null && position.getSymbol() != null ? position.getSymbol().getValue() : "null",
                liquidationPrice != null ? liquidationPrice.getValue() : "null");
    }

    /**
     * Ghi log lỗi tính toán phí thanh lý
     * @param position Vị thế
     * @param liquidationPrice Giá thanh lý
     * @param e Ngoại lệ
     */
    private void logLiquidationFeeError(Position position, Money liquidationPrice, Exception e) {
        log.error("Lỗi khi tính toán phí thanh lý, positionId = {}, liquidationPrice = {}",
                position != null && position.getId() != null ? position.getId().getValue() : "null",
                liquidationPrice != null ? liquidationPrice.getValue() : "null", e);
    }

    /**
     * Kiểm tra đầu vào cho phương thức tính toán phí thanh lý
     * @param position Vị thế
     * @param liquidationPrice Giá thanh lý
     * @param contract Hợp đồng
     */
    private void validateLiquidationFeeInput(Position position, Money liquidationPrice, Contract contract) {
        if (position == null) {
            throw new ValidationException("Position không được để trống");
        }

        if (liquidationPrice == null) {
            throw new ValidationException("LiquidationPrice không được để trống");
        }

        if (contract == null) {
            throw new ValidationException("Contract không được để trống");
        }

        if (position.getVolume() == null) {
            throw new ValidationException("Volume của vị thế không được để trống");
        }
    }

    /**
     * Tính toán phí thanh lý
     * @param position Vị thế
     * @param liquidationPrice Giá thanh lý
     * @param contract Hợp đồng
     * @return Phí thanh lý
     */
    private Money doCalculateLiquidationFee(Position position, Money liquidationPrice, Contract contract) {
        // Validate input values trước khi tính toán
        BigDecimal volume = com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator
                .validateAndScale(position.getVolume(), "volume");
        BigDecimal price = com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator
                .validateAndScale(liquidationPrice.getValue(), "liquidationPrice");

        // Phí thanh lý = Giá trị vị thế * Tỷ lệ phí thanh lý
        BigDecimal positionValue = volume.multiply(price);

        // Validate position value
        positionValue = com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator
                .validateAndScale(positionValue, "positionValue");

        BigDecimal liquidationFeeRate = contract.getLiquidationFeeRate() != null ?
                contract.getLiquidationFeeRate().getValue() : BigDecimal.valueOf(0.005); // 0.5% mặc định

        BigDecimal feeAmount = positionValue.multiply(liquidationFeeRate);

        // Validate fee amount
        feeAmount = com.icetea.lotus.infrastructure.persistence.util.BigDecimalValidator
                .validateAndScale(feeAmount, "liquidationFeeAmount");

        Money liquidationFee = Money.of(feeAmount);

        log.debug("Đã tính toán phí thanh lý thành công, positionId = {}, volume = {}, price = {}, positionValue = {}, feeRate = {}, liquidationFee = {}",
                position.getId().getValue(), volume, price, positionValue, liquidationFeeRate, liquidationFee.getValue());

        return liquidationFee;
    }

    /**
     * Phục hồi khi tính toán phí thanh lý thất bại
     * @param e Ngoại lệ
     * @param position Vị thế
     * @param liquidationPrice Giá thanh lý
     * @param contract Hợp đồng
     * @return Money.ZERO
     */
    @Recover
    public Money recoverCalculateLiquidationFee(Exception e, Position position, Money liquidationPrice, Contract contract) {
        log.error("Đã thử lại tính toán phí thanh lý 3 lần nhưng thất bại, positionId = {}, symbol = {}",
                position != null && position.getId() != null ? position.getId().getValue() : "null",
                position != null && position.getSymbol() != null ? position.getSymbol().getValue() : "null", e);
        return Money.ZERO;
    }

    /**
     * Tính toán số tiền bảo hiểm cần sử dụng với xử lý ngoại lệ và thử lại
     * @param position Vị thế
     * @param liquidationPrice Giá thanh lý
     * @param contract Hợp đồng
     * @return Số tiền bảo hiểm cần sử dụng
     */
    @Override
    @Retryable(
            retryFor = {DataAccessException.class, TransactionException.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public Money calculateInsuranceFundAmount(Position position, Money liquidationPrice, Contract contract) {
        try {
            logInsuranceFundCalculation(position, liquidationPrice);

            // Kiểm tra đầu vào
            validateInsuranceFundInput(position, liquidationPrice, contract);

            // Tính toán số tiền bảo hiểm
            return doCalculateInsuranceFundAmount(position, liquidationPrice, contract);
        } catch (DataAccessException | TransactionException e) {
            logInsuranceFundError(position, liquidationPrice, e);
            throw e;
        } catch (ValidationException e) {
            log.error("Lỗi validation khi tính toán số tiền bảo hiểm cần sử dụng: {}", e.getMessage(), e);
            throw e;
        } catch (ArithmeticException e) {
            logInsuranceFundError(position, liquidationPrice, e);
            return Money.ZERO;
        } catch (Exception e) {
            logInsuranceFundError(position, liquidationPrice, e);
            throw new BusinessException("Lỗi khi tính toán số tiền bảo hiểm cần sử dụng: " + e.getMessage(), e);
        }
    }

    /**
     * Ghi log tính toán số tiền bảo hiểm
     * @param position Vị thế
     * @param liquidationPrice Giá thanh lý
     */
    private void logInsuranceFundCalculation(Position position, Money liquidationPrice) {
        log.debug("Tính toán số tiền bảo hiểm cần sử dụng, positionId = {}, symbol = {}, liquidationPrice = {}",
                position != null && position.getId() != null ? position.getId().getValue() : "null",
                position != null && position.getSymbol() != null ? position.getSymbol().getValue() : "null",
                liquidationPrice != null ? liquidationPrice.getValue() : "null");
    }

    /**
     * Ghi log lỗi tính toán số tiền bảo hiểm
     * @param position Vị thế
     * @param liquidationPrice Giá thanh lý
     * @param e Ngoại lệ
     */
    private void logInsuranceFundError(Position position, Money liquidationPrice, Exception e) {
        log.error("Lỗi khi tính toán số tiền bảo hiểm cần sử dụng, positionId = {}, liquidationPrice = {}",
                position != null && position.getId() != null ? position.getId().getValue() : "null",
                liquidationPrice != null ? liquidationPrice.getValue() : "null", e);
    }

    /**
     * Kiểm tra đầu vào cho phương thức tính toán số tiền bảo hiểm
     * @param position Vị thế
     * @param liquidationPrice Giá thanh lý
     * @param contract Hợp đồng
     */
    private void validateInsuranceFundInput(Position position, Money liquidationPrice, Contract contract) {
        if (position == null) {
            throw new ValidationException("Position không được để trống");
        }

        if (liquidationPrice == null) {
            throw new ValidationException("LiquidationPrice không được để trống");
        }

        if (contract == null) {
            throw new ValidationException("Contract không được để trống");
        }

        if (position.getMargin() == null) {
            throw new ValidationException("Margin của vị thế không được để trống");
        }
    }

    /**
     * Tính toán số tiền bảo hiểm cần sử dụng
     * @param position Vị thế
     * @param liquidationPrice Giá thanh lý
     * @param contract Hợp đồng
     * @return Số tiền bảo hiểm cần sử dụng
     */
    private Money doCalculateInsuranceFundAmount(Position position, Money liquidationPrice, Contract contract) {
        // Sử dụng remainingMargin từ vị thế nếu có
        Money remainingMargin;
        if (position.getRemainingMargin() != null) {
            remainingMargin = position.getRemainingMargin();
            log.debug("Sử dụng remainingMargin từ vị thế, positionId = {}, remainingMargin = {}",
                    position.getId().getValue(), remainingMargin.getValue());
        } else {
            // Tính toán lợi nhuận/lỗ
            Money profit = calculateProfitSafely(position, liquidationPrice);

            // Tính toán phí thanh lý
            Money liquidationFee = calculateLiquidationFeeSafely(position, liquidationPrice, contract);

            // Tính toán số tiền ký quỹ còn lại
            remainingMargin = position.getMargin().add(profit).subtract(liquidationFee);
            log.debug("Tính toán remainingMargin, positionId = {}, remainingMargin = {}",
                    position.getId().getValue(), remainingMargin.getValue());
        }

        Money insuranceFundAmount;

        // Nếu số tiền ký quỹ còn lại âm, cần sử dụng quỹ bảo hiểm
        if (remainingMargin.isLessThan(Money.ZERO)) {
            insuranceFundAmount = Money.of(remainingMargin.getValue().abs());

            // Kiểm tra xem có cần kích hoạt ADL không
            // Giả sử giới hạn sử dụng quỹ bảo hiểm là 20 lần tỷ lệ ký quỹ bảo trì
            Money maxInsuranceFundUsage = Money.of(contract.getMaintenanceMarginRatio()
                .multiply(BigDecimal.valueOf(20)));
            if (insuranceFundAmount.isGreaterThan(maxInsuranceFundUsage)) {
                log.warn("Số tiền bảo hiểm cần sử dụng vượt quá giới hạn, kích hoạt ADL, " +
                        "positionId = {}, insuranceFundAmount = {}, maxInsuranceFundUsage = {}",
                        position.getId().getValue(), insuranceFundAmount.getValue(),
                        maxInsuranceFundUsage.getValue());

                // Kích hoạt ADL
                triggerADL(position, insuranceFundAmount);
            }
        } else {
            insuranceFundAmount = Money.ZERO;
        }

        log.debug("Đã tính toán số tiền bảo hiểm cần sử dụng thành công, positionId = {}, insuranceFundAmount = {}",
                position.getId().getValue(), insuranceFundAmount.getValue());

        return insuranceFundAmount;
    }

    /**
     * Tìm các vị thế có lợi nhuận cao nhất ở hướng ngược lại
     * @param position Vị thế cần thanh lý
     * @param limit Số lượng vị thế tối đa
     * @return Danh sách các vị thế có lợi nhuận
     */
    private List<Position> findProfitablePositionsInOppositeDirection(Position position, int limit) {
        try {
            // Xác định hướng ngược lại
            PositionDirection oppositeDirection = position.getDirection() == PositionDirection.LONG
                    ? PositionDirection.SHORT
                    : PositionDirection.LONG;

            // Tìm các vị thế có lợi nhuận cao nhất ở hướng ngược lại
            return positionService.findProfitablePositionsBySymbolAndDirection(
                    position.getSymbol(), oppositeDirection, limit);
        } catch (Exception e) {
            log.error("Lỗi khi tìm các vị thế có lợi nhuận, positionId = {}", position.getId().getValue(), e);
            return Collections.emptyList();
        }
    }

    /**
     * Tính toán lợi nhuận của vị thế
     * @param position Vị thế
     * @return Lợi nhuận của vị thế
     */
    private Money calculatePositionProfit(Position position) {
        try {
            // Lấy giá đánh dấu hiện tại
            Money markPrice = positionService.getMarkPrice(position.getSymbol());

            // Tính toán lợi nhuận
            return positionService.calculateUnrealizedPnl(position, markPrice);
        } catch (Exception e) {
            log.error("Lỗi khi tính toán lợi nhuận của vị thế, positionId = {}", position.getId().getValue(), e);
            return Money.ZERO;
        }
    }

    /**
     * Giảm đòn bẩy của vị thế
     * @param position Vị thế
     * @param amount Số tiền cần giảm
     */
    private void reducePositionLeverage(Position position, Money amount) {
        try {
            // Tính toán đòn bẩy mới
            BigDecimal currentLeverage = position.getLeverage();
            Money currentMargin = position.getMargin();
            Money newMargin = currentMargin.subtract(amount);

            // Đảm bảo margin mới không nhỏ hơn 0
            if (newMargin.isLessThanOrEqual(Money.ZERO)) {
                newMargin = Money.of(BigDecimal.valueOf(0.01)); // Giá trị tối thiểu
            }

            // Tính toán đòn bẩy mới
            BigDecimal newLeverage = currentLeverage.multiply(currentMargin.getValue())
                    .divide(newMargin.getValue(), 2, RoundingMode.HALF_UP);

            // Cập nhật vị thế
            Position updatedPosition = Position.builder()
                    .id(position.getId())
                    .memberId(position.getMemberId())
                    .symbol(position.getSymbol())
                    .direction(position.getDirection())
                    .volume(position.getVolume())
                    .openPrice(position.getOpenPrice())
                    .closePrice(position.getClosePrice())
                    .liquidationPrice(position.getLiquidationPrice())
                    .margin(newMargin)
                    .leverage(newLeverage)
                    .status(position.getStatus())
                    .createTime(position.getCreateTime())
                    .updateTime(LocalDateTime.now())
                    .remainingMargin(position.getRemainingMargin())
                    .build();

            // Lưu vị thế
            positionService.save(updatedPosition);

            // Tạo bản ghi ADL
            createADLRecord(position, amount);

            log.info("Đã giảm đòn bẩy của vị thế, positionId = {}, oldLeverage = {}, newLeverage = {}, amount = {}",
                    position.getId().getValue(), currentLeverage, newLeverage, amount.getValue());
        } catch (Exception e) {
            log.error("Lỗi khi giảm đòn bẩy của vị thế, positionId = {}", position.getId().getValue(), e);
        }
    }

    /**
     * Tạo bản ghi ADL
     * @param position Vị thế
     * @param amount Số tiền ADL
     */
    private void createADLRecord(Position position, Money amount) {
        try {
            // Tạo bản ghi ADL
            ADLRecord adlRecord = ADLRecord.builder()
                    // Không đặt ID, để cơ sở dữ liệu tự động tạo
                    .positionId(position.getId().getValue())
                    .memberId(position.getMemberId())
                    .symbol(position.getSymbol())
                    .direction(position.getDirection())
                    .volume(Money.of(position.getVolume()))
                    .price(position.getOpenPrice())
                    .time(LocalDateTime.now())
                    .build();

            // Lưu bản ghi ADL
            adlRecordRepository.save(adlRecord);

            log.info("Đã tạo bản ghi ADL, positionId = {}, memberId = {}, amount = {}",
                    position.getId().getValue(), position.getMemberId(), amount.getValue());
        } catch (Exception e) {
            log.error("Lỗi khi tạo bản ghi ADL, positionId = {}", position.getId().getValue(), e);
        }
    }

    /**
     * Kích hoạt ADL (Auto-Deleveraging)
     * @param position Vị thế cần thanh lý
     * @param insuranceFundAmount Số tiền bảo hiểm cần sử dụng
     */
    private void triggerADL(Position position, Money insuranceFundAmount) {
        if (position == null || position.getId() == null || insuranceFundAmount == null) {
            log.warn("Không thể kích hoạt ADL do dữ liệu đầu vào không hợp lệ");
            return;
        }

        try {
            log.info("Đã kích hoạt ADL cho vị thế, positionId = {}, amount = {}",
                    position.getId().getValue(), insuranceFundAmount.getValue());

            // 1. Tìm các vị thế có lợi nhuận cao nhất ở hướng ngược lại
            List<Position> profitablePositions = findProfitablePositionsInOppositeDirection(position, 10);

            if (profitablePositions == null || profitablePositions.isEmpty()) {
                log.warn("Không tìm thấy vị thế có lợi nhuận để thực hiện ADL, positionId = {}",
                        position.getId().getValue());
                return;
            }

            // 2. Giảm đòn bẩy của các vị thế đó và thu hồi tiền
            Money totalRecovered = recoverFundsFromProfitablePositions(profitablePositions, insuranceFundAmount);

            // 3. Sử dụng lợi nhuận từ các vị thế đó để bù đắp cho quỹ bảo hiểm
            creditInsuranceFund(position, totalRecovered);
        } catch (Exception e) {
            log.error("Lỗi khi kích hoạt ADL cho vị thế, positionId = {}",
                    position.getId() != null ? position.getId().getValue() : "null", e);
        }
    }

    /**
     * Thu hồi tiền từ các vị thế có lợi nhuận
     * @param profitablePositions Danh sách các vị thế có lợi nhuận
     * @param insuranceFundAmount Số tiền bảo hiểm cần sử dụng
     * @return Tổng số tiền đã thu hồi
     */
    private Money recoverFundsFromProfitablePositions(List<Position> profitablePositions, Money insuranceFundAmount) {
        Money totalRecovered = Money.ZERO;

        if (profitablePositions == null || profitablePositions.isEmpty() || insuranceFundAmount == null || insuranceFundAmount.isLessThanOrEqual(Money.ZERO)) {
            return totalRecovered;
        }

        // Lọc các vị thế có lợi nhuận dương và xử lý chúng
        List<Position> validPositions = new ArrayList<>();
        for (Position pos : profitablePositions) {
            Money profit = calculatePositionProfit(pos);
            if (profit != null && profit.isGreaterThan(Money.ZERO)) {
                validPositions.add(pos);
            }
        }

        // Sắp xếp theo lợi nhuận giảm dần
        validPositions.sort((p1, p2) -> {
            Money profit1 = calculatePositionProfit(p1);
            Money profit2 = calculatePositionProfit(p2);
            return profit2.getValue().compareTo(profit1.getValue());
        });

        // Xử lý từng vị thế cho đến khi đủ số tiền cần thu hồi
        for (Position profitablePosition : validPositions) {
            if (totalRecovered.isGreaterThanOrEqual(insuranceFundAmount)) {
                break;
            }

            // Tính toán số tiền cần lấy từ vị thế này
            Money positionProfit = calculatePositionProfit(profitablePosition);
            Money remainingNeeded = insuranceFundAmount.subtract(totalRecovered);
            Money amountToTake = Money.of(Math.min(
                    positionProfit.getValue().doubleValue(),
                    remainingNeeded.getValue().doubleValue()
            ));

            // Giảm đòn bẩy của vị thế
            reducePositionLeverage(profitablePosition, amountToTake);

            // Cập nhật tổng số tiền đã thu hồi
            totalRecovered = totalRecovered.add(amountToTake);

            log.info("Đã thực hiện ADL trên vị thế, positionId = {}, memberId = {}, amount = {}",
                    profitablePosition.getId() != null ? profitablePosition.getId().getValue() : "null",
                    profitablePosition.getMemberId(),
                    amountToTake.getValue());
        }

        return totalRecovered;
    }

    /**
     * Ghi có vào quỹ bảo hiểm
     * @param position Vị thế cần thanh lý
     * @param amount Số tiền cần ghi có
     */
    private void creditInsuranceFund(Position position, Money amount) {
        if (position == null || position.getId() == null || amount == null) {
            log.warn("Không thể ghi có vào quỹ bảo hiểm do dữ liệu đầu vào không hợp lệ");
            return;
        }

        if (amount.isGreaterThan(Money.ZERO)) {
            accountService.credit(
                SystemConstants.INSURANCE_FUND_ID,
                amount,
                AccountOperationType.ADL_RECOVERY,
                "ADL recovery for insurance fund from liquidation of position " + position.getId().getValue()
            );

            log.info("Đã bù đắp cho quỹ bảo hiểm từ ADL, amount = {}, positionId = {}",
                    amount.getValue(), position.getId().getValue());
        }
    }

    /**
     * Phục hồi khi tính toán số tiền bảo hiểm cần sử dụng thất bại
     * @param e Ngoại lệ
     * @param position Vị thế
     * @param liquidationPrice Giá thanh lý
     * @param contract Hợp đồng
     * @return Money.ZERO
     */
    @Recover
    public Money recoverCalculateInsuranceFundAmount(Exception e, Position position, Money liquidationPrice, Contract contract) {
        log.error("Đã thử lại tính toán số tiền bảo hiểm cần sử dụng 3 lần nhưng thất bại, positionId = {}, symbol = {}",
                position != null && position.getId() != null ? position.getId().getValue() : "null",
                position != null && position.getSymbol() != null ? position.getSymbol().getValue() : "null", e);
        return Money.ZERO;
    }

    /**
     * Kiểm tra các vị thế cần thanh lý cho một symbol
     * @param symbol Symbol cần kiểm tra
     * @return Danh sách các vị thế cần thanh lý
     */
    public List<Position> checkPositionsForLiquidation(Symbol symbol) {
        // Triển khai phương thức này để sửa lỗi biên dịch
        return Collections.emptyList();
    }
}
