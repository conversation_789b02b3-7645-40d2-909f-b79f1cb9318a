package com.icetea.lotus.controller;

import com.alibaba.fastjson.JSON;
import com.icetea.lotus.config.security.CurrentUser;
import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.dto.AddOrderRequest;
import com.icetea.lotus.entity.ExchangeCoin;
import com.icetea.lotus.entity.ExchangeOrder;
import com.icetea.lotus.entity.ExchangeOrderDetail;
import com.icetea.lotus.entity.ExchangeOrderDirection;
import com.icetea.lotus.entity.ExchangeOrderStatus;
import com.icetea.lotus.entity.ExchangeOrderType;
import com.icetea.lotus.entity.Member;
import com.icetea.lotus.entity.transform.AuthMember;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.service.ExchangeOrderDetailService;
import com.icetea.lotus.service.ExchangeOrderService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.util.MessageResult;
import com.icetea.lotus.validator.AddOrderRequestValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * REST controller for managing exchange orders.
 * This controller provides endpoints to add, retrieve, and cancel orders,
 * as well as check the details of specific orders.
 */
@Slf4j
@RestController
@RequestMapping("/order")
@RequiredArgsConstructor
public class OrderController {
    private final ExchangeOrderService orderService;
    private final ExchangeCoinService exchangeCoinService;
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ExchangeOrderDetailService exchangeOrderDetailService;
    private final MemberService memberService;
    private final RestTemplate restTemplate;
    private final AddOrderRequestValidator requestValidator;
    @Value("${exchange.max-cancel-times:-1}")
    private int maxCancelTimes;

    /**
     * Add a new order
     */
    @RequestMapping("add")
    public MessageResult addOrder(@CurrentUser AuthMember authMember,
                                  ExchangeOrderDirection direction, String symbol, BigDecimal price,
                                  BigDecimal amount, ExchangeOrderType type) {

        // Create request object
        AddOrderRequest orderRequest = AddOrderRequest.builder()
                .direction(direction)
                .symbol(symbol)
                .price(price)
                .amount(amount)
                .type(type)
                .build();

        // Step 1: Perform basic validations
        MessageResult validationResult = requestValidator.performAllValidations(orderRequest, authMember);
        if (validationResult != null) {
            return validationResult;
        }

        // Get necessary data for processing
        ExchangeCoin exchangeCoin = exchangeCoinService.findBySymbol(symbol);
        Member member = memberService.findOne(authMember.getId());
        String baseCoin = exchangeCoin.getBaseSymbol();
        String tradingCoin = exchangeCoin.getCoinSymbol();

        // Step 2: Validate amount and price
        price = price.setScale(exchangeCoin.getBaseCoinScale(), RoundingMode.DOWN);
        MessageResult amountPriceValidation = requestValidator.validateAmountAndPrice(direction, type, price, amount, exchangeCoin);
        if (amountPriceValidation != null) {
            return amountPriceValidation;
        }

        // Step 3: Validate wallets
        MessageResult walletValidation = requestValidator.validateWallets(member.getId(), baseCoin, tradingCoin);
        if (walletValidation != null) {
            return walletValidation;
        }

        // Step 4: Validate price limits and market trading
        MessageResult priceLimitValidation = requestValidator.validatePriceLimits(direction, type, price, exchangeCoin);
        if (priceLimitValidation != null) {
            return priceLimitValidation;
        }

        // Step 5: Validate trading order limits
        long currentTradingCount = orderService.findCurrentTradingCount(member.getId(), symbol, direction);
        if (exchangeCoin.getMaxTradingOrder() > 0 && currentTradingCount >= exchangeCoin.getMaxTradingOrder()) {
            return MessageResult.error(500, "Exceeding the maximum number of pending orders " + exchangeCoin.getMaxTradingOrder());
        }

        // Step 6: Create and submit the order
        ExchangeOrder order = createOrder(member.getId(), symbol, baseCoin, tradingCoin, direction, type, price, amount);

        MessageResult addOrderResult = orderService.addOrder(member.getId(), order);
        if (addOrderResult.getCode() != 0) {
            return MessageResult.error(500, "Failed to submit order:" + addOrderResult.getMessage());
        }

        // Step 7: Send notification and return result
        log.info(">>>>>>>>>>Order submission completed>>>>>>>>>>");
        kafkaTemplate.send("exchange-order", JSON.toJSONString(order));

        MessageResult result = MessageResult.success("success");
        result.setData(order.getOrderId());
        return result;
    }


    /**
     * Create an order with the given parameters
     */
    private ExchangeOrder createOrder(Long memberId, String symbol, String baseCoin, String tradingCoin,
                                      ExchangeOrderDirection direction, ExchangeOrderType type,
                                      BigDecimal price, BigDecimal amount) {
        ExchangeOrder order = new ExchangeOrder();
        order.setMemberId(memberId);
        order.setSymbol(symbol);
        order.setBaseSymbol(baseCoin);
        order.setCoinSymbol(tradingCoin);
        order.setType(type);
        order.setDirection(direction);
        order.setPrice(type == ExchangeOrderType.MARKET_PRICE ? BigDecimal.ZERO : price);
        order.setUseDiscount("0");
        order.setAmount(amount);

        return order;
    }

    /**
     * Historical Commission
     */
    @RequestMapping("history")
    public Page<ExchangeOrder> historyOrder(@CurrentUser AuthMember member, String symbol, int pageNo, int pageSize) {
        return orderService.findHistory(member.getId(), symbol, pageNo, pageSize);
    }

    /**
     * Personal Center Historical Commission
     */
    @RequestMapping("personal/history")
    public Page<ExchangeOrder> personalHistoryOrder(@CurrentUser AuthMember member,
                                                    @RequestParam(value = "symbol", required = false) String symbol,
                                                    @RequestParam(value = "type", required = false) ExchangeOrderType type,
                                                    @RequestParam(value = "status", required = false) ExchangeOrderStatus status,
                                                    @RequestParam(value = "startTime", required = false) String startTime,
                                                    @RequestParam(value = "endTime", required = false) String endTime,
                                                    @RequestParam(value = "direction", required = false) ExchangeOrderDirection direction,
                                                    @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                    @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {

        return orderService.findPersonalHistory(member.getId(), symbol, type, status, startTime, endTime, direction, pageNo, pageSize);
    }


    /**
     * The current commission of the individual center
     */
    @RequestMapping("personal/current")
    public Page<ExchangeOrder> personalCurrentOrder(@CurrentUser AuthMember member,
                                                    @RequestParam(value = "symbol", required = false) String symbol,
                                                    @RequestParam(value = "type", required = false) ExchangeOrderType type,
                                                    @RequestParam(value = "startTime", required = false) String startTime,
                                                    @RequestParam(value = "endTime", required = false) String endTime,
                                                    @RequestParam(value = "direction", required = false) ExchangeOrderDirection direction,
                                                    @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                    @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        Page<ExchangeOrder> page = orderService.findPersonalCurrent(member.getId(), symbol, type, startTime, endTime, direction, pageNo, pageSize);
        return processExchangeOrder(page);
    }

    /**
     * Current delegation
     */
    @RequestMapping("current")
    public Page<ExchangeOrder> currentOrder(@CurrentUser AuthMember member, String symbol, int pageNo, int pageSize) {
        Page<ExchangeOrder> page = orderService.findCurrent(member.getId(), symbol, pageNo, pageSize);
        return processExchangeOrder(page);
    }


    /**
     * Processes a page of exchange orders by enriching each order with its associated details
     * and calculating the total traded amount and turnover.
     *
     * @param page the page of exchange orders to be processed
     * @return the processed page of exchange orders with details, traded amount, and turnover updated
     */
    private Page<ExchangeOrder> processExchangeOrder(Page<ExchangeOrder> page) {
        page.getContent().forEach(exchangeOrder -> {
            // Get transaction details
            BigDecimal tradedAmount = BigDecimal.ZERO;
            BigDecimal turnover = BigDecimal.ZERO;
            List<ExchangeOrderDetail> details = exchangeOrderDetailService.findAllByOrderId(exchangeOrder.getOrderId());
            exchangeOrder.setDetail(details);
            for (ExchangeOrderDetail trade : details) {
                tradedAmount = tradedAmount.add(trade.getAmount());
                turnover = turnover.add(trade.getTurnover());
            }
            exchangeOrder.setTradedAmount(tradedAmount);
            exchangeOrder.setTurnover(turnover);
        });
        return page;
    }

    /**
     * Check the details of the commissioned transaction
     */
    @RequestMapping("detail/{orderId}")
    public List<ExchangeOrderDetail> currentOrder(@CurrentUser AuthMember member, @PathVariable String orderId) {
        return exchangeOrderDetailService.findAllByOrderId(orderId);
    }

    /**
     * Cancel an order
     *
     * @param member  an authenticated member
     * @param orderId the ID of the order to cancel
     * @return a message result indicating success or failure
     */
    @RequestMapping("cancel/{orderId}")
    public MessageResult cancelOrder(@CurrentUser AuthMember member, @PathVariable String orderId) {

        // Step 1: Retrieve the order
        ExchangeOrder order = orderService.findOne(orderId);

        // Step 2: Validate order ownership and status
        MessageResult ownershipValidation = requestValidator.validateOrderOwnership(order, member.getId());
        if (ownershipValidation != null) {
            return ownershipValidation;
        }

        // Step 3: Validate order status
        MessageResult statusValidation = requestValidator.validateOrderStatus(order);
        if (statusValidation != null) {
            return statusValidation;
        }

        // Step 4: Validate cancellation during liquidation period
        MessageResult liquidationValidation = requestValidator.validateLiquidationPeriod(order);
        if (liquidationValidation != null) {
            return liquidationValidation;
        }

        // Step 5: Process the cancellation
        return processCancellation(order, member.getId());
    }


    /**
     * Processes the cancellation of an order
     *
     * @param order    the order to cancel
     * @param memberId the ID of the current user
     * @return a message result indicating success or failure
     */
    private MessageResult processCancellation(ExchangeOrder order, Long memberId) {
        if (isExchangeOrderExist(order)) {
            // Check if the user has exceeded the maximum cancellation limit
            if (maxCancelTimes > 0 &&
                    orderService.findTodayOrderCancelTimes(memberId, order.getSymbol()) >= maxCancelTimes) {
                return MessageResult.error(500, "You have cancelled " + maxCancelTimes + " times today");
            }
            // Send a message to the Exchange system
            kafkaTemplate.send("exchange-order-cancel", JSON.toJSONString(order));
        } else {
            // Forced cancellation
            orderService.forceCancelOrder(order);
        }
        return MessageResult.success("success");
    }

    /**
     * Find out if the order exists in the matchmaking trader
     */
    public boolean isExchangeOrderExist(ExchangeOrder order) {
        try {
            String serviceName = "exchange";
            String url = "http://" + serviceName + "/monitor/order?symbol=" + order.getSymbol() + "&orderId=" + order.getOrderId() + "&direction=" + order.getDirection() + "&type=" + order.getType();
            ResponseEntity<ExchangeOrder> result = restTemplate.getForEntity(url, ExchangeOrder.class);
            return Objects.nonNull(result.getBody());
        } catch (Exception e) {
            log.error("Failed to check if the order exists in the matchmaking trader", e);
            return false;
        }
    }

    /**
     * Get order time limit
     *
     * @return
     */
    @GetMapping("/time_limit")
    public MessageResult userAddExchangeTimeLimit() {
        MessageResult mr = new MessageResult();
        mr.setCode(0);
        mr.setMessage("success");
        mr.setData(SysConstant.USER_ADD_EXCHANGE_ORDER_TIME_LIMIT_EXPIRE_TIME);
        return mr;
    }
}
