package com.icetea.lotus.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.Trader.CoinTrader;
import com.icetea.lotus.Trader.CoinTraderFactory;
import com.icetea.lotus.config.TraderInitializationStatus;
import com.icetea.lotus.entity.ExchangeOrder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class ExchangeOrderConsumer {

    private static final ObjectMapper objectMapper = new ObjectMapper()
            .configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    private final CoinTraderFactory traderFactory;
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final TraderInitializationStatus initializationStatus;
    @Value("${topic-kafka.exchange.order-cancel-success}")
    private String exchangeOrderCancelSuccessTopic;

    @KafkaListener(
            topics = "#{kafkaTopicProvider.getExchangeOrderTopic()}",
            containerFactory = "kafkaListenerContainerFactory")
    public void onOrderSubmitted(List<ConsumerRecord<String, String>> records) {
        // Check if traders are initialized before processing messages
        if (traderEngineIsStarting("Traders not fully initialized yet. Skipping processing of {} orders.", records))
            return;

        for (int i = 0; i < records.size(); i++) {
            ConsumerRecord<String, String> record = records.get(i);
            log.info("Receive orders >> topic={},value={},size={}", record.topic(), record.value(), records.size());
            ExchangeOrder order = null;
            try {
                order = objectMapper.readValue(record.value(), ExchangeOrder.class);
            } catch (JsonProcessingException e) {
                log.error("Error parsing order from JSON", e);
            }
            if (order == null) {
                return;
            }
            CoinTrader trader = traderFactory.getTrader(order.getSymbol());
            // If the current currency transaction is suspended, the order will be automatically canceled
            if (trader.isTradingHalt() || !trader.getReady()) {
                log.info("trading halt or not ready, symbol={}", order.getSymbol());
                // The matchmaker is not ready to be completed, withdraw the currently waiting order
                try {
                    kafkaTemplate.send(exchangeOrderCancelSuccessTopic, objectMapper.writeValueAsString(order));
                } catch (JsonProcessingException e) {
                    log.error("Error serializing order to JSON", e);
                }
            } else {
                try {
                    long startTick = System.currentTimeMillis();
                    trader.trade(order);
                    log.info("complete trade,{}ms used!", System.currentTimeMillis() - startTick);
                } catch (Exception e) {
                    log.info("====Trading error, return order ===", e);
                    try {
                        kafkaTemplate.send(exchangeOrderCancelSuccessTopic, objectMapper.writeValueAsString(order));
                    } catch (JsonProcessingException ex) {
                        log.error("Error serializing order to JSON", ex);
                    }
                }
            }
        }
    }

    @KafkaListener(topics = "#{kafkaTopicProvider.getExchangeOrderCancelTopic()}", containerFactory = "kafkaListenerContainerFactory")
    public void onOrderCancel(List<ConsumerRecord<String, String>> records) {
        // Check if traders are initialized before processing messages

        if (traderEngineIsStarting("Traders not fully initialized yet. Skipping processing of {} cancel orders.", records))
            return;

        for (int i = 0; i < records.size(); i++) {
            ConsumerRecord<String, String> record = records.get(i);
            log.info("Cancel order topic={},key={},size={}", record.topic(), record.key(), records.size());
            ExchangeOrder order = null;
            try {
                order = objectMapper.readValue(record.value(), ExchangeOrder.class);
            } catch (JsonProcessingException e) {
                log.error("Error parsing order from JSON", e);
            }
            if (order == null) {
                return;
            }
            CoinTrader trader = traderFactory.getTrader(order.getSymbol());
            if (trader.getReady()) {
                try {
                    ExchangeOrder result = trader.cancelOrder(order);
                    if (result != null) {
                        try {
                            kafkaTemplate.send(exchangeOrderCancelSuccessTopic, objectMapper.writeValueAsString(result));
                        } catch (JsonProcessingException ex) {
                            log.error("Error serializing result to JSON", ex);
                        }
                    }
                } catch (Exception e) {
                    log.info("====An error occurred when canceling an order===", e);
                }
            }
        }
    }

    private boolean traderEngineIsStarting(String s, List<ConsumerRecord<String, String>> records) {
        if (!initializationStatus.isInitialized()) {
            log.info(s, records.size());
            // Return early without processing any orders
            return true;
        }
        return false;
    }
}
