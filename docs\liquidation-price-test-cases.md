# Liquidation Price Test Cases - Verify Calculation Accuracy

## 🧪 Test Cases để verify công thức Liquidation Price

### **Test Case 1: LONG Position - Standard Case**

#### **Input:**
```
Entry Price: $50,000
Volume: 1 BTC
Leverage: 10x
Maintenance Margin Rate: 0.5%
Direction: LONG
```

#### **Expected Calculation:**
```
Position Value = Entry Price × Volume = $50,000 × 1 = $50,000
Initial Margin = Position Value / Leverage = $50,000 / 10 = $5,000
Maintenance Margin = Position Value × Maintenance Margin Rate = $50,000 × 0.5% = $250

LONG Liquidation Price = (Entry Value - Initial Margin + Maintenance Margin) / Volume
                       = ($50,000 - $5,000 + $250) / 1
                       = $45,250
```

#### **Expected Result:** `$45,250`

### **Test Case 2: SHORT Position - Standard Case**

#### **Input:**
```
Entry Price: $50,000
Volume: 1 BTC
Leverage: 10x
Maintenance Margin Rate: 0.5%
Direction: SHORT
```

#### **Expected Calculation:**
```
Position Value = Entry Price × Volume = $50,000 × 1 = $50,000
Initial Margin = Position Value / Leverage = $50,000 / 10 = $5,000
Maintenance Margin = Position Value × Maintenance Margin Rate = $50,000 × 0.5% = $250

SHORT Liquidation Price = (Entry Value + Initial Margin - Maintenance Margin) / Volume
                        = ($50,000 + $5,000 - $250) / 1
                        = $54,750
```

#### **Expected Result:** `$54,750`

### **Test Case 3: High Leverage LONG Position**

#### **Input:**
```
Entry Price: $50,000
Volume: 0.5 BTC
Leverage: 50x
Maintenance Margin Rate: 1%
Direction: LONG
```

#### **Expected Calculation:**
```
Position Value = $50,000 × 0.5 = $25,000
Initial Margin = $25,000 / 50 = $500
Maintenance Margin = $25,000 × 1% = $250

LONG Liquidation Price = ($25,000 - $500 + $250) / 0.5
                       = $24,750 / 0.5
                       = $49,500
```

#### **Expected Result:** `$49,500`

### **Test Case 4: High Leverage SHORT Position**

#### **Input:**
```
Entry Price: $50,000
Volume: 0.5 BTC
Leverage: 50x
Maintenance Margin Rate: 1%
Direction: SHORT
```

#### **Expected Calculation:**
```
Position Value = $50,000 × 0.5 = $25,000
Initial Margin = $25,000 / 50 = $500
Maintenance Margin = $25,000 × 1% = $250

SHORT Liquidation Price = ($25,000 + $500 - $250) / 0.5
                        = $25,250 / 0.5
                        = $50,500
```

#### **Expected Result:** `$50,500`

### **Test Case 5: Low Leverage LONG Position**

#### **Input:**
```
Entry Price: $50,000
Volume: 2 BTC
Leverage: 2x
Maintenance Margin Rate: 0.25%
Direction: LONG
```

#### **Expected Calculation:**
```
Position Value = $50,000 × 2 = $100,000
Initial Margin = $100,000 / 2 = $50,000
Maintenance Margin = $100,000 × 0.25% = $250

LONG Liquidation Price = ($100,000 - $50,000 + $250) / 2
                       = $50,250 / 2
                       = $25,125
```

#### **Expected Result:** `$25,125`

## 🔧 Test Implementation

### **Unit Test cho Position.calculateLiquidationPrice():**

```java
@Test
public void testLiquidationPriceCalculation_LongPosition() {
    // Given
    Position position = Position.builder()
            .direction(PositionDirection.LONG)
            .openPrice(Money.of(new BigDecimal("50000")))
            .volume(new BigDecimal("1"))
            .margin(Money.of(new BigDecimal("5000"))) // 50000/10
            .build();
    
    BigDecimal maintenanceMarginRate = new BigDecimal("0.005"); // 0.5%
    
    // When
    Money liquidationPrice = position.calculateLiquidationPrice(maintenanceMarginRate);
    
    // Then
    assertEquals(new BigDecimal("45250.00000000"), liquidationPrice.getValue());
}

@Test
public void testLiquidationPriceCalculation_ShortPosition() {
    // Given
    Position position = Position.builder()
            .direction(PositionDirection.SHORT)
            .openPrice(Money.of(new BigDecimal("50000")))
            .volume(new BigDecimal("1"))
            .margin(Money.of(new BigDecimal("5000"))) // 50000/10
            .build();
    
    BigDecimal maintenanceMarginRate = new BigDecimal("0.005"); // 0.5%
    
    // When
    Money liquidationPrice = position.calculateLiquidationPrice(maintenanceMarginRate);
    
    // Then
    assertEquals(new BigDecimal("54750.00000000"), liquidationPrice.getValue());
}
```

### **Integration Test cho API Endpoint:**

```java
@Test
public void testCalculateLiquidationPriceAPI() {
    // Given: Tạo position trong database
    Position position = createTestPosition(
        memberId: 123L,
        symbol: "BTCUSDT",
        direction: PositionDirection.LONG,
        entryPrice: 50000,
        volume: 1,
        leverage: 10
    );
    
    // When: Gọi API
    ResponseEntity<ApiResponse<BigDecimal>> response = restTemplate.getForEntity(
        "/api/v1/liquidation/123/BTCUSDT/liquidation-price",
        new ParameterizedTypeReference<ApiResponse<BigDecimal>>() {}
    );
    
    // Then: Verify response
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertEquals(new BigDecimal("45250"), response.getBody().getData());
}
```

### **WebSocket Test cho Real-time Updates:**

```java
@Test
public void testWebSocketLiquidationPriceUpdates() {
    // Given: Subscribe to position updates
    StompSession session = connectToWebSocket();
    session.subscribe("/user/position/update/BTCUSDT", new PositionUpdateHandler());
    
    // When: Giá thay đổi trigger position update
    priceManagementService.updateMarkPrice(Symbol.of("BTCUSDT"), Money.of(new BigDecimal("45300")));
    
    // Then: Verify WebSocket gửi update với liquidation price đúng
    PositionDto receivedUpdate = waitForWebSocketUpdate();
    assertEquals(new BigDecimal("45250"), receivedUpdate.getLiquidationPrice());
}
```

## 🚨 Common Issues to Check

### **1. Precision Issues:**
- Verify rounding mode (HALF_UP)
- Check decimal places (8 digits)
- Ensure no precision loss in calculations

### **2. Edge Cases:**
- Volume = 0 (should return 0)
- Leverage = 0 (should throw exception)
- Negative prices (should handle gracefully)
- Very high leverage (should not overflow)

### **3. Performance:**
- Batch calculation for multiple positions
- Caching for repeated calculations
- WebSocket update frequency

### **4. Consistency:**
- Same result from all calculation methods
- API vs WebSocket data consistency
- Database vs calculated values

## 🎯 Acceptance Criteria

### **✅ Pass Criteria:**
1. All test cases return expected liquidation prices
2. LONG and SHORT calculations are correct
3. Different leverage levels work properly
4. API endpoints return accurate data
5. WebSocket streams correct real-time updates
6. Performance is acceptable (< 100ms per calculation)

### **❌ Fail Criteria:**
1. Any test case returns wrong liquidation price
2. Inconsistency between calculation methods
3. Performance degradation
4. WebSocket data doesn't match API data
5. Precision errors or rounding issues

## 🔄 Regression Testing

### **Before Fix:**
- Test with old calculation methods
- Document incorrect results
- Identify affected users/positions

### **After Fix:**
- Re-run all test cases
- Verify API responses
- Check WebSocket streams
- Monitor production metrics

**Priority: CRITICAL - Liquidation price accuracy directly affects user funds and risk management!** 🚨
