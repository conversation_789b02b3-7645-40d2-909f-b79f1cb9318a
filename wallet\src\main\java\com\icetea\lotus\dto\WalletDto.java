package com.icetea.lotus.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * DTO cho Wallet
 * Chứa thông tin về ví
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WalletDto {
    private Long id;
    private Long memberId;
    private String coin;
    private BigDecimal balance;
    private BigDecimal frozenBalance;
    private BigDecimal availableBalance;
    private BigDecimal unrealizedPnl;
    private BigDecimal realizedPnl;
    private BigDecimal usedMargin;
    private BigDecimal totalFee;
    private BigDecimal totalFundingFee;
    private Boolean isLock;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
