package com.icetea.lotus.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenApiConfig  {
//    @Bean
//    public Docket apiAdmin() {
//        return new Docket(DocumentationType.SWAGGER_2)
//                .select()
//                .apis(RequestHandlerSelectors.basePackage("com.icetea.lotus.controller"))
//                .paths(PathSelectors.any())
//                .build()
//                .apiInfo(apiInfo());
//    }
//
//    private ApiInfo apiInfo() {
//        return new ApiInfoBuilder().title("ORDER API")
//                .description("API documentation for order-api project")
//                .version("1.0.0")
//                .build();
//    }
    @Bean
    public OpenAPI customOpenAPI(
            @Value("${openapi.service.title}") String title,
            @Value("${openapi.service.version}") String version) {
        return new OpenAPI()
                .info(new Info()
                        .title(title)
                        .version(version)
                        .description("API External documentation"));
    }

    @Bean
    public GroupedOpenApi apiAdmin() {
        return GroupedOpenApi.builder()
                .group("admin")
                .packagesToScan("com.icetea.lotus.controller")
                .build();
    }
}
