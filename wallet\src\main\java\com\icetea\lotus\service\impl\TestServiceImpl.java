package com.icetea.lotus.service.impl;

import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.TestService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.LinkedHashMap;
import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class TestServiceImpl implements TestService {

    private final RestTemplate restTemplate;

    private final CoinService coinService;

    @Value("${rest-template.service-name.custom-rpc}")
    private String customRpc;

    @Override
    public ResponseEntity<MessageResult> getBlockHeight(String unit) {
        // Check required parameters
        if (unit == null || unit.trim().isEmpty()) {
            throw new IllegalArgumentException("Currency unit cannot be null or empty");
        }
        ResponseEntity<MessageResult> result = callRpcService(unit);
        return result;
    }

    @Override
    public LinkedHashMap<String, ResponseEntity<MessageResult>> checkRpcServices() {
        log.info("Retrieving list of coin RPC units");
        List<String> units = coinService.findAllRpcUnit();

        if (units == null || units.isEmpty()) {
            throw new IllegalArgumentException("No RPC coin units available");
        }

        log.info("Checking RPC services for units: {}", units);
        LinkedHashMap<String, ResponseEntity<MessageResult>> results = new LinkedHashMap<>(units.size());

        units.forEach(unit -> {
            ResponseEntity<MessageResult> result = callRpcService(unit);
            results.put(unit, result);
        });

        return results;
    }

    /**
     * Calls the RPC service to retrieve the block height for the specified currency unit.
     *
     * @param unit the currency unit, e.g., BTC, ETH, etc.
     * @return ResponseEntity containing the MessageResult with the block height.
     * @throws RuntimeException if the RPC service call fails.
     */
    private ResponseEntity<MessageResult> callRpcService(String unit) {
        String serviceName = customRpc + unit.toUpperCase();
        String url = "http://" + serviceName + "/rpc/height";
        log.debug("Calling RPC service: {}", url);

        try {
            ResponseEntity<MessageResult> result = restTemplate.getForEntity(url, MessageResult.class);
            log.info("Retrieved block height for unit: {}, result: {}", unit, result);
            return result;
        } catch (Exception e) {
            log.error("Failed to retrieve block height for unit: {}", unit, e);
            throw new RuntimeException("RPC service call failed for unit: " + unit, e);
        }
    }
}