package com.icetea.lotus.walletrpc.bitcoinrpc;

/**
 * Exception thrown when there's an error making a Bitcoin RPC request
 */
public class BitcoinRpcException extends Exception {
    
    /**
     * Constructs a new Bitcoin RPC exception with the specified detail message.
     *
     * @param message the detail message
     */
    public BitcoinRpcException(String message) {
        super(message);
    }
    
    /**
     * Constructs a new Bitcoin RPC exception with the specified detail message and cause.
     *
     * @param message the detail message
     * @param cause the cause
     */
    public BitcoinRpcException(String message, Throwable cause) {
        super(message, cause);
    }
}