package com.icetea.lotus;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.junit.Test;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertEquals;

/**
 * Test to verify that <PERSON> can be configured to ignore unknown properties.
 * This simulates the fix applied to ExchangeOrderConsumer.
 */
public class ExchangeOrderConsumerTest {

    // Simple POJO to test deserialization
    static class TestOrder {
        private String orderId;
        private String symbol;
        private double amount;

        public String getOrderId() { return orderId; }
        public void setOrderId(String orderId) { this.orderId = orderId; }

        public String getSymbol() { return symbol; }
        public void setSymbol(String symbol) { this.symbol = symbol; }

        public double getAmount() { return amount; }
        public void setAmount(double amount) { this.amount = amount; }
    }

    @Test
    public void testJacksonIgnoresUnknownProperties() throws JsonProcessingException {
        // Create ObjectMapper with FAIL_ON_UNKNOWN_PROPERTIES set to false
        ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // JSON with an unknown "completed" field
        String json = "{\"orderId\":\"E174730831674671\",\"symbol\":\"BZB/USDT\",\"amount\":2.00,\"completed\":false}";

        // This should not throw an exception if FAIL_ON_UNKNOWN_PROPERTIES is set to false
        TestOrder order = objectMapper.readValue(json, TestOrder.class);

        // Verify that the order was deserialized correctly
        assertNotNull(order);
        assertEquals("E174730831674671", order.getOrderId());
        assertEquals("BZB/USDT", order.getSymbol());
        assertEquals(2.00, order.getAmount(), 0.001);

        System.out.println("[DEBUG_LOG] Successfully deserialized order with 'completed' field");
    }
}
